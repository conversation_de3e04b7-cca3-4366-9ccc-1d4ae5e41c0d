<?php

namespace app\common\controller;

use app\common\library\Auth;
use think\Config;
use think\Controller;
use think\Hook;
use think\Lang;
use think\Loader;
use think\Validate;
use think\Db;
use app\common\sdk\KuaiDiHd;
use app\common\sdk\Kerrytj;
use fast\Random;
use app\api\controller\LineWebhook;
use app\common\sdk\Alibaba;
use think\Exception;

/**
 * 前台控制器基类
 */
class Frontend extends Controller
{

    /**
     * 布局模板
     * @var string
     */
    protected $layout = '';

    /**
     * 无需登录的方法,同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = [];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = [];

    /**
     * 权限Auth
     * @var Auth
     */
    protected $auth = null;

    protected $ali = null;

    public function _initialize()
    {
        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
        $modulename = $this->request->module();
        $controllername = Loader::parseName($this->request->controller());
        $actionname = strtolower($this->request->action());

        // 检测IP是否允许
        check_ip_allowed();

        // 如果有使用模板布局
        if ($this->layout) {
            $this->view->engine->layout('layout/' . $this->layout);
        }

        $this->auth = Auth::instance();

        $config = Config::get('site.alibaba');
        $this->ali = new Alibaba($config);

        // token
        $token = $this->request->server('HTTP_TOKEN', $this->request->request('token', \think\Cookie::get('token')));

        $path = str_replace('.', '/', $controllername) . '/' . $actionname;
        // 设置当前请求的URI
        $this->auth->setRequestUri($path);
        // 检测是否需要验证登录
        if (!$this->auth->match($this->noNeedLogin)) {
            //初始化
            $this->auth->init($token);
            //检测是否登录
            if (!$this->auth->isLogin()) {
                $this->error(__('Please login first'), 'index/login/login');
            }
            // 判断是否需要验证权限
            if (!$this->auth->match($this->noNeedRight)) {
                // 判断控制器和方法判断是否有对应权限
                if (!$this->auth->check($path)) {
                    $this->error(__('You have no permission'));
                }
            }
        } else {
            // 如果有传递token才验证是否登录状态
            if ($token) {
                $this->auth->init($token);
            }
        }
        
        $this->view->assign('user', $this->auth->getUser());
        $this->view->assign('cnum', $this->counpon_num());
        $this->view->assign('notice', $this->roll_notice());
        $this->view->assign('notice_num', $this->get_notice_num());


        // 语言检测
        $lang = $this->request->langset();
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})\$/i", $lang) ? $lang : 'zh-cn';

        $site = Config::get("site");

        $upload = \app\common\model\Config::upload();
        // 上传信息配置后
        Hook::listen("upload_config_init", $upload);

        // 配置信息
        $config = [
            'site'           => array_intersect_key($site, array_flip(['name', 'cdnurl', 'version', 'timezone', 'languages'])),
            'upload'         => $upload,
            'modulename'     => $modulename,
            'controllername' => $controllername,
            'actionname'     => $actionname,
            'jsname'         => 'frontend/' . str_replace('.', '/', $controllername),
            'moduleurl'      => rtrim(url("/{$modulename}", '', false), '/'),
            'language'       => $lang
        ];
        $config = array_merge($config, Config::get("view_replace_str"));

        Config::set('upload', array_merge(Config::get('upload'), $upload));

        // 配置信息后
        Hook::listen("config_init", $config);
        // 加载当前控制器语言包
        $this->loadlang($controllername);
        $this->assign('site', $site);
        $this->assign('config', $config);
    }

    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        $name = Loader::parseName($name);
        $name = preg_match("/^([a-zA-Z0-9_\.\/]+)\$/i", $name) ? $name : 'index';
        $lang = $this->request->langset();
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})\$/i", $lang) ? $lang : 'zh-cn';
        Lang::load(APP_PATH . $this->request->module() . '/lang/' . $lang . '/' . str_replace('.', '/', $name) . '.php');
    }

    /**
     * 渲染配置信息
     * @param mixed $name  键名或数组
     * @param mixed $value 值
     */
    protected function assignconfig($name, $value = '')
    {
        $this->view->config = array_merge($this->view->config ? $this->view->config : [], is_array($name) ? $name : [$name => $value]);
    }

    /**
     * 刷新Token
     */
    protected function token()
    {
        $token = $this->request->param('__token__');

        //验证Token
        if (!Validate::make()->check(['__token__' => $token], ['__token__' => 'require|token'])) {
            $this->error(__('Token verification error'), '', ['__token__' => $this->request->token()]);
        }

        //刷新Token
        $this->request->token();
    }

    protected function counpon_num()
    {
        if($this->auth->id){
            return $coupon_num = Db::name('user_coupon')->where('user_id', $this->auth->id)->count();
        }
        return 0;
    }

    protected function roll_notice(){

        $row = Db::name('notice')->where('type', 38)
                                ->field('type, title, content, createtime')
                                ->order('id desc')
                                ->find();
        return $row;
    }

    protected function get_notice_num(){

        $row = Db::name('notice')->count();
        return $row;
    }




    /**
	* 	产生导航树
	*	@param	$list  结果集
	*	@param	$pk ID号
	*	@param	$pid	父id
	*	@param	$child	子树字段
	*	@param	$root	
	*	@param	$key	
	*/
    public function list_to_tree($list, $pk = 'id', $pid = 'pid', $child = 'list', $root = 0, $key = '') {
		// 创建Tree
		$tree = array ();
		if (is_array ( $list )) {
			// 创建基于主键的数组引用
			$refer = array ();
			foreach ( $list as $k => $data ) {
				$refer [$data [$pk]] = & $list [$k];
			}
			foreach ( $list as $k => $data ) {
				// 判断是否存在parent
				$parentId = $data [$pid];
				if ($root == $parentId) {
					if ($key != '') {
						$tree [$data [$key]] = & $list [$k];
					} else {
						$tree [] = & $list [$k];
					}
				} else {
					if (isset ( $refer [$parentId] )) {
						$parent = & $refer [$parentId];
						if ($key != '') {
							$parent [$child] [$data [$key]] = & $list [$k];
						} else {
							$parent [$child] [] = & $list [$k];
						}
					}
				}
			}
		}
		return $tree;
	}


    /**
     * 记录分组
     */
    public function list_to_group($idArr, $list, $key){
        $data = [];
        for($i=0;$i<count($idArr);$i++){
            $id = $idArr[$i];
            $item = array_filter($list,  function($row) use($id, $key){
                    return $row[$key] == $id;
            });
            $item = array_values($item);
            $data[] = array($key=>$id, 'list'=>$item) ;
        }   
        return $data;
    }

    public function list_to_group_byid($li, $list, $key){
        $data = [];
        for($i=0;$i<count($li);$i++){
            $id = $li[$i]['id'];
            $item = array_filter($list,  function($row) use($id, $key){
                    return $row[$key] == $id;
            });
            $item = array_values($item);
            $li[$i]['list'] = $item;
            $data[] = $li[$i];
        }   
        return $data;
    }


    public function list_get_group($list, $key, $value){
        $item = array_filter($list,  function($row) use($key, $value){
            return $row[$key] == $value;
        });
        $item = array_values($item);
        return $item;
    }

    public function list_get_line($list, $key, $value){
        for($i=0;$i<count($list);$i++){
            if($list[$i][$key] == $value){
                return $list[$i];
            }
        }
        return null;
    }






    /**
     * 删除二维数组中含有键值的元素
     */
    public function delete_array_key_value($array, $key, $value){
        $filteredArray = array_filter($array, function($item) use ($key, $value) {
            return $item[$key] != $value;
        });
        return $filteredArray;
    }


    /**
     * 获取每组里面字段值最大的数据
     */
    public function list_key_to_max($list, $find_keys, $key, $compare){

        $data = [];
        for($i=0;$i<count($find_keys);$i++){
            $field = $find_keys[$i];
            $items = array_filter($list,  function($row) use($key, $field){
                    return $row[$key] == $field;
            });
            $items = array_values($items);
            
            if(count($items) <= 0){
                continue;
            }
            usort($items, function($a, $b) use($compare) {
                return $b[$compare] <=> $a[$compare]; 
            });
            $data[] = array($key=>$field, 'list'=>$items[0]);;
        }   

        return $data;
    }


    /**
     * 数据融合
     */
    public function list_add_array_record($list, $data, $attribute_name){
        $key = $data['name'];
        $data_array = $data['data'];

        for($i=0;$i<count($list);$i++){
            $id = $list[$i]['id'];
            for($j=0;$j<count($data_array);$j++){
                if( $data_array[$j][$key] != $id ){
                    continue;
                }
                $list[$i][$attribute_name] = $data_array[$j]['list'];
                break;
            }
        }
        return $list;
    }


    /**
     * 获取记录同类型数据
     */
    public function list_to_list($list, $key, $value){
        $data = array_reduce($list, function($carry, $item) use ($key, $value){
            if ($item[$key] == $value) {
                $carry[] = $item;
            }
            return $carry;
        }, []);
        return $data;
    }





    
    /**
     * 钱包日志
     */
    protected function money_log($type, $money, $orderid, $order_tl, $msg="" ){
        if( intval($money) <= 0 ){
            return;
        }
        $res = Frontend::consumption_type($type);
        $user = Db::name('user')->where('id', $this->auth->id)->find();
        if( !$user ){
            return;
        }
        if( $res == true ){
            $price = $user['money'] + $money;
        }else{
            $price = $user['money'] - $money;
        }

        $order = Db::name($order_tl)->where('id', $orderid)->find();
        $data = array(
            'type'          =>$type,
            'order_id'      =>$orderid,
            'order_no'      =>$order['order_no'],
            'order_table'   =>$order_tl,
            'user_id'       =>$user['id'],
            'money'         =>$money,
            'surplus_money' =>$price,
            'rae'           =>$res,
            'msg'           =>$msg,
            'createtime'    =>time(),
            'updatetime'    =>time(),
        );

        $_log = Db::name('wallet_log')->insert($data);
        $_user = Db::name('user')->where('id', $user['id'])->update(['money'=>$price]);

        if( !empty($_log) && !empty($_user) ){
            return true;
        }
        return false;
    }

    /**
     * 1  添加服务     3.  集运订单                             9 系统减少
     * 
     * 2  钱包充值                              8 退款          10 系统增加
     * 
     * @param mixed $type
     * @return bool
     */
    static public function consumption_type($type){
		if($type == '1' || $type == '3' || $type == '9'){
			return false;
		}else{
            return true;
		}
	}

    /**
	*订单日志
	*/
	public function orderLog($msg,$order_id,$table){
		$data = array(
			'order_id' => $order_id,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		/*获取年份*/
		$y = date('Y');
		$table = $table.'_log_'.$y;
		/*检查表是否存在*/
		$t_table =  't_'. $table;

        $query_sql = 'SHOW TABLES LIKE '."'".$t_table."'";
		if(count(db()->query($query_sql))){
			/*存在*/
			Db::name($table)->insert($data);
		}else{
			/*不存在*/
			$sql = 'CREATE TABLE if not exists '.$t_table.' LIKE t_recharge_log';
			Db::query($sql);
			Db::name($table)->insert($data);
		}
	}


    /**
     * 更新订单，并完一些后面操作
     * @param mixed $table
     * @param mixed $order_id
     * @param mixed $pay_status
     * @param mixed $order_status
     * @return bool
     */
    public function order_processing($table, $order_id, $pay_status, $order_status){
        
        $map = array(
            'pay_status'    =>$pay_status,
            'order_status'  =>$order_status
        );
        $_res = Db::name($table)->where('id', $order_id)->update($map);

        if($_res){
            return true;
        }
        return false;
    }


    public  function order_bank_matching($bank_id,$table_name,$order_id,$bank_num){
        $map = [
            'user_id'       =>$this->auth->id,
            'bank_id'       =>$bank_id
        ];

        // //感觉用不到, 先注释
        // $info = Db::name('bank_full_number')->where($map)->find();
		// if($info){
		// 	$number = $info['number'];
		// }else{
		// 	$number = trim($bank_num,',');
		// }

        $number = trim($bank_num,',');
		$data = [
			'user_id'       => $this->auth->id,
			'order_id'      => $order_id,
			'number'        => $number,
			'order_table'   => $table_name,
			'bank_id'       => $bank_id,
		];
	
		if( Db::name('order_bank_matching')->insert($data) )
        {
            return true;
        }
        return false;
    }



    /* @mobile 手机号
	@code 验证码
	@type 类型 */
	public function ruleCode($mobile,$code,$type){
		$map = array(
			'mobile' => $mobile,
			'code' => $code,
			'type' => $type,
		);
		$list = Db::name('code')
				->field('id')
				->where($map)
				->select();
		if($list){
			return true;
		}else{
			return false;
		}
	}

    /**
	@mobile 手机号
	@code 验证码
	@type 类型 
	*/
	public function tips($type){
		switch($type){
			case 0:
				return '簡訊驗證碼錯誤';
			break;
			default:
				return '簡訊驗證碼錯誤';
		}
	}


    /* 
	*@type 类型 0注册 1忘记密码 2使用淘币验证 3修改密码 4添加银行验证 8双重验证
	*/
	public function sendCode($type, $num=6){
		$mobile = $this->request->post('mobile')?$this->request->post('mobile'):$this->auth->mobile;
		$count = Db::name('code')
				->where('mobile', $mobile)
				->count();

		if($count >= '50'){
			return $this->error(__('Your message has reached the limit'));
		}
		$item = Db::name('user')
				->field('id')
				->where('mobile', $mobile)
				->find();


		if($type == '0'){
			if($item){
				return $this->error(__('Mobile number already exists'));
			}
		}
		if($type == '1'){
			if(!$item){
				return $this->error(__('Mobile phone number is not registered yet. Please register first'));
			}
		}

        $code = $code = Random::numeric($num);
        
        $map = $data = array(
			'mobile'        => $mobile,
			'code'          => $code,
			'type'          => $type,
		);

		unset($map['code']);
		Db::name('code')
			->where($map)
			->delete();
		// $sms = new SMSHttp();
		/* 0注册 1忘记密码 2使用淘币验证 3修改密码 4添加银行验证 */
		$msg_txt = $this->msg_txt($type);

		$content = str_replace('#code#',$code,$msg_txt['title']);
        $data['createtime'] = time();
		$result = Db::name('code')->insert($data);

		if(is_numeric($result)){
			$res = $this->sendSMS($mobile,$content);
			if($res == true){
				
				$this->success(__('Verification code has been sent to your mobile phone'));
			}else{
				$this->error(__('Verification code acquisition failed, please refresh and try again'));
			}
		}else{
			$this->error(__('Verification code acquisition failed, please refresh and try again'));
		}
	}


    /**
	* 短信内容
	*/
	public function msg_txt($type){
		$data = array(
			/* 註冊賬號 */
			'0' => array(
				'title' => '【集運通】尊敬的用戶，您的註冊驗證碼為：#code#，請不要向任何人透露此驗證碼。',
				'type' => '註冊賬號',
			),
			/*登录密码*/
			'1' => array(
				'title' => '【集運通】尊敬的用戶，您的登錄密碼為：#code#，請不要向任何人透露此驗證碼。',
				'type' => '密碼登錄',
			),
			/* 支付驗證 */
			'2' => array(
				'title' => '【集運通】您支付的短信驗證為：#code#，請勿將驗證碼碼告知他人',
				'type' => '支付驗證',
			),
			
            
			/* 付款方式 超商 */
			// '1' => '【集運通】R幣儲值，7-11/全家/萊爾富/OK超商繳費代碼：LLL18290658276，金額5980TWD，請在24小時內完成繳費',
			
			/* 訂單已取消/關閉發送 */
			// '3' => '【集運通】您的代付鏈接因超時或被賣家關閉，代付失敗，請登入會員中心關閉訂單，重新申請代付，如有疑問請提出申訴。',
			/* 不能代付（您不能未此交易做代付）發送的簡訊內容 */
			// '4' => '【集運通】您的代付金額被賣家修改了，代付失敗，已經幫您退款到淘幣，請登入會員中心重新提交訂單。',
			/* 提示警示 */
			// '5' => '【集運通】淘寶阻止了我們為您代付，可能因為您在購買虛擬物品或近期淘寶不允許的交易。金額XXX已幫您退款淘幣，請重新申請代付。',
			/* 使用了錯誤的代付賬號 */
			// '6' => '【集運通】您創建鏈接使用的朋友帳號有誤，代付失敗，已經幫您退款淘幣，請重新申請代付，如有疑問請提出申訴。 ',
			/* 代付訂單遇到退款發送*/
			// '7' => '【集運通】您的訂單金額退款$money$ 已幫您退款到淘幣賬戶中~請您注意查收~如有問題請您提交申訴',
			/* 修改手机号 */
			// '8' => '【集運通】尊敬的用戶，您正嘗試修改手機號，如果您並未進行此操作，請忽略此簡訊，驗證碼：#code#',
			
			
			
			/*身份证审核失败*/
			// '12' => '【集運通】您的審核未通過，請您參照我們平台驗證處示例圖樣本上傳喔，感謝您LINE:@etao',
			/*身份证审核成功*/
			// '13' => '【集運通】審核已通過，請您在日後使用時用您提交審核的銀行賬戶轉賬哦~感謝您，LINE:@etao',
		);
		return $data[$type];
	}


    /**
	* 发送短信
	*/
	public function sendSMS ($mobile, $content){
		$url = 'http://smsb2c.mitake.com.tw/b2c/mtk/SmSend?';
		$url .= '&username=0966623633';
		$url .= '&password=686868';
		$url .= '&dstaddr='.$mobile;
		$url .= '&smbody='.urlencode($content);
		$url .= '&CharsetURL=UTF-8';
		// echo $url;exit;
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		$output = curl_exec($curl);
		curl_close($curl);
		if(count(explode('statuscode=1',$output)) > 1){
			return true;
		}else{
			return false;
		}
	}


    /**
     * 条件查询数据库表记录
     */
    public function query_record($table, $map, $field='*', $order=array()){
        $row = Db::name($table)->where($map)->field($field)->order($order)->find();
        return $row;
    }

    /**
     * 查询多条记录
     */
    public function query_records($table, $map, $field='*', $order=array()){
        $rows = Db::name($table)->where($map)->field($field)->order($order)->select();
        return $rows;
    }

    public function records_count($table, $map){
        $count = Db::name($table)->where($map)->count();
        return $count; 
    }

    /**
     * 插入记录并返回ID
     */
    public function getid_insert_record($table, $map){
        $id = Db::name($table)->insertGetId($map);
        return $id;
    }


    public function insert_array_record($table, $data){
        $res = Db::name($table)->insertAll($data);
        return $res; 
    }

    public function update_record($table, $map, $data){
        $res = Db::name($table)->where($map)->update($data);
        if( !$res ){
            return false;
        }
        return true;
    }

    public function delete_record($table, $map){
        $res = Db::name($table)->where($map)->delete();
        if( !$res ){
            return false;
        }
        return true;
    }


    /**
     * 字段值增加
     */
    public function add_field_value($table, $map, $field, $value){
        $res = Db::name($table)->where($map)->setInc($field, $value);
        if( !$res ){
            return false;
        }
        return true;

    }



    /**
     * 获取记录字段值
     */
    public function get_field_value($table, $map, $field){
        $value = Db::name($table)->where($map)->value($field);
        return $value;
    }




    /**
     * 查询快递100  物流的信息
     * @param mixed $com    物流公司简写，如 申通快递 - shengtong
     * @param mixed $num    快递单号
     * @return void
     */
    public function kuaidi_hd_query($uid, $pid, $com, $num, $phone=''){
        $map = array(
            'user_id'      =>$uid,
            'num'          =>$num
        );
        $row = $this->query_record('kuaidi_query', $map);
        if( !$row ){
            $config = Config::get('site.kuaidi100');
            $kuaidi = new KuaiDiHd($config);
            $res = $kuaidi->query_do($com, $num,$phone);
            //  {"result":false,"returnCode":"500","message":"查询无结果，请隔段时间再查"}
            $json_data = json_decode($res, true);
            // 有没有都要把下面记录插进去，免得用户无限查，无限调用

            $id = $this->getid_insert_record('kuaidi_query', array('user_id'=>$uid,'pid'=>$pid,'com'=>$com,'num'=>$num,'updatetime'=>time(),'createtime'=>time()));
            if( isset($json_data['result']) ){
                return array('code'=>'1', 'message'=>$json_data['message']);
            }else{
                if($id <= 0){
                    return array('code'=>'1', 'message'=>'插入失败');
                }
                $insert_data = [];
                $data = $json_data['data'];
                if( count($data) < 0 ){
                    return array('code'=>'1', 'message'=>'还没有任何记录');
                }
                $data = array_reverse($data);
                for($i=0;$i<count($data);$i++){
                    $insert_data[] = array('kid'=>$id, 'time'=>$data[$i]['time'], 'context'=>$data[$i]['context'], 'ftime'=>$data[$i]['ftime'], 'areaCode'=>$data[$i]['areaCode'], 'areaName'=>$data[$i]['areaName'], 'status'=>$data[$i]['status']);
                }
                $this->insert_array_record('kuaidi_log', $insert_data);
                return array('code'=>'0', 'data'=>$data);
            }
        }

        $updatetime = $row['updatetime'];
        if( $row['ct'] > 5 &&  $updatetime < ($updatetime+60*5) ){
            return array('code'=>'1', '訪問連續失敗，休息幾分鍾再來');
        }else if( $row['ct'] > 5 &&  $updatetime > ($updatetime+60*5) ){
            $this->update_record('kuaidi_query', ['id'=>$row['id']], ['ct'=>0]);
        }
        
        $rows = $this->query_records('kuaidi_log', array('kid'=>$row['id']), 'time,context,ftime,areaCode,areaName,status', array('id'=>'DESC'));
        if( $updatetime < ($updatetime+60*60) && count($rows)>0 ){
            return array('code'=>'0', 'data'=>$rows);
        }

        $config = Config::get('site.kuaidi100');
        $kuaidi = new KuaiDiHd($config);
        $res = $kuaidi->query_do($com, $num);
        //  {"result":false,"returnCode":"500","message":"查询无结果，请隔段时间再查"}
        $json_data = json_decode($res, true);
        if( isset($json_data['result']) ){
            $this->add_field_value('kuaidi_query', array('id'=>$row['id']), 'ct', 1);
            return array('code'=>'1', 'message'=>$json_data['message']);
        }

        $data = $json_data['data'];
        $data = array_reverse($data);
        $index = count($rows);
        $insert_data = array_slice($data, $index, count($data)-$index);
        
        // 不知道能不能直接插入上面的数据   ？？？？
        $this->insert_array_record('kuaidi_log', $insert_data);
        $this->update_record('kuaidi_query', ['id'=>$row['id']], ['ct'=>0,'updatetime'=>time()]);

        // 临时启用，查看查询次数
        $this->add_field_value('kuaidi_query', array('id'=>$row['id']), 'js', 1);
        return array('code'=>'0', 'data'=>$data);
    }






    public function tw_tcat_query($com, $num, $phone=''){

        $config = Config::get('site.kuaidi100');
        $kuaidi = new KuaiDiHd($config);
        $res = $kuaidi->query_do($com, $num);
        //  {"result":false,"returnCode":"500","message":"查询无结果，请隔段时间再查"}
        return $res;
    }


    public function tw_hct_query($num){
        //http://47.236.98.171:5000/query?name=hct&number=1390701723

        $url = "http://47.236.98.171:5000/query?name=hct&number=".$num;
        $http = curl_init();
        curl_setopt($http, CURLOPT_URL, $url);
        curl_setopt($http, CURLOPT_RETURNTRANSFER, true);
        $res = curl_exec($http);
        curl_close($http);
        $res = json_decode($res, true);
        $res = json_encode($res, JSON_UNESCAPED_UNICODE);
        return $res;
    }


    public function tw_ktj_query($num){

        //$bln = ["40491720975"];
        $bln = [];
        $bln[] = $num;
        $kj = new Kerrytj();
        $res = $kj->tracing($bln);
        return $res;
    }






    /**
     * Line 集运订单信息
     * @param mixed $order
     * @return string
     */
    public function webhook_jy_order($orderid){
        $to = $this->get_field_value('user',array('id' =>$this->auth->id), 'lineid');
        if( empty($to) ){
            return false;
        }

        $order = $this->query_record('jyorder', array('id'=> $orderid));
        if( !$order ){
            return false;
        }

        $list = $this->query_records('package', array('order_id'=> $order['id']), 'goods_name, scale');
        $line = new LineWebhook();

        $content = $line->jy_good_list($list);
        $pay_status = Config::get('site.pay_status');
        $order_data = [
            'create_time'       =>date("Y-m-d H:i:s", $order['createtime']),
            'order_no'          =>$order['order_no'],
            'contents'          =>$content,
            'order_total'       =>'NT$ ' . $order['tb_money'],
            'pay_status'        =>$pay_status[$order['pay_status']],
            'redirect_url'      =>'https://linecorp.com'
        ];

        $line->jy_order($order_data, $to);
        return true;
    }











    public function line_bang(){
        $user = $this->query_record('user', array('id'=>$this->auth->id));
        if( empty($user['lineid']) ){
            return false;
        }
        return true;
    }


    public function line_bot(){
        $user = $this->query_record('user', array('id'=>$this->auth->id));
        if( empty($user['lineid']) ){
            return false;
        }
        if( $user['bot_friend'] == 1 ){
            return true;
        }
        $line = new LineWebhook();
        $bot = $line->botIsFriend($user['lineid']);
        if( $bot ){
            $this->update_record('user', array('id'=>$this->auth->id), array('bot_friend'=>1));
            return true; 
        }
        return false;
    }

    public function line_field($field){
        $value = $this->get_field_value('user', array('id'=>$this->auth->id), $field);
        return $value;
    }


    public function aliImageProxy($url)
    {
        if (empty($url)) {
            return $this->error('图片URL不能为空');
        }

        // 验证URL是否为阿里巴巴域名
        if (!preg_match('/alicdn\.com|alibaba\.com/', $url)) {
            return $this->error('只允许访问阿里巴巴图片');
        }

        try {
            // 设置请求头，模拟正常浏览器访问
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer: https://www.alibaba.com/',
                'Accept: image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding: gzip, deflate, br',
                'Connection: keep-alive',
                'Upgrade-Insecure-Requests: 1'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $imageData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200 && $imageData) {
                // 设置响应头
                header('Content-Type: image/jpeg');
                header('Cache-Control: public, max-age=86400');
                header('Access-Control-Allow-Origin: *');
                echo $imageData;
            } else {
                return $this->error('图片获取失败');
            }

        } catch (Exception $e) {
            return $this->error('图片代理错误：' . $e->getMessage());
        }
    }



    /**
	* 获取订单列表
	*/
	public static function getOrderList($map, $num, $table){
		return Db::name($table)
			->field('*')
			->order('id','desc')
			->where($map)
			->paginate($num);
	}


    public static function getIdByLink($link){
        if(empty($link)){
            return null;
        }
        $id = null;
        if(strpos($link, "detail.1688.com") !== false){
            $beg = strpos($link, "offer/");
            $end = strpos($link, ".html");
            $id = substr($link, $beg+6, $end-6-$beg);
        }
        return $id;
    }







}
