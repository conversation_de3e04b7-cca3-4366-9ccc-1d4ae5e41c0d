<?php

//https://open.1688.com/solution/solutionDetail.htm?solutionKey=1697014160788#apiAndMessageList
//https://open.1688.com/develop/app/list

namespace   app\common\sdk;
use think\Exception;


class Alibaba{
    private $app_key;
    private $app_secret;
    private $app_token;

    private $url_pre = "https://gw.open.1688.com/openapi/";

    public function __construct($config) {
        $this->app_key = $config["key"];
        $this->app_secret = $config["secret"];
        $this->app_token = $config["token"];
    }


    private function sign($path, $params) {
        $aliParams = array();
        foreach ($params as $key => $val) {
            $aliParams[] = $key . $val;
        }
        sort($aliParams);
        $sign_str = $path . join('', $aliParams);
        $code_sign = strtoupper(bin2hex(hash_hmac("sha1", $sign_str, $this->app_secret, true)));
        return $code_sign;
    }

    private function post_data($url, $params) {
        $http = curl_init($url);
        curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($http, CURLOPT_POST, 1);
        curl_setopt($http, CURLOPT_POSTFIELDS, http_build_query($params));
        $result = curl_exec($http);
        curl_close($http);
        return $result;
    }


    private function get_data($url) {
        $http = curl_init();
        curl_setopt($http, CURLOPT_URL, $url);
        curl_setopt($http, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($http);
        curl_close($http);
        return $result;
    }


    /**
     * 商品热搜词       测试API使用
     * @param mixed $source_id  查询ID
     * @param mixed $country    语言
     * @param mixed $send       发送方式
     * @return void
     */
    // public function topKeyword($source_id, $country="en", $send="post") {
    //     $url_path = 'param2/1/com.alibaba.fenxiao.crossborder/product.search.topKeyword/';
    //     $param_arr = array(
    //         "access_token" => $this->app_token,
    //         "topSeKeywordParam" => json_encode(array("country"=>$country,"sourceId"=>$source_id,"hotKeywordType"=>"cate") )
    //     );

    //     $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
    //     if($send == "post"){
    //         $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
    //         $result = $this->post_data($url, $param_arr);
    //     }else{
    //         $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query($param_arr) . "&_aop_signature=" . $code_sign;
    //         echo $url;
    //         echo "</br>";
    //         echo "</br>";
    //         echo "</br>";
    //         $result = $this->get_data($url);
    //     }
    //     $data = json_encode($result, JSON_UNESCAPED_UNICODE);
    //     return $data;
    // }

    /**
     * 多语言关键词搜索
     * @param mixed $keyword
     * @param mixed $page
     * @param mixed $size
     * @param mixed $filter  筛选条件如 shipInToday(当日发货)  JPFL(排除日本不宜销售商品) 参数值为官方设定,可多选，英文逗号隔开
     * @return bool|string
     */
    public function keywordQuery($keyword, $page, $size, $prices="", $pricee="", $filter="") {
        $url_path = 'param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordQuery/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "offerQueryParam" => json_encode(
                array("keyword"=>$keyword,
                             "beginPage"=>$page,
                             "pageSize"=>$size,
                             "country"=>"en",
                             "priceStart"=>$prices,
                             "priceEnd"=>$pricee,
                             "filter" => $filter
            ))
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }

    /**
     * 多语言商详
     * @param mixed $offerId  商品ID
     * @return bool|string
     */
    public function queryProductDetail($offerId) {
        $url_path = 'param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "offerDetailParam" => json_encode(
                array("offerId"=>$offerId,
                             "country" => "en"
            ))
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }

    /**
     * 获取收货地址列表
     * @return bool|string
     */
    public function getReceiveAddress(){

        $url_path = 'param2/1/com.alibaba.trade/alibaba.trade.receiveAddress.get/';
        $param_arr = array(
            "access_token" => $this->app_token
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;

    }


    /**
     * 跨境订单创建
     * @param mixed $address_param  -->addressId  地址列表ID
     * @param mixed $cargoParam_list  -->offerId  商品ID  specId  规格ID(购买的商品)  quantity  数量
     * @param mixed $flow
     * @param mixed $message
     * @return bool|string
     */
    public function createCrossOrder($address_param, $cargoParam_list, $flow='general', $message=''){

        $url_path = 'param2/1/com.alibaba.trade/alibaba.trade.createCrossOrder/';
        $list = [];
        for($i=0; $i<count($cargoParam_list); $i++){
            $list[] = array(
                "offerId"       =>$cargoParam_list[$i]['offerId'],
                "specId"        =>$cargoParam_list[$i]['specId'],
                "quantity"      =>$cargoParam_list[$i]['quantity'],
            );
        }
        $param_arr = array(
            "access_token"      =>$this->app_token,
            "flow"              =>$flow,
            "message"           =>$message,
            'addressParam'      =>json_encode(array(
                                            "addressId"     =>$address_param['addressId']                   
                                )),
            'cargoParamList'    =>json_encode($list, JSON_UNESCAPED_UNICODE),

        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }

    /**
     * 创建订单前预览数据接口
     * @param mixed $address_param
     * @param mixed $cargoParam_list
     * @param mixed $flow
     * @param mixed $message
     * @return bool|string
     */
    public function createOrderPreview($address_param, $cargoParam_list, $flow='general', $message=''){

        $url_path = 'param2/1/com.alibaba.trade/alibaba.createOrder.preview/';
        $list = [];
        for($i=0; $i<count($cargoParam_list); $i++){
            $list[] = array(
                "offerId"       =>$cargoParam_list[$i]['offerId'],
                "specId"        =>$cargoParam_list[$i]['specId'],
                "quantity"      =>$cargoParam_list[$i]['quantity'],
            );
        }
        $param_arr = array(
            "access_token"      =>$this->app_token,
            "flow"              =>$flow,
            "message"           =>$message,
            'addressParam'      =>json_encode(array(
                                            "addressId"     =>$address_param['addressId']                   
                                )),
            'cargoParamList'    =>json_encode($list, JSON_UNESCAPED_UNICODE),

        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }


    /**
     *  获取订单详情
     */
    public function orderDetails($orderId, $webSite='alibaba'){
        $url_path = 'param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "webSite" => $webSite,
            "orderId" => $orderId
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }



    /**
     * 获取交易订单的物流信息(买家视角)
     */
    public function getLogisticsInfos($orderId, $webSite='alibaba'){
        $url_path = 'param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsInfos.buyerView/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "webSite" => $webSite,
            "orderId" => $orderId
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }


    /**
     * 获取交易订单的物流跟踪信息(买家视角)
     * @param mixed $orderId
     * @return bool|string
     */
    public function getLogisticsTraceInfo($orderId, $webSite='alibaba'){
        $url_path = 'param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsTraceInfo.buyerView/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "webSite" => $webSite,
            "orderId" => $orderId
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }


    /**
     * 取消交易--> 代付关闭
     */
    public function cancelOrder($orderId, $webSite='alibaba', $cancelReason='other'){
        $url_path = 'param2/1/com.alibaba.trade/alibaba.trade.cancel/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "webSite" => $webSite,
            "tradeID" => $orderId,
            "cancelReason" => $cancelReason
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }

    /**
     * 买家删除已关闭的订单
     */
    public function deleteClosedOrder($orderId){
        $url_path = 'param2/1/com.alibaba.trade/trade.order.buyerdelete/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "orderId" => $orderId,
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }
    
    /** "en"
     * 多语言类目查询接口。根据当前语种和类目ID查询对应语种的类目详情，包含当前类目的下级类目列表数据(一二级类目返回下级类目表，三级没有)  
     * @param mixed $categoryId  类目ID，传入的级别官方自动识别
     * @return bool|string
     */
    public function getById($categoryId, $language) {
        $url_path = 'param2/1/com.alibaba.fenxiao.crossborder/category.translation.getById/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "language" => $language,
            "categoryId" => $categoryId
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }

    
    /**
     * Openuid转换解密为旺旺昵称接口
     * @param mixed $openuid    sellerOpenId
     * @return bool|string      {"wangwangNick":"宝泉针织"}
     */
    public function openuidDecrypt($openuid){
        $url_path = 'param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/';
        $milliseconds = round(microtime(true) * 1000);
        $param_arr = array(
            "access_token" => $this->app_token,
            "openUid" => $openuid,
            '_aop_timestamp'=>$milliseconds
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        $result = json_decode($result, true);
        $nick = $result['wangwangNick'];
        return $nick;
    }


    /**
     * 唤起阿里旺旺
     * @param mixed $openuid
     * @return bool|string
     */
    public function wangwangUrl($openuid){
        $url_path = 'param2/1/com.alibaba.account/account.wangwangUrl.get/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "toOpenUid" => $openuid,
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }




    public function alipayUrlGet($orderlist){
        $url_path = 'param2/1/com.alibaba.trade/alibaba.alipay.url.get/';
        $param_arr = array(
            "access_token" => $this->app_token,
            "orderIdList" => json_encode($orderlist, JSON_UNESCAPED_UNICODE),
        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }





    /**
     * 支付
     * @param mixed $orderId
     * @return bool|string
     */
    public function preparePay($orderId, $payAmount=0){
        $url_path = 'param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.preparePay/';

        $param = array();
        $param['orderId'] = $orderId;
        if( $payAmount > 0 ){
            $param['payAmount'] = $payAmount;
        }

        $param_arr = array(
            "access_token" => $this->app_token,
            "tradeWithholdPreparePayParam" => json_encode($param)

        );
        $code_sign = strtoupper($this->sign($url_path . $this->app_key, $param_arr));
        $url = $this->url_pre . $url_path . $this->app_key . "?" . http_build_query(array("_aop_signature"=> $code_sign));
        $result = $this->post_data($url, $param_arr);
        return $result;
    }



    /**
     * 工具函数
     * @param mixed $dateStr
     * @return bool|int
     */
    public function aliTimeToTimestamp($dateStr){
        //$dateStr = '20250726163510000+0800';
        $dateStr = substr($dateStr, 0, 14);
        $formatted = date('Y-m-d H:i:s', strtotime($dateStr));
        $timestamp = strtotime($formatted);
        return $timestamp;
    }

}