<?php

namespace   app\common\sdk;
use think\Exception;


class Kerrytj{
    private $account = "ShiDuoTungYun";
    private $password = "3R4cX9Af";

    public function __construct() {
    }

    private function basic_auth(){
        $info_Str = $this->account.":".$this->password;
        $basic = base64_encode($info_Str);
        return $basic;
    }


    private function post_data($url, $params, $header) {
        $http = curl_init($url);
        curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($http, CURLOPT_POST, 1);
        curl_setopt($http, CURLOPT_HTTPHEADER, $header);
        curl_setopt($http, CURLOPT_POSTFIELDS, $params);
        $result = curl_exec($http);
        curl_close($http);
        return $result;
    }



    
    public function tracing($bln){
        $url = 'https://KerryTracingEDI.kerrytj.com/api/Tracing/BLNListTracing';        
        
        $basic = $this->basic_auth();
        $header = array(
                'Content-Type: application/json',
                'Authorization: Basic '. $basic,
                'Cache-Control: no-cache',
        );

        $param = [];
        for( $i=0;$i<count($bln);$i++ ){
            $param[] = array('BLN'=>$bln[$i]);  
        }
        $param = json_encode($param,JSON_UNESCAPED_UNICODE);
        $result = $this->post_data($url, $param, $header);
        return $result;
    }



    




}