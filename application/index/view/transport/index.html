{include file="common/resources" /}
<!-- 添加CSRF token -->
{:token()}

<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="application">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <div class="transportation-page relative">
            <div class="absolute fw fs16" style="top: 0; left: 0; color: #3D3D3D">倉庫地址</div>
            <div class="custom-tabs">
                <!-- 标签导航 -->
                <div class="tab-header">
                    <div :class="{ 'active': activeIndex === index }" :key="index"
                        @click="changeTabs(activeIndex = index)" class="tab-item pointer fs16"
                        v-for="(item, index) in tabList">
                        {{ item }}
                    </div>
                </div>
                <!-- 内容区域 -->
                <div class="tab-content">
                    <div class="tab-user-info">
                        <el-table :data="activeUserList" style="width: 100%" class="custom-header">
                            <el-table-column label="聯絡人" prop="username" width="120">
                            </el-table-column>
                            <el-table-column label="聯繫電話" prop="mobile" width="180">
                            </el-table-column>
                            <el-table-column label="倉庫名稱" prop="wtitle" width="180">
                            </el-table-column>
                            <el-table-column label="收貨地址" prop="addr" width="650">
                            </el-table-column>
                            <el-table-column label="郵政編碼" prop="code" width="200">
                            </el-table-column>
                            <el-table-column label="操作" width="">
                                <template slot-scope="scope">
                                    <span @click="copyAddress(scope.row)" class="pointer"
                                        style="color: #EF436D">複製</span>
                                </template>
                                >
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="title">包裹預報</div>
                    <div class="tab-parcel-info">
                        <el-table :data="formData" style="width: 100%; height: 470px; overflow-y: auto;" class="custom-header custom-scrollbar">
                            <el-table-column label="快遞單號" prop="deliveryId" width="280">
                                <template slot-scope="scope">
                                    <el-input placeholder="请输入快遞單號" v-model="scope.row.deliveryId"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="快遞名稱" prop="deliveryName" width="160">
                                <template slot-scope="scope">
                                    <el-select placeholder="请选择快遞" v-model="scope.row.deliveryName" @change="onDeliveryChange(scope.row)">
                                        <el-option :key="item.value" :label="item.label" :value="item.label"
                                            v-for="item in deliveryOptions">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="貨物名稱" prop="goodsName" width="280">
                                <template slot-scope="scope">
                                    <el-input placeholder="请输入貨物名稱" v-model="scope.row.goodsName"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="貨物類型" prop="goodsType" width="160">
                                <template slot-scope="scope">
                                    <el-select placeholder="普貨/特貨" v-model="scope.row.goodsType">
                                        <el-option :key="item.value" :label="item.label" :value="item.value"
                                            v-for="item in goodsTypeOptions">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="貨物數量" prop="goodsCount" width="200">
                                <template slot-scope="scope">
                                    <el-input-number :max="50" :min="1" @change="handleChange(scope.row.goodsCount)"
                                        v-model="scope.row.goodsCount">
                                    </el-input-number>
                                </template>
                            </el-table-column>
<!--                            <el-table-column label="預計寄外島" prop="isSend" width="120">-->
<!--                                <template slot-scope="scope">-->
<!--                                    <el-select placeholder="是" v-model="scope.row.isSend">-->
<!--                                        <el-option :key="item.value" :label="item.label" :value="item.value"-->
<!--                                            v-for="item in isSendOptions">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                </template>-->
<!--                            </el-table-column>-->
                            <el-table-column label="倉庫" prop="deliveryType" width="120">
                                <template slot-scope="scope">
                                    <el-select placeholder="選擇倉庫" v-model="scope.row.deliveryType">
                                        <el-option :key="item.value" :label="item.label" :value="item.value"
                                            v-for="item in deliveryTypeOptions">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="備註項目" prop="remarks" width="240">
                                <template slot-scope="scope">
                                    <el-input placeholder="请输入" maxlength="10" show-word-limit v-model="scope.row.remarks"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="">
                                <template slot-scope="scope">
                                    <div v-if="scope.$index === 0" style="display: flex; align-items: center; gap: 8px">
                                        <span class="green fs14 pointer underline" @click="addRow()">新增</span>
                                        <span class="fs14 brown pointer underline" @click="resetAllRows()">清空</span>
                                    </div>
                                    <div v-else style="display: flex; align-items: center; gap: 8px">
                                        <span class="red fs14 pointer underline" @click="removeRow(scope.$index)">刪除</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
            <div class="forecast-footer">
                <div class="footer-left">
                    <el-checkbox class="forecast-radio" v-model="checked"></el-checkbox>
                    <span style="margin-left: 4px">我同意EZ集運通相關條款</span>
                    <a href="{:url('/index/login/tran_agree')}">-集運商品運送條款</a>
                </div>
                <div class="footer-right">
                    <el-button @click="submit" style="background: #EF436D" type="danger">確認委託
                    </el-button>
                </div>
            </div>
            <el-dialog title="" :visible.sync="protocolDialogVisible" width="1008px">
                {include file="assets/timeout" /}
                <span slot="footer" class="dialog-footer">
                <el-button @click="protocolDialogVisible = false">返 回</el-button>
            </span>
            </el-dialog>
        </div>
    </div>
</div>

<script>
    var userData = JSON.parse('<?=json_encode($rows, JSON_UNESCAPED_UNICODE)?>');
    var kuaidi = JSON.parse('<?=json_encode($kd, JSON_UNESCAPED_UNICODE)?>');
    var tp = JSON.parse('<?=json_encode($tp, JSON_UNESCAPED_UNICODE)?>');
    var gt = JSON.parse('<?=json_encode($gt, JSON_UNESCAPED_UNICODE)?>');
    console.log('userData', userData)
    console.log('kuaidi', kuaidi)
    console.log('tp', tp)
    console.log('gt', gt)
    const app = new Vue({
        el: '#application',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            isCardStatus: false,
            protocolDialogVisible: false,
            // activeMenu: 0,
            // MenuHref:[
            //     'index',        // 預報包裹
            //     'myParcel',    // 我的包裹
            //     'order',        // 訂單列表
            //     'imp',          // 進口憑證
            //     'recipient',    // 收件人管理
            //     'exp',          // 集運說明
            //     'addr',      // 倉庫地址
            //     'applicant'     // 申報人管理
            // ],
            formData: [{
                deliveryId: '',     // 快遞單號
                // deliveryName: '',   // 快遞名
                goodsName: '',      // 商品名稱
                goodsType: '',      // 商品類型
                goodsCount: 1,      // 貨物數量
                // isSend: null,         // 是否寄外島
                deliveryType: '',   // 倉庫類型
                remarks: '',
                wid: '',         // 物流ID
            }],
            isOperation: 1,
            deliveryList: kuaidi,    // 快递类型
            tabList: [],        // 選項卡
            activeIndex: 0,     // 当前激活的选项卡索引
            userList: userData,
            goodsGt: gt,
            // isSendOptions: [
            //     { value: 1, label: '是' },
            //     { value: 0, label: '否' }
            // ],
            deliveryType: tp,
            checked: false,
        },
        computed: {
            firstUserList() {
                return this.userList.filter(item => item.type === 15);
            },
            secondUserList() {
                return this.userList.filter(item => item.type === 16);
            },
            thirdUserList() {
                return this.userList.filter(item => item.type === 17);
            },
            activeUserList() {
                switch (this.activeIndex) {
                    case 0: return this.firstUserList;
                    case 1: return this.secondUserList;
                    case 2: return this.thirdUserList;
                    default: return [];
                }
            },
            deliveryOptions() {
                return this.deliveryList.map(item => ({
                    value: item.id,
                    label: item.name
                }));
            },
            deliveryTypeOptions() {
                let type;
                switch (this.activeIndex) {
                    case 0: // 海快
                        type = 15;
                        break;
                    case 1: // 空运
                        type = 16;
                        break;
                    case 2: // 海運
                        type = 17;
                        break;
                    default:
                        type = null;
                }
                // 过滤对应type的仓库，并映射为选项格式
                return this.deliveryType.filter(item => item.id === type).flatMap(item => item.list).map(ite=>({
                        value: ite.id,
                        label: ite.title
                    }));
            },
            goodsTypeOptions() {
                return this.goodsGt.map(item => ({
                    value: item.id,
                    label: item.title
                }))
            },
        },

        mounted() {
            const links = document.querySelectorAll('#nav_app .nav-link');
            const current = window.location.pathname;
            links.forEach(link => {
                if (link.getAttribute('href').indexOf(current) !== -1) {
                    link.classList.add('active');
                }
            });
            this.initTabs();
        },

        methods: {
            resetFormData() {
                return {
                    deliveryId: '',
                    deliveryName: '',
                    goodsName: '',
                    goodsType: '',
                    goodsCount: 1,
                    // isSend: null,
                    deliveryType: '',
                    remarks: '',
                }
            },
            resetRow(index) {
                this.$set(this.formData, index, this.resetFormData());
            },
            resetAllRows() {
                // 清空所有行的内容，但保留行数
                this.formData.forEach((item, index) => {
                    this.$set(this.formData, index, this.resetFormData());
                });
            },
            changeTabs(index) {
                this.activeIndex = index;
            },
            initData() {
                this.initTabs();
            },
            initTabs() {
                this.tabList = [...new Set(this.deliveryType.map(item => item.name))];
            },
            handleChange(val) {
                console.log(val, '货物数量变化');
            },
            addRow() {
                let newRow = this.resetFormData(); // 使用 resetFormData 初始化新行数据
                this.formData.push(newRow); // 将新行数据添加到末尾
                console.log('新增行:', newRow);
            },
            removeRow(index) {
                if (this.formData.length > 1) {
                    this.formData.splice(index, 1);
                }
            },
            copyAddress(row) {
                const userInfo = `聯絡人：${row.username}\n聯繫電話：${row.mobile}\n郵政編碼：${row.code}\n收貨地址：${row.addr}`;
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(userInfo).then(() => {
                        this.$message({
                            message: '複製成功',
                            type: 'success'
                        });
                    }).catch(err => {
                        console.error('複製失敗:', err);
                    });
                } else {
                    const textarea = document.createElement('textarea');
                    textarea.value = userInfo;
                    document.body.appendChild(textarea);
                    textarea.select();
                    try {
                        document.execCommand('copy');
                        this.$message({
                            message: '複製成功',
                            type: 'success'
                        });
                    } catch (err) {
                        console.error('複製失敗:', err);
                    } finally {
                        document.body.removeChild(textarea);
                    }
                }
            },

            // 校验表单
            validateForm() {
                const requiredFields = [
                    'deliveryId',   // 快遞單號
                    'deliveryName', // 快遞名
                    'goodsName',    // 商品名稱
                    'goodsType',    // 商品類型
                    // 'isSend',       // 是否寄外島
                    'deliveryType'  // 倉庫類型
                ];
                const hasEmpty = this.formData.some(row =>
                    requiredFields.some(field => {
                        const value = row[field];
                        // 字符串类型检查trim后长度，其他类型只判断 undefined/null
                        return typeof value === 'string'
                            ? !value.trim()
                            : value === undefined || value === null;
                    })
                );

                if (hasEmpty) {
                    this.$message({
                        message: '請將信息填寫完整再提交',
                        type: 'error',
                        duration: 3000
                    });
                    return false;
                }
                return true;
            },

            submit: _.debounce(async function () {
                try {
                    if (!this.checked) {
                        this.$message.warning('請同意EZ集運通相關條款')
                        return
                    }

                    if (!this.validateForm()) return

                    // const submitData = JSON.parse(JSON.stringify(this.formData)).map(item => ({
                    //     ...item,
                    //     goodsCount: Number(item.goodsCount),
                    //     goodsName: item.goodsName.trim(),
                    //     deliveryId: item.deliveryId.trim()
                    // }))

                    // 请求接口
                    let requestData = this.formData.map(item => ({
                        deliveryId: item.deliveryId.trim(),
                        goodsName: item.goodsName,
                        goodsType: item.goodsType, 
                        goodsCount: item.goodsCount,
                        deliveryType: item.deliveryType,
                        remarks: item.remarks,
                        // sname: item.sname,
                        wid: item.id
                    }));
                    let res = await axios.post('index', {
                        data: requestData,
                        ok:0
                    });
                    console.log(res, 'res');

                    if (Number(res.data.code) === 0) {
                        this.$message.success(res.data.msg)
                        this.formData = [this.resetFormData()];
                        this.checked = false;
                        return;
                    }

                    if (Number(res.data.code) === 1) {
                        this.$message.error(res.data.msg);
                        return
                    }

                    if (Number(res.data.code) === 2) {
                        this.$confirm('填寫的快遞單號查詢不到，是否繼續提交?', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(async () => {
                            let res2 = await axios.post('index', {
                                data: requestData,
                                ok: 1
                            });
                            this.$message.success(res2.data.msg)
                            this.formData = [this.resetFormData()];
                            this.checked = false;
                            return;
                        }).catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消本次提交'
                            });
                        });
                        return;
                    }



                } catch (error) {
                    console.error('提交出错:', error)
                    this.$message.error('網絡錯誤，請稍後重試')
                } finally {
                }
            }, 800),
            onDeliveryChange(row) {
                const selected = this.deliveryList.find(item => item.name === row.deliveryName);
                if (selected) {
                    // row.sname = selected.sname;
                    row.id = selected.id
                } else {
                    // row.sname = '';
                    row.id = '';
                }
                console.log('selected',selected);
                
            },
        }
    })
</script>