{include file="common/resources" /}
<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="exp">
    <!-- 引入底部导航栏组件 -->
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="tab-header">
            <?php
                foreach($site['transport'] as $key => $ls){
            ?>
                <?php
                    if($key==1){
                ?>
                <div class="tab-item pointer fs16 active" onclick="showTab('<?=$key?>')"><?=$ls?>説明</div>
                <?php
                    }else{
                ?>     
                <div class="tab-item pointer fs16" onclick="showTab('<?=$key?>')"><?=$ls?>説明</div>  
                <?php
                    }
                ?>
            <?php
                }
            ?>
        </div>
        
        <div class="absolute fw fs16" style="top: 20px; left: 20px; color: #3D3D3D">集運說明</div>
        
        <div class="re-content">
            <?php
                foreach($site['transport'] as $key => $ls){
            ?>
                <div id="<?=$key?>" class="tab-content card-grid" style="display: <?=$key==1 ? 'flex' : 'none'?>;">
                    <?php
                        foreach($list_tp as $item){
                    ?>
                        <?php
                            if($item['type']==$key){
                        ?>
                            <div class="card-item">
                                <div class="card-title">
                                    <img src="{$item['img']}">
                                    <?=$item['title']?>
                                </div>
                                <div class="card-content">
                                    <?=$item['content']?>
                                </div>
                            </div>
                        <?php
                            }
                        ?>
                    <?php
                        }
                    ?>
                </div>
            <?php
                }
            ?>
            
            <div class="faq-section">
                <?php
                    foreach($list as $key => $ls){
                ?>
                    <?php
                        if($key % 3 == 0){
                    ?>
                    <div class="faq-col">
                    <?php
                        }
                    ?>
                    <a class="item ellipsis" target="_blank" href="<?=url('index/transport/exp_content',array('id'=>$ls['id']))?>"><?=$ls['title']?></a>
                    <?php
                        if(($key + 1) % 3 == 0 || $key == count($list) - 1){
                    ?>
                    </div>
                    <?php
                        }
                    ?>
                <?php
                    }
                ?>       
            </div>
        </div>
    </div>
</div>

<script>
    function showTab(tabId) {
        // Hide all tab contents
        const tabs = document.getElementsByClassName('tab-content');
        for (let tab of tabs) {
            tab.style.display = 'none';
        }
        
        // Show the selected tab content
        document.getElementById(tabId).style.display = 'flex';
        
        // Update tab button styling
        const buttons = document.getElementsByClassName('tab-item');
        for (let btn of buttons) {
            btn.classList.remove('active');
        }
        
        // Add active class to clicked button
        event.currentTarget.classList.add('active');
    }

    document.addEventListener('DOMContentLoaded', function () {
        if (typeof bottomNavMixin !== 'undefined') {
            new Vue({
                el: '#exp',
                mixins: [bottomNavMixin],
            });
        }
    });

</script>














