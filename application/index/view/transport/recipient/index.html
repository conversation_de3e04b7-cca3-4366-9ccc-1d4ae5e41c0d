
{include file="common/resources" /}
<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<!-- 使用隐藏输入字段传递数据到Vue -->
<input type="hidden" id="listData" value='{:json_encode($list)}'>

<div class="transport-box" id="recipient">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="absolute fw fs16" style="top: 20px; left: 20px; color: #3D3D3D">收件人</div>
        <div class="handle-header" style="width: 100%; margin-top: 56px">
            <div style="display: flex; gap: 24px; align-items: center">
                <div class="fs16 c333">搜索內容：</div>
                <div style="display: flex">
                    <el-input
                    style="width: 340px;"
                    placeholder="收件人/電話"
                    v-model="iValue"
                    @keyup.enter.native="searchRecipient"
                    >
                    <i slot="suffix" class="el-input__icon el-icon-search pointer" @click="searchRecipient"></i>
                  </el-input>
                </div>
            </div>
            <div class="filter-btns">
                <el-button icon="el-icon-plus" style="background: #22B573" type="success" @click="addRecipient">新增
                </el-button>
                <el-button icon="el-icon-delete" type="warning" @click="deleteRecipient">刪除
                </el-button>
                <el-button icon="el-icon-refresh" style="border: 1px solid #D8D8D8; background: #FFFFFF; color: #666666"
                    type="info">重置
                </el-button>
            </div>
        </div>
        <div class="table mb24" style="width: 100%;">
            <div v-if="reList.length === 0" style="width: 100%; min-height: 572px;">
                <el-empty description="暫無數據"></el-empty>
            </div>
            <el-table 
            v-else 
            :data="reList" 
            style="width: 100%; min-height: 572px" 
            class="custom-header rec-table"
            @selection-change="handleSelect">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column prop="name" label="收件人" width="100" align="center"></el-table-column>
                <el-table-column prop="label" label="分组" width="100" align="center"></el-table-column>
                <el-table-column prop="mobile" label="电话" width="220" align="center"></el-table-column>
                <el-table-column prop="detail" label="宅配地址" width="500" align="center"
                    show-overflow-tooltip></el-table-column>
                <!-- <el-table-column label="店取地址" width="300" align="center"></el-table-column> -->
                <!-- <el-table-column label="狀態" width="100" align="center"></el-table-column> -->
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                       <span class="pointer" style="color:#22B573; margin-right:8px;" @click="editRecipient(scope.row)">修改</span>
                       <span class="pointer" style="color:#EF436D;" @click="deleteRecipient(scope.row)">删除</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="my-footer">
            <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]" :total="reList.length || 0"
                @current-change="handleCurrentChange" @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
        </div>

    </div>
</div>

<script>
    // 从隐藏字段获取数据
    var listDataInput = document.getElementById('listData');
    var recipientData = [];
    
    try {
        recipientData = JSON.parse(listDataInput.value);
    } catch (e) {
        console.error('Error parsing JSON data:', e);
    }
    
    var app = new Vue({
        el: '#recipient',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            activeIndex: 0,
            currentPage: 1,
            currentPageSize: 5,
            pageSize: [5, 10, 20, 50],
            orderData: '',
            recipientType: [],
            iValue: '',
            ids:[],
            reList:recipientData
        },
        mounted() {
            console.log(this.reList,'++++++++++++++++++');
            
        },
        methods: {
            handleCurrentChange() {
                
            },
            handleSizeChange() {
                
            },
            addRecipient() {
                window.location.href = '/index/user/addAddresses';
            },
            
            // 选项框事件
            handleSelect(e) {
                this.ids = e.map(item => item.id);
                console.log(this.ids,'this.ids');
                
            },

            async searchRecipient() {
                try {
                    let res = await axios.post('/index/transport/recipient', {
                        sch: this.iValue
                    });
                    if (res.data.code == 0) {
                        this.reList = res.data.data;
                        console.log(this.reList,'this.reList');
                        
                    }
                } catch (err) {
                    console.log(err, 'err');
                }
            },

            
            async deleteRecipient(row) {
                let ids = (row && row.id) ? [row.id] : this.ids;

                // 检查是否有要删除的项
                if (!ids || ids.length === 0) {
                    this.$message.warning('請先選擇要刪除的收件人');
                    return;
                }
                
                try {
                    // 确认是否删除
                    await this.$confirm('確定要刪除所選收件人嗎?', '提示', {
                        confirmButtonText: '確定',
                        cancelButtonText: '取消',
                        type: 'warning',
                        confirmButtonClass: 'el-button--danger custom-confirm-button'
                    });
                    
                    // 发送删除请求
                    let params = {
                        ids: ids,
                        url: '/index/transport/recipient'
                    };
                    
                    let res = await axios.post('/index/user/deleteAddress', params, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    
                    if (res.data.code == 1) {
                        console.log(res,'++++++++++++++++++');
                        
                        this.$message.success(res.data.msg);
                        window.location.reload();
                    } else {
                        this.$message.error(res.data.msg || '刪除失敗');
                    }
                } catch (err) {
                    if (err !== 'cancel') {
                        console.error('删除失败:', err);
                        this.$message.error('系統錯誤，請稍後再試');
                    }
                }
            },

            editRecipient(row) {
                window.location.href = `/index/user/addAddresses?id=${row.id}`;
            }
        }
    });
</script>

<style>
.custom-confirm-button {
    background-color: #EF436D !important;
    border-color: #EF436D !important;
}
</style>