{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="addr">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="absolute fw fs16" style="top: 20px; left: 20px; color: #3D3D3D">倉庫地址</div>
        <div class="addr-content">
            <el-table :data="warehouseData" class="custom-header" style="width: 100%; margin: 0 auto;">
                <el-table-column prop="label" label="" width="120" align="left" :resizable="false" header-align="center" fixed>
                </el-table-column>
                <el-table-column prop="haikuai" label="海快仓库" align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.label === '运输方式'"><img class="transport-icon" src="__CDN__/assets/img/pc/transport01.svg">海快</span>
                        <span v-else-if="scope.row.label === '仓库地址'" v-html="scope.row.haikuai"></span>
                        <span v-else>{{ scope.row.haikuai }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="kongyun" label="空运仓库" align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.label === '运输方式'"><img class="transport-icon" src="__CDN__/assets/img/pc/transport02.svg">空运</span>
                        <span v-else-if="scope.row.label === '仓库地址'" v-html="scope.row.kongyun"></span>
                        <span v-else>{{ scope.row.kongyun }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="haiyun" label="海运仓库" align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.label === '运输方式'"><img class="transport-icon" src="__CDN__/assets/img/pc/transport03.svg">海运</span>
                        <span v-else-if="scope.row.label === '仓库地址'" v-html="scope.row.haiyun"></span>
                        <span v-else>{{ scope.row.haiyun }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</div>

<script>
     const app = new Vue({
        el: '#addr',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            warehouseData: [
                {
                    label: '运输方式',
                    haikuai: '',
                    kongyun: '',
                    haiyun: ''
                },
                {
                    label: '免费仓储',
                    haikuai: '60天',
                    kongyun: '30天',
                    haiyun: '180天'
                },
                {
                    label: '时效',
                    haikuai: '5-7天',
                    kongyun: '3-5天',
                    haiyun: '7-14天'
                },
                {
                    label: '仓库地址',
                    haikuai: '深圳市宝安区松岗街道塘下涌社区金嘉利工业区5栋飞扬转运仓<br/>(会员ID：10518+19831)',
                    kongyun: '深圳市宝安区松岗街道塘下涌社区金嘉利工业区5栋飞扬转运仓<br/>(会员ID：10518+19831)',
                    haiyun: '深圳市宝安区松岗街道塘下涌社区金嘉利工业区5栋飞扬转运仓<br/>(会员ID：10518+19831)'
                }
            ]
        },
        methods: {
           
        }
    })
</script>