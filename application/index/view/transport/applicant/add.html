
{include file="common/resources" /}
<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box">
    <div class="left_d" data-parent-nav="transport/applicant">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <div id="applicant-form">
            <a href="javascript:history.back();" class="back-link">&lt; 返回</a>
            
            <h2 class="form-title">{{isEdit ? '修改申報人' : '新增申報人'}}</h2>
            
            <el-form :model="formData" :rules="rules" ref="applicantForm" label-width="100px" @submit.native.prevent>
                <el-form-item prop="name" label="姓名" class="form-item">
                    <el-input v-model="formData.name" placeholder="請輸入姓氏與名字"></el-input>
                </el-form-item>
                
                <el-form-item prop="mobile" label="手機號碼" class="form-item">
                    <el-input v-model="formData.mobile" placeholder="請輸入手機號碼"></el-input>
                </el-form-item>
                
                <el-form-item prop="card" label="身份證號" class="form-item">
                    <el-input v-model="formData.card" placeholder="請輸入身份證號"></el-input>
                </el-form-item>
                
                <el-form-item class="form-buttons">
                    <el-button @click="handleCancel">取消</el-button>
                    <el-button type="primary" @click="submitForm" :loading="loading">確定</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</div>

<style>
#applicant-form {
    padding: 20px;
    max-width: 700px;
}

.back-link {
    color: #EF436D;
    text-decoration: none;
    font-size: 14px;
    display: block;
    margin-bottom: 15px;
}

.form-title {
    color: #EF436D;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding-left: 100px;
}

/* Element UI 样式覆盖 */
.form-item {
    position: relative;
    margin-bottom: 22px;
}

.el-form-item__label {
    text-align: right;
    line-height: 30px;
}

.el-input__inner {
    height: 40px;
}

.el-button--primary {
    background-color: #EF436D;
    border-color: #EF436D;
}

.el-button--primary:hover,
.el-button--primary:focus {
    background-color: #f26585;
    border-color: #f26585;
}
</style>

<script>
new Vue({
    el: '#applicant-form',
    data() {
        // 自定义验证函数
        const validateMobile = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('請輸入手機號碼'));
            }
            if (!/^\d{8,15}$/.test(value)) {
                return callback(new Error('請輸入有效的手機號碼'));
            }
            callback();
        };
        
        const validateIDCard = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('請輸入身份證號'));
            }
            if (!/^[A-Z0-9]{8,18}$/.test(value)) {
                return callback(new Error('請輸入有效的身份證號'));
            }
            callback();
        };
        
        return {
            formData: {
                name: '',
                mobile: '',
                card: '',
                ty: 0 // 个人类型
            },
            rules: {
                name: [{ required: true, message: '請輸入收件人姓名', trigger: 'blur' }],
                mobile: [
                    { required: true, message: '請輸入手機號碼', trigger: 'blur' },
                    { pattern: /^09\d{8}$/, message: '手機號碼必須為09開頭的10位數字', trigger: 'blur' }
                ],
                card: [
                    { required: true, message: '請輸入收件人身份證號', trigger: 'blur' },
                    { pattern: /^[A-Z][0-9]{9}$/, message: '身份證號必須為1位大寫字母+9位數字', trigger: 'blur' }
                ]
            },
            loading: false,
            isEdit: false,
            editId: null
        };
    },
    created() {
        // 检查URL中是否有id参数，判断是编辑还是新增
        this.checkMode();
    },
    methods: {
        checkMode() {
            // 获取URL中的id参数
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            
            if (id) {
                this.isEdit = true;
                this.editId = id;
                // 如果是编辑模式，加载申报人数据
                this.loadApplicantData(id);
            }
        },
        
        loadApplicantData(id) {
            axios.get('/index/transport/addApplicant', {
                params: { id: id }
            }).then(response => {
                if (response.data && response.data.code === '0' && response.data.data) {
                    const data = response.data.data;
                    this.formData = {
                        name: data.username || '',
                        mobile: data.mobile || '',
                        card: data.card || '',
                        ty: data.type || 0
                    };
                } else {
                    this.$message.error('獲取申報人數據失敗');
                }
            }).catch(error => {
                console.error('獲取數據失敗:', error);
                this.$message.error('獲取申報人數據失敗');
            });
        },
        
        submitForm() {
            this.$refs.applicantForm.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    
                    // 准备请求参数，编辑模式需要添加id
                    const params = {
                        uname: this.formData.name,
                        mb: this.formData.mobile,
                        card: this.formData.card,
                        ty: this.formData.ty
                    };
                    
                    // 如果是编辑模式，添加id参数
                    if (this.isEdit && this.editId) {
                        params.id = this.editId;
                    }
                    
                    // 提交表单数据
                    axios.post('/index/transport/addApplicant', params).then(response => {
                        this.loading = false;
                        if (response.data.code === '0') {
                            // 提交成功
                            this.$message({
                                message: this.isEdit ? '修改申報人成功' : '新增申報人成功',
                                type: 'success'
                            });
                            // 跳转回列表页
                            setTimeout(() => {
                                sessionStorage.setItem('activeNav', 'transport/applicant');
                                window.location.href = '/index/transport/applicant';
                            }, 1000);
                        } else {
                            // 显示错误信息
                            this.$message.error(response.data.msg || '提交失敗，請稍後重試');
                        }
                    }).catch(error => {
                        this.loading = false;
                        console.error('提交失敗:', error);
                        this.$message.error('提交失敗，請稍後重試');
                    });
                } else {
                    return false;
                }
            });
        },
        
        handleCancel() {
            history.back();
        }
    }
});
</script>