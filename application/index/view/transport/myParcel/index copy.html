{include file="common/resources" /}
{load href="__CDN__/assets/css/login.css,__CDN__/assets/js/bottom_nav.js" /}
<script src="//at.alicdn.com/t/c/font_4960088_jne4eo87lz9.js"></script>


<!-- 添加CSRF token -->
{:token()}
<style>
    [v-cloak] {
        display: none;
    }
    
    @font-face {
        font-family: 'Source Han Sans CN';
        src: url('__CDN__/assets/css/font_family/SourceHanSans/SourceHanSansTW-Regular.otf') format('opentype');
        font-style: normal;
        font-display: swap;
    }

    .custom-timestamp .el-timeline-item__timestamp {
        color: #91949a;
    }

    .active-timestamp .el-timeline-item__timestamp {
        color: #67c23a;
    }

</style>
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="my-parcel" v-cloak>
    <!-- 引入底部导航栏组件 -->
    {include file="common/bottom_nav" /}
    
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <div class="custom-tabs relative">
            <div class="absolute fw fs16" style="top: 0; left: 0; color: #3D3D3D">我的包裹</div>
            <!-- 标签导航 -->
            <div class="tab-header">
                <div :class="{ 'active': activeIndex === index }" :key="index" @click="changeTabs(activeIndex = index)"
                     class="tab-item fs16" v-for="(item, index) in label">
                    {{ item }}
                </div>
            </div>
            <!-- 内容区域 -->
            <div class="tab-content">
                <div v-if="activeIndex === 0">
                    <div style="width: 100%;">
                    
                        <div class="my-nav">
                            <!-- 左侧标签页，类似el-tabs -->
                            <el-tabs @tab-click="onWarehouseChange" v-model="form.warehouseName" style="flex-grow: 1;">
                                <el-tab-pane :key="item.id" :label="formatTabLabel(item)" :name="item.id"
                                    v-for="(item, index) in warehouseOptions"></el-tab-pane>
                            </el-tabs>
                            <!-- 右侧搜索框 -->
                            <div class="nav-search">
                                <input type="text" class="search-input" v-model="form.deliveryNum" placeholder="輸入快遞單號"
                                    @keyup.enter="searchParcel">
                                <button class="search-icon" @click="searchParcel">
                                    <i class="el-icon-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div v-loading="tabLoading"  class="loading-container">
                        <div v-if="paginatedData.length === 0" style="width: 100%; min-height: 638px">
                            <el-empty description="暫無數據"></el-empty>
                        </div>
                        <el-table
                                v-else
                                :data="paginatedData"
                                style="width: 100%; max-height: 686px;"
                                tooltip-effect="dark"
                                class="custom-header is-scrolling-none custom-scrollbar">
                            <el-table-column type="selection" width="55" :selectable="checkSelectable">
                            </el-table-column>
                            <el-table-column label="商品信息" width="500px">
                                <template slot-scope="scope">
                                    <div class="goods-up">
                                        <div class="goods-up-left">
                                            <span>
                                                <svg class="icon fs14" aria-hidden="true" style="display: inline;">
                                                    <use :xlink:href="scope.row.icon"></use>
                                                </svg>
                                                <span class="red">{{scope.row.sname}} <span>{{scope.row.waybill}}</span></span>
                                            </span>
                                            <span style="margin-left: 12px">
                                                <div class="dot"></div>
                                            </span>
                                            <el-tooltip
                                                    class="item"
                                                    effect="light"
                                                    placement="bottom-start"
                                                    :visible-arrow="false"
                                                    transition="el-fade-in-linear"
                                                    popper-class="logistics-tooltip"
                                                    @hide="wuliuList = []"
                                            >
                                                <template slot="content">
                                                    <div class="logistics-box">
                                                        <el-timeline>
                                                            <el-timeline-item :color="item.color" :icon="item.icon" :key="index" :size="item.size"
                                                                              :timestamp="item.timestamp" :type="index === 0 ? 'success' : ''" placement="top" v-for="(item, index) in wuliuList">
                                                                <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: center; gap: 8px;">
                                                                    <span class="fs13 fw" :style="{ color: index === 0 ? '#67c23a' : '#333333' }">[{{item.status}}]</span>
                                                                    <span class="fs13 c666">{{item.content}}</span>
                                                                </div>
                                                            </el-timeline-item>
                                                        </el-timeline>
                                                    </div>
                                                </template>
                                                <span class="logistics" @mouseenter="openLogistics(scope.row)">
                                                <span class="pointer" style="font-weight: 500; color: #EF436D">【{{scope.row.kd_log ?.status || '暫無物流狀態'}}】</span>
                                                <span class="pointer">{{scope.row.kd_log?.context || '暂无物流信息'}}</span>
                                              </span>
                                            </el-tooltip>

                                        </div>
                                    </div>
                                    <!-- <div class="top-line"></div> -->
                                    <div class="goods-middle pointer" style="padding: 24px 0; margin-bottom: -12px;">
                                        <div class="goods-img">
                                            <img v-if="scope.row.goods_url" :src="scope.row.goods_url" alt="">
                                            <img  v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                                        </div>
                                        <div class="goods-name">
                                            <div class="goods-short-name">[{{scope.row.goods_name}}]</div>
                                            <!--                                            <div class="goods-description-name">-->
                                            <!--                                                <img class="goods-small-img"-->
                                            <!--                                                    src="__CDN__/assets/img/pc/new_index/red.png">-->
                                            <!--                                                <span>懒人桌面手机支架金属悬臂直播俯拍录视频可升降旋转平板支架批发</span>-->
                                            <!--                                            </div>-->
                                            <div class="goods-description-name">暂无商品详细信息</div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="委託單號" min-width="16%">
                                <template slot-scope="scope">
                                    <div style="margin-top: 58px">{{scope.row.entrust_no}}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="委托時間" min-width="16%">
                                <template slot-scope="scope">
                                    <div style="margin-top: 58px">{{formatDateTime(scope.row.createtime, 1)}}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" min-width="18%">
                                <template slot-scope="scope">

                                    <div class="handle-up">
                                        <div v-if="Number(scope.row.refuse) === 0" class="goods-remarks">
                                            <div>備註：</div>
                                            <div style="color: #22B573">{{scope.row.oremarks || ''}}</div>
                                            <div @click="openRemarks(scope.row)" class="edit pointer"><img
                                                    src="__CDN__/assets/img/pc/new_index/edit_icon.png"></div>
                                        </div>
                                    </div>
                                    <div class="handle-middle" style="margin-top: 24px">
                                        <div style="display: flex; flex-direction: column; justify-content: center;align-content: center;gap: 22px">
                                            <div>
                                                <span v-if="Number(scope.row.refuse) === 0" @click="openService(scope.row)"
                                                   class="underline red mr12 fs13 pointer">附加服務</span>
                                                <el-popconfirm v-if="Number(scope.row.refuse) === 0" 
                                                        title="是否確定拒收該包裹？"
                                                        @confirm="handleRejectConfirm(scope.row)">
                                                    <template slot="reference">
                                                    <span class="underline red mr12 fs13 pointer">拒收包裹</span>
                                                    </template>
                                                </el-popconfirm>
                                                <span v-else class="red mr12 fs13 underline"
                                                      style="cursor: not-allowed">包裹已拒收</span>
                                                <span v-if="Number(scope.row.refuse) === 0" @click="openTransfer" class="underline red fs13 pointer">包裹轉寄</span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!-- <div class="my-footer">
                            <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]"
                                           :total="parcelData.length" @current-change="handleCurrentChange"
                                           @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper">
                            </el-pagination>
                        </div> -->
                    </div>
                </div>
                <div v-if="activeIndex === 1">
                    <div class="handle-tabs">
                        <!-- Tabs -->
                        <el-tabs @tab-click="changeWarehouse" v-model="activeName" style="flex-grow: 1;">
                            <el-tab-pane :key="item.id" :label="formatTabLabel(item)" :name="item.id"
                                         v-for="(item, index) in warehouseOptions"></el-tab-pane>
                        </el-tabs>
                        <div class="handle-tabs-right">
                            <div class="handle-tabs-text">已選 <span class="red">{{selectedParcels.length}}</span>
                                個包裹，包裹總重量：<span class="red">{{totalWeight}}KG</span></div>
                            <el-button style="margin-bottom: 8px" icon="el-icon-box"
                                       :style="selectedParcels.length > 0 ? 'background: #22B573; color: #FFFFFF' : 'background: #EEEEEE; color: #666666'"
                                       :type="selectedParcels.length > 0 ? 'success' : 'info'" size="small"
                                       @click="handlePack">
                                打包回臺
                            </el-button>
                            <el-button style="margin-bottom: 8px" icon="el-icon-refresh" size="small"
                                       @click="resetInput">重置
                            </el-button>
                        </div>
                    </div>
                    <div v-loading="tabLoading"  class="loading-container">
                        <div v-if="paginatedData.length === 0" style="width: 100%; min-height: 638px;">
                            <el-empty description="暫無數據"></el-empty>
                        </div>
                        <el-table
                                v-else
                                :data="paginatedData"
                                ref="myTable"
                                style="width: 100%; max-height: 686px;"
                                tooltip-effect="dark"
                                class="custom-header is-scrolling-none custom-scrollbar"
                                @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" :selectable="checkSelectable">
                            </el-table-column>
                            <el-table-column label="入庫時間/快遞單號/商品信息" min-width="45%">
                                <template slot-scope="scope">
                                    <div class="goods-up">
                                        <div class="goods-up-left">
                                            <span style="margin-right: 12px">{{ formatDateTime(scope.row.createtime, 1)}}</span>
                                            <svg class="icon fs14" aria-hidden="true" style="display: inline;">
                                                <use :xlink:href="scope.row.icon"></use>
                                            </svg>
                                            <span class="red">{{scope.row.sname}} <span>{{scope.row.waybill}}</span></span>
                                            <span style="margin-left: 12px">
                                                <div class="dot"></div>
                                            </span>
                                            <el-tooltip
                                                    class="item"
                                                    effect="light"
                                                    placement="bottom-start"
                                                    :visible-arrow="false"
                                                    transition="el-fade-in-linear"
                                                    popper-class="logistics-tooltip"
                                                    @hide="wuliuList = []"
                                            >
                                                <template slot="content">
                                                    <div class="logistics-box">
                                                        <el-timeline>
                                                            <el-timeline-item :color="item.color" :icon="item.icon" :key="index" :size="item.size"
                                                                              :timestamp="item.timestamp" :type="item.type" placement="top" v-for="(item, index) in wuliuList">
                                                                <div
                                                                        style="display: flex; flex-direction: column; align-items: flex-start; justify-content: center; gap: 8px;">
                                                                    <span class="fs13 fw" :style="{ color: index === 0 ? '#67c23a' : '#333333' }">[{{item.status}}]</span>
                                                                    <span class="fs13 c666">{{item.content}}</span>
                                                                </div>
                                                            </el-timeline-item>
                                                        </el-timeline>
                                                    </div>
                                                </template>
                                                <span class="logistics" @mouseenter="openLogistics(scope.row)">
                                                <span class="pointer" style="font-weight: 500; color: #EF436D">【{{scope.row.kd_log?.status || '暫無物流狀態'}}】</span>
                                                <span class="pointer">{{scope.row.kd_log?.context || '暫無物流信息'}}</span>
                                              </span>
                                            </el-tooltip>
                                        </div>
                                    </div>
                                    <!-- <div class="top-line"></div> -->
                                    <div class="goods-middle pointer" style="padding: 24px 0">
                                        <div class="goods-img">
                                            <img v-if="scope.row.goods_url" :src="scope.row.goods_url" alt="">
                                            <img  v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                                        </div>
                                        <div class="goods-name">
                                            <div class="goods-short-name">
                                                <span class="mr8">[{{scope.row.goods_name}}]</span>
                                                <span>
                                                    <el-tag :type="scope.row.goodstype_id === 2 ? 'success' :
                                                                                                    scope.row.goodstype_id === 3 ? 'info' :
                                                                                                    scope.row.goodstype_id === 4 ? 'warning' :
                                                                                                    scope.row.goodstype_id === 5 ? 'danger' : '' " size="small">
                                                        {{scope.row.goodstype_id === 1 ? '普貨' :
                                                        scope.row.goodstype_id === 2 ? '特貨' :
                                                        scope.row.goodstype_id === 3 ? '海運' :
                                                        scope.row.goodstype_id === 4 ? '家具類' :
                                                        scope.row.goodstype_id === 5 ? '一類' : ''}}</el-tag>
                                                </span>
                                            </div>
                                            <div class="goods-description-name">
                                                <!--                                                <img class="goods-small-img"-->
                                                <!--                                                    src="__CDN__/assets/img/pc/new_index/red.png">-->
                                                <!--                                                <span><div class="goods-description-name"></div></span>-->
                                                暂无商品详细信息
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div v-if="scope.row.ser_detail && scope.row.ser_detail.length > 0" class="top-line"></div> -->
                                    <div v-if="scope.row.ser_detail && scope.row.ser_detail.length > 0" class="goods-down">
                                        <el-tag type="danger" style="background: #fdecf0; font-size: 13px; color: #EF436D"
                                                size="small">已下單</el-tag>
                                        <div class="pointer" style="color: #EF436D">
                                            <span class="fs13" style="margin-right: 8px;">附加服務訂單（{{scope.row.ser_detail.length}}）</span>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="可選方案" min-width="16%">
                                <template slot-scope="scope">
                                    <div style="display: flex; gap: 4px; margin-top: 12px">
                                        <div class="goods-svg">
                                            <img alt="" :src="Number(scope.row.scale) > 50 ? transportIcon[2] : transportIcon[0]">
                                        </div>
                                        <div>{{Number(scope.row.scale) > 50 ? '海運' : ' 海快'}}</div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="重量" min-width="16%">
                                <template slot-scope="scope">
                                    <div class="goods-svg" style="margin: 12px 0 0 44px">
                                        <el-image :preview-src-list="previewSrcList"
                                                  src="__CDN__/assets/img/pc/new_index/img.svg"
                                                  style="width: 100%; height: 100%; display: none;">
                                        </el-image>
                                    </div>
                                    <div class="fs16">包裹重量{{forwardNumber(scope.row.scale)}}KG</div>
                                    <div class="fs13 tips-content">剩餘免費倉儲 356 天
                                        <el-tooltip class="item" effect="light" placement="bottom-start">
                                            <template slot="content">
                                                <div class="section-title">倉庫到期說明：</div>
                                                <div style="font-size: 12px; color: #333333; max-width: 292px">
                                                    如您未能在免費倉儲期限內完成集運，系統將自動視為您已放棄包裹並刪除到期包裹。
                                                    請務必在免費倉儲期限內完成集運。
                                                </div>
                                            </template>
                                            <i class="el-icon-question" style="color: #EF436D"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" min-width="18%">
                                <template slot-scope="scope">
                                    <div class="handle-up">
                                        <div class="goods-remarks">
                                            <div>備註：</div>
                                            <div style="color: #22B573">{{scope.row.oremarks || ''}}</div>
                                            <div @click="openRemarks(scope.row)" class="edit pointer"><img
                                                    src="__CDN__/assets/img/pc/new_index/edit_icon.png"></div>
                                        </div>
                                    </div>
                                    <div class="handle-middle">
                                        <div style="display: flex; flex-direction: column; justify-content: center;align-content: center;gap: 22px">
                                            <div>
                                            <span @click.stop="openService(scope.row)"
                                                  class="underline red mr12 fs13 pointer">附加服務</span>
                                                <span @click="openTransfer" class="underline red fs13 pointer">包裹轉寄</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="handle-down">
                                        <div v-if="scope.row.ser_detail && scope.row.ser_detail.length > 0" class="table-other">
                                            <div class="pointer" style="color: #EF436D" @click.stop="openVS(scope.row)">
                                                <span class="underline" style="margin-right: 8px;">查看</span>
                                                <i class="el-icon-view"></i>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!-- <div class="my-footer">
                            <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]"
                                           :total="parcelData.length" @current-change="handleCurrentChange"
                                           @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper">
                            </el-pagination>
                        </div> -->
                    </div>
                </div>
                <div v-if="activeIndex === 2">
                    <div style="width: 100%;">
                        <div class="my-nav">
                            <!-- 左侧标签页，类似el-tabs -->
                            <el-tabs @tab-click="onWarehouseChange" v-model="form.warehouseName" style="flex-grow: 1;">
                                <el-tab-pane :key="item.id" :label="formatTabLabel(item)" :name="item.id"
                                    v-for="(item, index) in warehouseOptions"></el-tab-pane>
                            </el-tabs>
                            <!-- 右侧搜索框 -->
                            <div class="nav-search">
                                <input type="text" class="search-input" v-model="form.orderNum" placeholder="輸入訂單編號"
                                    @keyup.enter="searchParcel">
                                <button class="search-icon" @click="searchParcel">
                                    <i class="el-icon-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div v-loading="tabLoading"  class="loading-container custom-scrollbar" style="max-height: 686px; overflow-y: auto; overflow-x: hidden;">
                        <div v-if="paginatedData.length === 0" style="width: 100%; min-height: 638px;">
                            <el-empty description="暫無數據"></el-empty>
                        </div>
                        <div v-else v-for="item in paginatedData">
                            <div class="await-box"  :key="item.id">
                                <div class="await-tags" :style="{backgroundColor: (item.pay_status === 2 ? '#22b573' :'#E37318')}">{{
                                    item.pay_status === 0 ? '未付款' :
                                    item.pay_status === 1 ? '部分支付' :
                                    item.pay_status === 2 ? '已付款' :
                                    item.pay_status === 3 ? '多支付' : ''}}</div>
                                <div class="Source-Han-Sans await-btn">
                                    <button v-if="item.pay_status == 0" class="packup-btn packup-btn-pay" @click="openPayDialog(item)">去付款</button>
                                    <button class="packup-btn packup-btn-calc" v-if="false">取消訂單</button>
                                </div>
                                <div class="mb8" style="display: flex; justify-content: flex-start; align-items: center; gap: 4px">
                                    <div class="goods-svg">
                                        <img alt="" :src="item.transport.transport === 15 ? transportIcon[0] : 
                                        item.transport.transport === 16 ? transportIcon[1] : transportIcon[2]">
                                    </div>
                                    <div class="c333 fs14 mr8" style="margin-top: 3px;">{{
                                        item.transport.transport === 15 ? '海快' : 
                                        item.transport.transport === 16 ? '空運' : '海運'}}</div>
                                    <div class="c333">
                                        <span class="fs14">訂單編號：</span>
                                        <span class="fs18 fw">{{item.order_no}}</span>
                                    </div>
                                </div>
                                <div class="mb8">
                                    <span class="c666 fs13">下單時間：</span>
                                    <span class="c333 fs13">{{formatDateTime(item.createtime, 2)}}</span>
                                </div>
                                <div style="display: flex; gap: 56px; height: 22px; margin-bottom: 22px; flex-wrap: wrap;">
                                    <div>
                                        <span class="my-tag">申報人</span>
                                        <span class="fs13 c333 ml12">{{item.addr.name}} ({{item.addr.card}})</span>
                                    </div>
                                    <div>
                                        <span class="my-tag">報關方式</span>
                                        <span class="fs13 c333 ml12">{{customsType}}</span>
                                    </div>
                                </div>
                                <div style="min-height: 32px; display: flex; gap: 97px; flex-wrap: wrap;">
                                    <div style="display: flex; gap: 7px">
                                        <div class="addr-svg" style="margin-top: 1px;">
                                            <img alt="" src="__CDN__/assets/img/pc/new_index/parcel09.svg">
                                        </div>
                                        <div class="c333 fs13 fw">
                                            <div>{{item.addr.detail}}</div>
                                            <div> {{item.addr.name}} ({{item.addr.mobile}})</div>
                                        </div>
                                    </div>
                                    <div style="display: flex; gap: 7px; cursor: pointer" @click="changeAddress">
                                        <div class="await-edit-svg" style="margin-top: 2px;">
                                            <img alt="" src="__CDN__/assets/img/pc/new_index/edit_icon.png">
                                        </div>
                                        <div class="fs13 pointer" style="color: #22B573;" @click.stop="editOrderAddr(item.addr.id)">我要修改收貨地址（免費）</div>
                                    </div>
                                </div>
                                <div class="mt12 fs13" v-if="item.remarks">
                                    <span class="c666">備註</span>
                                    <span style="color: #ff9200">{{item.remarks}}</span>
                                </div>
                                <div v-if="isPay" class="ezway-box mt12">
                                    <div class="ezway-alert">
                                        <i class="el-icon-warning-outline" style="color: #E37318"></i>
                                        <span class="c333 fs13">由於您的ezway設置是預先委任，已幫您提前傳輸海關申報接口，請於ezway app確認申報相符，倉庫才能開始打包</span>
                                    </div>
                                    <div class="ezway-btn-row">
                                        <span class="ezway-span ezway-btn-red">請在30S內確認申報相符，超時將取消訂單！</span>
                                        <span class="red underline fs13" @click="handleNoNotice">收不到預先委任通知</span>
                                    </div>
                                    <div class="flex-between">
                                        <a href="" class="ezway-link fs13">如何關閉預先委任？</a>
                                        <button class="ezway-btn ezway-btn-change fs13 Source-Han-Sans"  :disabled="countingDown" @click="startCountdown">{{ countingDown ? `確認中...(${countdown}s)` : '我已確認相符' }}</button>
                                    </div>
                                </div>
                                <div class="dividing-line"></div>
                                <div style="display: flex;justify-content: space-between; align-items: center">
                                    <div style="display: flex; justify-content: flex-start; gap: 137px">
                                        <div style="display: flex; gap: 12px; align-items: center">
                                            <div class="await-svg">
                                                <img src="__CDN__/assets/img/pc/new_index/parcel10.svg" alt="">
                                            </div>
                                            <div class="fs18 c333" style="font-weight: 500">
                                                <span>訂單金額：</span>
                                                <span class="fs18 c666">NT$ <span class="red fs24">{{item.tb_money}}</span></span>
                                            </div>
                                        </div>
                                        <div style="display: flex; gap: 12px; align-items: center">
                                            <div class="await-svg">
                                                <img src="__CDN__/assets/img/pc/new_index/parcel11.svg" alt="">
                                            </div>
                                            <div class="fs18 c333" style="font-weight: 500">
                                                <span>重量：</span>
                                                <span class="fs18 c666"><span class="fs24" style="color: #59DDD1">{{forwardNumber(item.scale)}}</span> KG</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="red" style="display: flex" @click="handleShowMore">
                                        <div class="block-box">{{item.page.length}}個包裹</div>
                                        <div class="pointer">
                                            <span class="fs14">{{!isSwitch ? '展開' : '關閉'}}</span>
                                            <i :class="isSwitch ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="isSwitch" class="packup-dialog-right" style="width: 100%; padding: 0 0;">
                                <div class="packup-goods-list">
                                    <div class="border-goods-item" v-for="(package, index) in item.page" :key="index">
                                        <div class="border-goods-header">
                                            <div class="fs13" style="display: flex; align-items: center; gap: 9px">
                                                <span class="c333" style="margin-right: 4px;">{{index + 1}}</span>
                                                <span style="margin-top: 2px;">
                                                    <i class="iconfont icon-yundakuaidi"></i>
                                                    <svg class="icon fs14" aria-hidden="true">
                                                        <use :xlink:href="package.icon"></use>
                                                    </svg>
                                                </span>
                                                <span class="red">{{package.sname}} <span>{{package.waybill}}</span></span>
                                            </div>
                                            <div class="fw" style="display: flex; align-items: center">包裹總重量：
                                                <div class="red mr12">{{package.scale}}KG</div>
                                                <div style="width: 20px; height: 20px;" v-if="false">
                                                    <img style="width: 100%; height: 100%" src="__CDN__/assets/img/pc/new_index/img.svg" alt="">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="packup-goods-body">
                                            <div class="packup-goods-img">
                                                <img v-if="package.goods_url" :src="item.goods_url" alt="">
                                                <img  v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                                            </div>
                                            <div class="packup-goods-info">
                                                <div class="red fs14">[{{package.goods_name}}]</div>
                                                <div class="c333 fs13">暂无商品详细信息</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="activeIndex === 3">
                    <div style="width: 100%;">
                        <div class="my-nav">
                            <!-- 左侧标签页，类似el-tabs -->
                            <el-tabs @tab-click="onWarehouseChange" v-model="form.warehouseName" style="flex-grow: 1;">
                                <el-tab-pane :key="item.id" :label="formatTabLabel(item)" :name="item.id"
                                    v-for="(item, index) in warehouseOptions"></el-tab-pane>
                            </el-tabs>
                            <!-- 右侧搜索框 -->
                            <div class="nav-search">
                                <input type="text" class="search-input" v-model="form.orderNum" placeholder="输入訂單編號"
                                    @keyup.enter="searchParcel">
                                <button class="search-icon" @click="searchParcel">
                                    <i class="el-icon-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div v-loading="tabLoading"  class="loading-container custom-scrollbar" style="max-height: 686px; overflow-y: auto;">
                        <div v-if="paginatedData.length === 0" style="width: 100%; min-height: 638px;">
                            <el-empty description="暫無數據"></el-empty>
                        </div>
                        <div v-else class="parcel-div-table">
                            <!-- 表头 -->
                            <div class="parcel-table-header">
                                <div class="parcel-table-header-cell">入庫時間/快遞單號/商品信息</div>
                                <div class="parcel-table-header-cell">大陸快遞單號</div>
                                <div class="parcel-table-header-cell">運輸方式</div>
                                <div class="parcel-table-header-cell">商品數量</div>
                                <div class="parcel-table-header-cell">重量</div>
                                <div class="parcel-table-header-cell">操作</div>
                            </div>

                            <!-- 数据行容器 -->
                            <div class="parcel-table-data-container custom-scrollbar">
                                <div v-for="(row, rowIndex) in paginatedData" :key="rowIndex" class="parcel-table-row">
                                <!-- 订单头部信息 -->
                                <div class="parcel-order-header">
                                    <div>
                                        <span class="parcel-goods-tag">已出貨</span>
                                        <span class="parcel-goods-date">{{ formatDate(row.createtime, 2)}}</span>
                                        <span class="parcel-goods-order">訂單號：{{row.order_no || '訂單號異常'}}</span>
                                    </div>
                                    <div v-if="row.order_status == 3" class="parcel-action-delete">
                                        <el-popconfirm title="是否確定刪除該包裹？" @confirm="delOrderRow(row.order_no)">
                                            <template slot="reference">
                                                <div class="parcel-delete-btn">
                                                    <i class="el-icon-delete"></i>
                                                    <span>刪除</span>
                                                </div>
                                            </template>
                                        </el-popconfirm>
                                    </div>
                                </div>

                                <!-- 订单内容区域 -->
                                <div class="parcel-order-content" :style="{ gridTemplateRows: 'repeat(' + row.page.length + ', 80px)' }">
                                    <!-- 商品数据列 -->
                                    <div class="parcel-data-columns">
                                        <div v-for="(item, itemIndex) in row.page" :key="item.id" class="parcel-row-content">
                                            <!-- 入庫時間/快遞單號/商品信息 -->
                                            <div class="parcel-cell parcel-cell-goods">
                                                <div class="parcel-goods-item">
                                                    <div class="parcel-goods-image">
                                                        <img v-if="item.goods_url" :src="item.goods_url" alt="">
                                                        <img v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                                                    </div>
                                                    <div class="parcel-goods-info">
                                                        <div class="parcel-goods-name">[{{item.goods_name}}]</div>
                                                        <div class="parcel-goods-desc">暂无商品详细信息</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 大陸快遞單號 -->
                                            <div class="parcel-cell">
                                                <div class="parcel-courier-item">
                                                    <svg class="icon fs14" aria-hidden="true" style="display: inline;">
                                                        <use :xlink:href="item.icon"></use>
                                                    </svg>
                                                    <span>{{item.sname}}</span>
                                                    <span>{{item.waybill}}</span>
                                                </div>
                                            </div>

                                            <!-- 運輸方式 -->
                                            <div class="parcel-cell">
                                                <div class="parcel-transport-method">
                                                    {{
                                                        row.transport.transport === 15 ? '海快' :
                                                            row.transport.transport === 16 ? '海運' :
                                                                row.transport.transport === 17 ? '空運' : ''
                                                    }}
                                                </div>
                                            </div>

                                            <!-- 商品數量 -->
                                            <div class="parcel-cell">
                                                <div class="parcel-quantity">
                                                    {{item.num}}
                                                </div>
                                            </div>

                                            <!-- 重量 -->
                                            <div class="parcel-cell">
                                                <div class="parcel-weight">
                                                    {{item.scale || 0}}KG
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作列 (独立区域) -->
                                    <div class="parcel-actions-column">
                                        <div class="parcel-actions">
                                            <div class="parcel-confirm-receipt">
                                                <div v-if="row.order_status == 3" style="color: #22b573">已收貨</div>
                                                <el-popconfirm v-else title="是否確定簽收該包裹？" @confirm="OrderReceipt(row)">
                                                    <template slot="reference">
                                                        <div>確認收貨</div>
                                                    </template>
                                                </el-popconfirm>
                                            </div>

                                            <!-- 订单信息移到操作里面 -->
                                            <div class="parcel-order-info">
                                                <div class="parcel-order-item">
                                                    <span class="parcel-order-label">訂單總額：</span>
                                                    <span class="parcel-order-value">NT$ {{row.tb_money}}</span>
                                                </div>
                                                <div class="parcel-order-item">
                                                    <span class="parcel-order-label">稅務狀態：</span>
                                                    <span class="parcel-order-value">{{
                                                        row.transport.style === 1 ? '不包稅' : row.style === 2 ? '包頻繁稅' : '全包稅'
                                                        }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="parcel-row-logistics">
                                    <!-- 台湾物流信息 -->
                                    <div class="parcel-footer-logistics pointer">
                                        <img v-if="row.wbname" :src="row.wbname === 'tcat' ? TWLogisticsIcon[1] : row.wbname === 'hct' ? TWLogisticsIcon[2] :  TWLogisticsIcon[0]" class="mr8" alt="">
                                        <span v-if="row.wbname" class="parcel-footer-logistics-number">{{
                                            row.wbname === 'tcat' ? '黑貓' : row.wbname === 'hct' ? '新竹' : row.wbname === 'ktj' ? '大榮' : ''
                                        }} {{row.waybill}}</span>
                                        <span></span>
                                        <span v-if="row.wbname" class="dot"></span>
                                        <el-tooltip
                                                    class="item"
                                                    effect="light"
                                                    placement="bottom-start"
                                                    :visible-arrow="false"
                                                    transition="el-fade-in-linear"
                                                    popper-class="logistics-tooltip"
                                                    @hide="TWLogistics = []"
                                        >
                                            <template slot="content">
                                                <div class="logistics-box">
                                                    <el-timeline>
                                                        <el-timeline-item 
                                                            :color="item.color" 
                                                            :icon="item.icon" 
                                                            :key="index" 
                                                            :size="item.size"
                                                            :timestamp="item.date" 
                                                            :type="index === 0 ? 'success' : '' " 
                                                            :class="index === 0 ? 'active-timestamp' : 'custom-timestamp' "
                                                            placement="top" 
                                                            v-for="(item, index) in TWLogistics">
                                                            <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: center; gap: 8px;">
                                                                <span v-if="item.status" class="fs13 fw" :style="{ color: index === 0 ? '#67c23a' : '#333333' }">[{{item.status}}]</span>
                                                                <span v-if="item.addr" class="fs13" :style="{ color: index === 0 ? '#67c23a' : '#333333' }">{{item.addr}}</span>
                                                                <span v-if="item.context" class="fs13 c666">{{item.context}}</span>
                                                            </div>
                                                        </el-timeline-item>
                                                    </el-timeline>
                                                </div>
                                            </template>
                                            <span @mouseenter="openTWLogistics(row)">
                                                <span v-if="getTwInfo(row, 'date')">（{{getTwInfo(row, 'date')}}）</span>
                                                <span v-if="getTwInfo(row, 'status')" class="parcel-footer-logistics-status" >【{{getTwInfo(row, 'status')}}】</span>
                                                <span v-if="getTwInfo(row, 'addr')">{{getTwInfo(row, 'addr')}}</span>
                                                <span v-if="getTwInfo(row, 'context')">{{getTwInfo(row, 'context')}}</span>
                                            </span>
                                        </el-tooltip>

                                        <div v-if="row.wbname" class="logistics-share pointer" @click="shareLogistics(row)">
                                            <i  class="el-icon-share" style="display: inline; font-size: 20px; color: #666666;"></i>
                                        </div>
                                    </div>
                                    <!-- 申报人信息 -->
                                    <div class="parcel-footer-declarant">
                                        <!-- 打包后重量 -->
                                         <div class="parcel-footer-weight">
                                            <span>打包後重量：</span>
                                            <span class="parcel-footer-weight-value">{{calculateWeight(row.scale)}}KG</span>
                                         </div>
                                        <div style="width: 297px; padding-left: 16px;">
                                            <span>申報人：</span>
                                            <span class="parcel-footer-declarant-info">{{row.addr.name}}-({{row.addr.card}})</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="parcel-row-remarks">
                                    <span class="c666">備註：</span>
                                    <span class="c333">{{row.remarks}}</span>
                                </div>
                            </div>
                            </div> <!-- 关闭 parcel-table-data-container -->
                        </div>
                        <!-- <div class="my-footer">
                            <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]"
                                           :total="parcelData.length" @current-change="handleCurrentChange"
                                           @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper">
                            </el-pagination>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
        <!--     编辑备注-->
        <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="remarksDialogVisible"
                   class="forward" right title="商品備註" width="1008px">
            <div class="service-content" style="width: 100%; margin-bottom: 24px">
                <div class="goods-middle flex">
                    <div class="goods-img" style="background: #FFFFFF">
                        <img v-if="goodsInfo.goods_url" :src="goodsInfo.goods_url" alt="">
                        <img  v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                    </div>
                    <div class="service-info">
                        <div class="goods-name inline-block" style="height: 20px">
                            <span class="goods-short-name" style="margin-right: 6px">[{{goodsInfo.goods_name}}]</span>
                            <svg class="icon fs14" aria-hidden="true">
                                <use :xlink:href="goodsInfo.icon"></use>
                            </svg>
                            <span>暂无商品详细信息</span>
                        </div>
                        <div>
                            <!--                            <span style="margin-right: 24px">商品價格：{{goodsInfo.unit_price}}元</span>-->
                            <span>數量：{{goodsInfo.num}}</span>
                        </div>
                    </div>

                </div>
                <div v-if="goodsInfo.insure > 0 " class="insured-price-btn">已保價</div>
                <div v-if="goodsInfo.insure > 0 " class="insured-price">
                    <span>已保價：28.89元</span>
                    <span>【免費保價】</span>
                </div>
            </div>
            <div style="height: 30px; display: flex; flex-direction: column; gap: 8px; margin-bottom: 50px">
                <div style="color: #333333">備註</div>
                <div>
                    <el-input maxlength="20" placeholder="請填寫備註信息" style="width: 100%"
                              v-model="oremarks">
                    </el-input>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="remarksClose" class="pd30-btn" size="small">取 消</el-button>
                <el-button @click="remarksSubmit" class="pd30-btn" size="small" style="background: #EF436D"
                           type="danger">確 定</el-button>
            </span>
        </el-dialog>
        <!--    附加服務-->
        <el-dialog :close-on-click-modal="false" :visible.sync="serviceDialogVisible"
                   class="other-service" right title="編輯附加服務" width="968px">
            <div class="custom-scrollbar" style="max-height: 560px; overflow-y: auto; width: 100%">
                <div class="service-content" >
                    <div class="goods-middle flex">
                        <div class="goods-img">
                            <img v-if="goodsInfo.goods_url" :src="goodsInfo.goods_url">
                            <img  v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                        </div>
                        <div class="service-info">
                            <div class="goods-name inline-block" style="height: 20px">
                                <span class="goods-short-name" style="margin-right: 6px">[{{goodsInfo.goods_name}}]</span>
                            </div>
                            <div>
                                <!--                            <span style="margin-right: 24px">商品價格：{{goodsInfo.unit_price}}元</span>-->
                                <span class="fs13 c333">暫無商品詳細信息</span>
                            </div>

                        </div>
                    </div>
                    <div v-if="goodsInfo.insure > 0 " class="insured-price-btn">已保價</div>
                    <div class="insured-num fs13 c666">數量：{{goodsInfo.num}}</div>
                    <div v-if="goodsInfo.insure > 0 " class="insured-price">
                        <!--                    <span>已保價：{{goodsInfo.insure}}元</span>-->
                        <span class="fs13 c666">【免費保價】</span>
                    </div>
                </div>
                <div v-if="addServiceList.length > 0" class="sub-title" style="justify-content: space-between">
                    <div style="display: flex">
                        <div class="sub-line"></div>
                        <div>已入庫附加服務</div>
                    </div>
                    <div class="sub-text">* 以下服務皆以臺幣計價</div>
                </div>
                <div v-if="addServiceList.length > 0" class="service-box2" style="margin-bottom: 24px">
                    <div class="float-window2">
                        <div>已選附加服務</div>
                    </div>
                    <div class="expand-box">
                        <div v-for="(item, index) in addServiceList" :key="index">
                            <!--                        <div class="expand-toggle" @click="toggleExpand(index)">-->
                            <div class="expand-toggle">
                                <div>
                                    <el-tag type="danger" style="background: #fdecf0; color: #EF436D">已下單</el-tag>
                                    <span class="ml12">{{item.title}}：</span>
                                    <span class="fw c333 fs13">{{item.amount}}元</span>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="sub-title" style="justify-content: space-between">
                    <div style="display: flex">
                        <div class="sub-line"></div>
                        <div>{{addServiceList.length > 0 ? '新增' : '選擇'}}附加服務</div>
                    </div>
                    <div v-if="activeIndex === 0" class="sub-text">* 以下服務皆以臺幣計價</div>
                </div>
                <div class="service-box" style="margin-bottom: 24px">
                    <div class="service-box-title">
                        <span style="color: #333333">*開箱費</span>
                        <span style="color: #666666">（基礎服務費50元）</span>
                    </div>
                    <div class="check-box">
                        <el-checkbox-group @change="updateCheckService" style="display: flex"
                                           v-model="firstSelectedCheckList">
                            <div :key="index" v-for="(item, index) in firstCheckList">
                                <el-checkbox :label="item" style="margin-right: 48px">
                                    <div @click.stop="openInservice(item)">
                                        <el-tooltip effect="light" placement="bottom-start"
                                                    v-if="item.content && (Array.isArray(item.content) ? item.content.length > 0 : Object.keys(item.content).length > 0)">
                                            <template slot="content">
                                                <div class="custom-tooltip server-tooltip">
                                                    <template v-if="Array.isArray(item.content)">
                                                        <div v-for="(content, index) in item.content" :key="index">
                                                            <div v-if="content.ctitle" class="section-title">
                                                                {{content.ctitle}}
                                                            </div>
                                                            <div v-if="content.content" class="section-content"
                                                                 v-html="content.content"></div>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div v-for="(content, key) in item.content" :key="key">
                                                            <div v-if="content.ctitle" class="section-title">
                                                                {{content.ctitle}}
                                                            </div>
                                                            <div v-if="content.content" class="section-content"
                                                                 v-html="content.content"></div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
                                            <i class="el-icon-question" style="color: #C7C7C7; font-size: 12px"></i>
                                        </el-tooltip>
                                        <span class="fs13 c333">{{ item.title }}</span>
                                    </div>
                                    <div class="fs12 c999">{{ item.descr}}</div>
                                </el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </div>
                    <div class="float-window">
                        <div class="parcel-icon">
                            <img alt="" src="__CDN__/assets/img/pc/new_index/parcel_icon.png">
                        </div>
                        <div>{{serviceNames[0]}}</div>
                    </div>
                </div>
                <div class="service-box2" style="height: 128px;">
                    <div class="check-box">
                        <el-checkbox-group @change="updateCheckService" style="display: flex"
                                           v-model="secondSelectedCheckList">
                            <div :key="index" v-for="(item, index) in secondCheckList">
                                <el-checkbox :label="item" style="margin-right: 48px">
                                    <div @click.stop="openInservice(item)">
                                        <el-tooltip effect="light" placement="bottom-start"
                                                    v-if="item.content && (Array.isArray(item.content) ? item.content.length > 0 : Object.keys(item.content).length > 0)">
                                            <template slot="content">
                                                <div class="custom-tooltip server-tooltip">
                                                    <template v-if="Array.isArray(item.content)">
                                                        <div v-for="(content, index) in item.content" :key="index">
                                                            <div v-if="content.ctitle" class="section-title">
                                                                {{content.ctitle}}
                                                            </div>
                                                            <div v-if="content.content" class="section-content"
                                                                 v-html="content.content"></div>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div v-for="(content, key) in item.content" :key="key">
                                                            <div v-if="content.ctitle" class="section-title">
                                                                {{content.ctitle}}
                                                            </div>
                                                            <div v-if="content.content" class="section-content"
                                                                 v-html="content.content"></div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
                                            <i class="el-icon-question" style="color: #C7C7C7; font-size: 12px"></i>
                                        </el-tooltip>
                                        <span class="fs13 c333">{{ item.title }}</span>
                                    </div>
                                    <div class="fs12 c999">{{ item.descr }}</div>
                                </el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </div>
                    <div class="float-window2">
                        <div class="parcel-icon">
                            <img alt="" src="__CDN__/assets/img/pc/new_index/parcel_icon.png">
                        </div>
                        <div>{{serviceNames[1]}}</div>
                    </div>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="closeService" class="pd30-btn" size="small">取 消</el-button>
                <el-button @click="confirmService" class="pd30-btn" size="small" style="background: #EF436D"
                           type="danger">確 定</el-button>
            </span>
        </el-dialog>
        <!--    編輯附加服務二級彈窗-->
        <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="inServiceDialogVisible"
                   class="forward" right :title="currentDialogItem.title" width="514px">
            <div class="service-dialog-content">
                <!-- 标题 -->
                <div class="dialog-title">
                    {{getDialogTitle}}
                </div>

                <!-- 包装服务信息 -->
                <div v-if="isPackagingService">
                    <div class="service-info-row">
                        <div class="info-label">服務項目：</div>
                        <div class="info-value">{{currentDialogItem.title}}</div>
                    </div>
                    <div class="service-info-row">
                        <div class="info-label">服務內容：</div>
                        <div class="info-value">於快遞包裹外箱包上{{currentDialogItem.title === '包裝氣泡棉' ? '氣泡棉'
                            : '氣柱袋'}}
                        </div>
                    </div>
                    <div class="service-info-row">
                        <div class="info-label">範例圖：</div>
                        <div class="example-images">
                            <div class="example-image">
                                <img src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                            </div>
                            <div>
                                <img src="__CDN__/assets/img/pc/new_index/right_arrow.svg" alt="">
                            </div>
                            <div class="example-image">
                                <img src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数量输入 -->
                <div v-if="currentDialogItem.title === '清點數量'" class="input-section">
                    <el-input-number v-model="goodsNum.count" :min="1" :max="50">
                    </el-input-number>
                </div>

                <!-- 拆分数量输入 -->
                <div v-if="currentDialogItem.title === '包裹拆分'" class="input-section">
                    <el-input-number v-model="goodsNum.deCount" :min="0" :max="50">
                    </el-input-number>
                </div>

                <!-- 备注输入 -->
                <div v-if="showRemarksInput" class="input-section">
                    <el-input maxlength="100" placeholder="可在此留言注意事項給倉庫作業人員" type="textarea" v-model="oremarks">
                    </el-input>
                </div>

                <!-- 拆分说明 -->
                <div v-if="currentDialogItem.title === '包裹拆分'" class="split-notice">
                    <div class="notice-title red">*</div>
                    <div class="notice-item">1. 如無特殊要求將依重量平均拆分包裹；</div>
                    <div class="notice-item">2. 真空壓縮包裝的棉被，枕頭等不適用拆包服務；</div>
                    <div class="notice-item">3. 一般多用透明袋分裝，無法指定包材，倉庫會視情況處理；</div>
                    <div class="notice-item">4. 包裹拆分為5件(含)以上時，將轉為商業件入庫，只能使用不包稅下單。</div>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="closeInService" class="pd30-btn" size="small">取 消</el-button>
                <el-button @click="inServiceDialogVisible = false" class="pd30-btn" size="small"
                           style="background: #EF436D" type="danger">確 定</el-button>
            </span>
        </el-dialog>
        <!--    查看附加服務二級彈窗-->
        <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="vsDialogVisible" class="forward"
                   right :title="`包裹增值服務訂單${goodsInfo.entrust_no}`" width="514px">
            <div class="service-dialog-content">
                <!-- 标题 -->
                <div class="red fs13">
                    <span>{{goodsInfo.wbill_name}}</span>
                    <span>{{goodsInfo.waybill}}</span>
                </div>
                <div
                        style="height: 1px; background: #D8D8D8; margin-top: 16px; width: calc(100% + 40px); margin-left: -20px;">
                </div>
                <div>
                    <ul class="service-item">
                        <li v-for="item in addServiceList" :key="item.title">
                            <div class="service-row">
                                <div>
                                    <span class="fs13 c666" style="margin-right: 8px;">{{ item.title }}：</span>
                                    <span class="fs13 c333">{{ item.amount }}元</span>
                                </div>
                                <div>
                                    <el-tag type="danger" size="small"
                                            style="background: #fdecf0; color: #EF436D">{{item.status === 0 ? '已下單' :
                                        '進行中'}}</el-tag>
                                </div>
                            </div>
                            <div class="service-box2-content">
                                <div class="service-info-item" v-for="(ite, idx) in item.log" :key="index">
                                    <div v-if="ite.img" class="service-info-title">
                                        <img class="service-img" :src="ite.img" />
                                    </div>
                                    <div class="service-info-list">
                                        <ul class="service-info-ul">
                                            <li>
                                                <div>
                                                    <i class="el-icon-star-on"
                                                       style="color: #EF436D; margin-right: 8px;"></i>
                                                    <span>{{ite.content}}</span>
                                                </div>
                                                <div>
                                                    <i class="el-icon-success"
                                                       style="color: #22B573; font-size: 18px;"></i>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="closeVS" class="pd30-btn" size="small">返 回</el-button>
            </span>
        </el-dialog>
        <!--    附加服務支付彈窗-->
        <el-dialog :visible.sync="payServiceDialogVisible" width="514px" :close-on-click-modal="false"
                   :show-close="false" right class="forward" title="附加服務訂單確認">
            <div style="display: flex">
                <div class="sub-line"></div>
                <div class="fs14 red">新增附加服務</div>
            </div>
            <div class="show-add-service">
                <div class="flex-between" v-for="(item, index) in selectAddServiceList" :key="index">
                    <span class="fs13 c666">{{item.title}}：</span>
                    <span class="fs13 c333">{{item.amount}}元</span>
                </div>
            </div>
            <div class="red fs13" style="margin: 0 24px; display: flex; align-items: flex-start">
                <el-checkbox v-model="isAgree" class="forecast-radio" style="margin-top: 3px"></el-checkbox>
                <span :style="isAgree ? {color: '#EF436D'} : {color: '#666666' }"
                      style="margin-left: 4px">我已了解附加服務訂單付款後即通知倉庫加入作業排程，一旦確認無法更改、取消、退款</span>
            </div>
            <div style="display: flex; position: relative; margin: 12px 12px 12px 0">
                <div class="sub-line"></div>
                <div class="fs14 red">支付方式</div>
                <div style="position: absolute; right: 0; font-size: 14px; color: #333333">T幣（剩餘：{{uMoney}}）</div>
            </div>
            <div class="flex-between" style="margin: 0 24px">
                <span class="fs13 c666">應付：</span>
                <span class="red fs18 fw">{{tAmount}}元</span>
            </div>
            <div v-if="uMoney < tAmount" class="flex-between" style="margin: 12px 24px">
                <span class="required">*<span class="fs13" style="color: #FF9200; margin-left: 2px">T幣餘額不足</span></span>
                <a href="/index/recharge/index" class="red fs14 underline">立即儲值</a>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="closePayService" class="pd30-btn" size="small">取 消</el-button>
                <el-button @click="openPayService" class="pd30-btn" size="small" type="danger"
                           :disabled="uMoney < tAmount" :style="payBtnStyle">支 付</el-button>
            </span>
        </el-dialog>
        <!--        支付彈窗-->
        <el-dialog :visible.sync="payDialogVisible" width="514px" class="custom-dialog" :show-close="true"
                   :close-on-click-modal="false" :close-on-press-escape="false" title="支付訂單" center>
            <!-- 支付金额盒子 -->
            <div class="pay-box">
                <div class="pay-amount-title">支付金額</div>
                <div class="pay-amount">NT$ <span>{{ orderForm.totalAmount || orderRow.tb_money || tAmount }}</span></div>
            </div>
            <!-- 支付方式 -->
            <!--            <div class="pay-method-title" style="margin-bottom: 16px">支付方式</div>-->
            <div class="sub-title relative" style="margin: 16px 0 16px 14px; width: 94%">
                <div class="sub-line"></div>
                <div style="margin-left: -4px">支付方式</div>
                <div class="pay-method-info absolute" style="top: 0; right: 0">
                    T幣（剩餘：{{uMoney}}）
                </div>
            </div>
            <div v-if="(Number(orderForm.totalAmount) || Number(orderRow.tb_money )|| Number(tAmount) ) > Number(uMoney)" class="flex-between" style="margin: 12px 24px">
                <span class="fs13 c333">* T幣餘額不足</span>
                <a href="/index/recharge/index" class="red fs14 underline">立即儲值</a>
            </div>
            <!-- 验证码输入 -->
            <!-- <div  v-if="Number(uMoney) > Number(orderForm.totalAmount || orderRow.tb_money)">
                <div class="pay-password-box">
                    <span class="red fs14">* 已向您綁定的{{maskMobile(uMobile)}}手機發送驗證碼</span>
                    <span v-if="!isCountingDown" class="red fs14 underline pointer" @click="sendSmsCode">重新發送</span>
                    <span v-else class="c666 fs14">{{countdown}}秒後可重新發送</span>
                </div>
                <div class="pay-password-inputs">
                    <input v-for="(num, idx) in 4" :key="idx" class="pay-password-input" type="text" maxlength="1"
                           inputmode="numeric" pattern="[0-9]*" v-model="verifyCode[idx]" :disabled="idx !== currentInput"
                           @focus="onFocus(idx)" @input="onInput(idx, $event)" @keydown.backspace="onBackspace(idx, $event)"
                           ref="inputs" autocomplete="off" />
                </div>
            </div> -->
            <!-- 密碼輸入支付 -->
            <div  v-else>
                <div class="pay-password-box">
                    <span style="color:#ff4d7a;font-size:14px;">* 請輸入6位錢包密碼</span>
                </div>
                <div class="pay-password-inputs">
                    <input v-for="(num, idx) in 6" :key="idx" class="pay-password-input" type="password" maxlength="1"
                           inputmode="numeric" pattern="[0-9]*" v-model="password[idx]" :disabled="idx !== currentInput"
                           @focus="onFocus(idx)" @input="onInput(idx, $event)" @keydown.backspace="onBackspace(idx, $event)"
                           ref="inputs" autocomplete="off" />
                </div>
            </div>
        </el-dialog>

        <!--    包裹轉寄-->
        <el-dialog :close-on-click-modal="false" :visible.sync="firstForwardDialogVisible" center class="forward"
                   color="red" title="包裹轉寄" width="1008px">
            <div class="sub-title" style="margin-bottom: 24px">
                <div class="sub-line"></div>
                <div>選擇轉運配送方式</div>
            </div>
            <div class="forward-content">
                <div @click="openFirstRegistration" class="forward-box-first">
                    <div class="logo">
                        <img alt="" src="__CDN__/assets/img/pc/logo01.png">
                    </div>
                    <div class="txt-one fs18">倉庫代寄</div>
                    <div class="txt-second fs13">由倉庫預約快遞寄出<br>請提供完整收件資料</div>
                    <div class="txt-third">收費標准：</div>
                    <div class="txt-fourth">首重
                        <span class="red">55元/KG</span>，續重
                        <span class="red">35元/KG</span>
                    </div>
                </div>
                <div @click="openSecondRegistration" class="forward-box-second">
                    <div class="logo">
                        <img alt="" src="__CDN__/assets/img/pc/logo02.png">
                    </div>
                    <div class="txt-one fs18">快遞自取</div>
                    <div class="txt-second fs13">自行聯係快遞至倉庫取件<br>須提供取件碼核對使用</div>
                    <div class="txt-third">收費標准：</div>
                    <div class="txt-fourth">包裹下架移交處理費
                        <span class="red">40元</span>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="secondForwardDialogVisible"
                   class="forward" right title="包裹轉寄" width="1008px">
            <div class="to-back red"><i @click="closeFirstRegistration" class="el-icon-arrow-left">返回</i>
            </div>
            <div class="sub-title">
                <div class="sub-line"></div>
                <div>轉寄收件資料填寫</div>
            </div>
            <el-form :model="ruleForm" :rules="rules" class="demo-ruleForm" label-position="left" label-width="150px"
                     ref="ruleForm">
                <div class="form-flex">
                    <div>
                        <el-form-item label="收件人姓名" prop="userName">
                            <el-input v-model="ruleForm.userName"></el-input>
                        </el-form-item>
                        <el-form-item label="收件人手機號" prop="mobile">
                            <el-input v-model="ruleForm.mobile"></el-input>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="收件人詳細地址" prop="address">
                            <el-input v-model="ruleForm.address"></el-input>
                        </el-form-item>
                        <el-form-item label="備註" prop="remarks">
                            <el-input v-model="ruleForm.remarks"></el-input>
                        </el-form-item>
                    </div>
                </div>
                </el-row>
            </el-form>
            <div class="tips"><span
                    style="color: #D54941">注意事項：</span>請確認填寫收件地址資料且聯絡收件方，若因資料有誤或者收件方拒收導致包裹未能順利送達，轉寄費用將無法退還。
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="closeFirstRegistration" class="pd30-btn" size="small">取 消</el-button>
                <el-button @click="addresseeSubmit" class="pd30-btn" size="small" style="background: #EF436D"
                           type="danger">確 定</el-button>
            </span>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="thirdForwardDialogVisible"
                   class="forward" right title="包裹轉寄" width="1008px">
            <div class="to-back red"><i @click="closeSecondRegistration" class="el-icon-arrow-left">返回</i>
            </div>
            <div class="sub-title">
                <div class="sub-line"></div>
                <div>取件地址</div>
            </div>
            <div class="address-info">
                <div>联络人：霏霏-(paybei-10518)</div>
                <div>联系电话：15989852261</div>
                <div>邮政编号：518101</div>
                <div>收货地址:深圳市宝安区松岗街道碧头金嘉利工业区5栋飞扬转运仓-(会员ID：10518+19831)</div>
            </div>
            <el-form :inline="true" :model="ruleForm" :rules="rules" class="demo-ruleForm" label-position="left"
                     label-width=160px" ref="ruleForm" style="margin: 0 0 20px 24px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="填寫取件碼" prop="pickUpCode">
                            <el-input v-model="ruleForm.pickUpCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="備註" prop="remarks">
                            <el-input v-model="ruleForm.remarks"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                </el-row>
            </el-form>
            <div class="tips"><span
                    style="color: #D54941">注意事項：</span>請確認填寫收件地址資料且聯絡收件方，若因資料有誤或者收件方拒收導致包裹未能順利送達，轉寄費用將無法退還。
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="closeSecondRegistration" class="pd30-btn" size="small">取 消</el-button>
                <el-button @click="thirdForwardDialogVisible = false" class="pd30-btn" size="small"
                           style="background: #EF436D" type="danger">確 定</el-button>
            </span>
        </el-dialog>
        <!-- 選擇申報人 -->
        <el-dialog :close-on-click-modal="false" :visible.sync="applicantDialogVisible" center class="forward"
                   color="red" title="選擇申報人" width="514">
            <div class="forward-content">
                <div @click="openApplicant" class="forward-box-onlyone">
                    <div class="bg">
                        <img alt="" src="__CDN__/assets/img/pc/new_index/bg01.png">
                    </div>
                    <div class="txt-one fs18">申報人</div>
                </div>
            </div>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" :visible.sync="applicantListDialogVisible" right class="forward" color="red"
                   title="申報人" width="514">
            <div class="custom-tabs">
                <el-tabs v-model="activeChange" @tab-click="changeApplicantType">
                    <el-tab-pane label="個人" name="1"></el-tab-pane>
                    <el-tab-pane label="公司" name="2"></el-tab-pane>
                </el-tabs>
            </div>
            <div class="applicant-content">
                <div class="input-serach">
                    <el-input width="100%" placeholder="搜索姓名/手機號/身份證號" v-model="searchValue" :loading="searchLoading"
                              @keyup.enter.native="searchApplicant">
                        <template #suffix>
                            <el-button icon="el-icon-search" style="color: #333333;" @click="searchApplicant"
                                       type="text"></el-button>
                        </template>
                    </el-input>
                </div>
                <div class="applicant-list">
                    <!-- 空数据时显示 el-empty -->
                    <el-empty
                            v-if="(activeChange === '1' && applicantList.length < 1) || (activeChange === '2' && companyList.length < 1)"
                            :description="activeChange === '1' ? '暫無個人申報人~' : '暫無公司申報人~'">
                    </el-empty>
                    <!-- 有数据时显示列表 -->
                    <div v-if="activeChange === '1' && applicantList.length > 0" style="display: flex; flex-direction: column; gap: 4px;">
                        <div class="applicant-item" v-for="item in applicantList" :key="item.id" @click="selectApplicant(item)"
                             :data-id="item.id">
                            <el-avatar src="" size="medium" style="margin-right: 16px;">{{item.username}}</el-avatar>
                            <div class="applicant-info">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span class="c333 fs13">{{ item.username }} ({{ item.mobile }})</span>
                                    <el-tag v-if="item.status === 1" type="success" size="mini" effect="plain"
                                            style="background: #e6f9f0; color: #22b573">
                                        <i class="el-icon-circle-check" style="color: #22B573; font-size: 10px;"></i>
                                        <span>EZWAY認證</span>
                                    </el-tag>
                                    <el-tag v-else type="info" size="mini" effect="plain">
                                        <i class="el-icon-error" style="font-size: 10px;"></i>
                                        <span>未通過認證</span>
                                    </el-tag>
                                </div>
                                <div class="fs13 c333" style="margin-top: 2px;">{{ item.card }}</div>
                            </div>
                            <i class="el-icon-close applicant-close"></i>
                        </div>
                    </div>
                    <div v-if="activeChange === '2' && companyList.length > 0" style="display: flex; flex-direction: column; gap: 4px;">
                        <div class="applicant-item company-item" v-for="item in companyList" :key="item.id" @click="selectApplicant(item)" :data-id="item.id">
                            <el-avatar src="__CDN__/assets/img/pc/new_index/parcel12.svg" size="medium"
                                       style="margin-right: 16px;"></el-avatar>
                            <div class="applicant-info">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span class="c333 fs13">{{ item.co_code }}</span>
                                    <el-tag v-if="item.ezway" type="success" size="mini" effect="plain"
                                            style="background: #e6f9f0; color: #22b573; border-radius: 3px;;">
                                        <i class="el-icon-circle-check" style="color: #22B573; font-size: 10px;"></i>
                                        <span>EZWAY認證</span>
                                    </el-tag>
                                </div>
                                <div class="fs13 c333" style="margin-top: 2px;">{{ item.mobile }}</div>
                            </div>
                            <i class="el-icon-close applicant-close"></i>
                        </div>
                    </div>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button size="mini" @click="addApplicant" type="success" icon="el-icon-circle-plus-outline"
                           style="background: #22B573; border: none;">{{activeChange === '1' ? '新增個人申報人' :
                    '新增公司申報人'}}</el-button>
                <el-button size="mini" type="danger" style="background: #EF436D" icon="el-icon-circle-plus-outline" @click="openAddrList">下一步</el-button>
            </span>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" :visible.sync="addApplicantDialogVisible" right class="forward"
                   color="red" title="新增申報人" width="514">
            <el-form v-if="activeChange === '1'" class="add-applicant-form" :model="addApplicantForm"
                     :rules="addApplicantRules" ref="addApplicantForm" label-width="100px" style="margin-top: 24px;">
                <el-form-item label="姓名" prop="name" required>
                    <span style="color: #EF436D; margin-right: 4px;">*</span>
                    <el-input v-model="addApplicantForm.name" placeholder="請輸入收件人姓名"></el-input>
                </el-form-item>
                <el-form-item label="手機號碼" prop="mobile" required>
                    <span style="color: #EF436D; margin-right: 4px;">*</span>
                    <el-input v-model="addApplicantForm.mobile" placeholder="請輸入手機號碼"></el-input>
                </el-form-item>
                <el-form-item label="身份證號" prop="idCard" required>
                    <span style="color: #EF436D; margin-right: 4px;">*</span>
                    <el-input v-model="addApplicantForm.idCard" placeholder="請輸入收件人身份證號"></el-input>
                </el-form-item>

            </el-form>
            <el-form v-else class="add-applicant-form" :model="addCompanyForm" :rules="rules"
                     ref="addCompanyForm" label-width="100px" style="margin-top: 24px;">
                <el-form-item label="公司编码" prop="companyCode" required>
                    <span style="color: #EF436D; margin-right: 4px;">*</span>
                    <el-input v-model="addCompanyForm.companyCode" placeholder="..."></el-input>
                </el-form-item>
                <el-form-item label="手機門號" prop="companyMobile" required>
                    <span style="color: #EF436D; margin-right: 4px;">*</span>
                    <el-input v-model="addCompanyForm.companyMobile" placeholder="..."></el-input>
                </el-form-item>
            </el-form>
            <div style="display: flex; justify-content: flex-end; gap: 24px; margin-top: 32px;">
                <el-button size="mini" @click="closeAddApplicant">取消</el-button>
                <el-button size="mini" type="danger" style="background: #EF436D;" @click="submitAddApplicant">確定</el-button>
            </div>
        </el-dialog>
        <!-- 收貨地址 -->
        <el-dialog :close-on-click-modal="false" :visible.sync="addrlsDialogVisible" right class="forward" color="red"
                   title="選擇收貨地址" width="514">
            <div>
                <div class="address-item" style="position:relative; background: #FFF7F9;">
                    <el-avatar src="" size="medium" style="margin-right: 16px;">{{userInfo.username}}</el-avatar>
                    <div class="applicant-info">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span class="c333 fs13">{{ userInfo.username }} {{ userInfo.mobile }}</span>
                        </div>
                        <div class="fs13 c333" style="margin-top: 2px;">{{ userInfo.card }}</div>
                    </div>
                    <span class="red fs13 underline pointer" style="position: absolute; top: 12px; right: 16px" @click="openApplicant">重新選擇</span>
                </div>
                <div class="input-serach mb16">
                    <el-input width="100%" placeholder="搜索姓名/手機號/身份證號" v-model="searchValue" :loading="searchLoading"
                              @keyup.enter.native="searchApplicant">
                        <template #suffix>
                            <el-button icon="el-icon-search" style="color: #333333;" @click="searchApplicant"
                                       type="text"></el-button>
                        </template>
                    </el-input>
                </div>
                <div style="display: flex; flex-direction: column; gap: 4px;">
                    <div v-for="item in addressList" :key="item.id">
                        <div @click="selectAddress(item)" class="address-item" :data-id="item.id">
                            <span class="c333 fs13 fw">{{ item.detail }}
                                <el-tag class="ml8" v-if="item.is_def" type="danger" style="font-size: 10px; font-weight: normal;" size="mini">默認地址</el-tag>
                            </span>
                            <span class="c333 fs13">{{ item.name }} ({{ item.mobile }})
                                <el-tag class="ml8"
                                        :type="item.lname === '本人' ? 'warning' : 'info' " size="mini" effect="plain"
                                        :style="item.lname === '本人' ? {background: '#FFF1E9', color: '#E37318'} : {background: '#F4F4F4', color: '#666666'}"
                                >{{item.lname}}</el-tag>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="openAddAddress" size="mini" type="success" icon="el-icon-circle-plus-outline"
                           style="background: #22B573; border: none;">新增收貨地址</el-button>
                <el-button v-if="applicantList && applicantList.length > 0" size="mini" type="danger" style="background: #EF436D"  icon="el-icon-circle-plus-outline" @click="orderId ? openOrderPack() : openTraOrder()">下一步</el-button>
            </span>
        </el-dialog>

        <!-- 订单收货地址 -->
        <el-dialog :close-on-click-modal="false" :visible.sync="orderAddrDialogVisible" right class="forward" color="red"
                   title="選擇收貨地址" width="514">
            <div>
                <div style="display: flex; flex-direction: column; gap: 4px;">
                    <div v-for="item in addressList" :key="item.id">
                        <div @click="selectAddress(item)" class="address-item" :data-id="item.id">
                            <span class="c333 fs13 fw">{{ item.detail }}
                                <el-tag class="ml8" v-if="item.is_def" type="danger" style="font-size: 10px; font-weight: normal;" size="mini">默認地址</el-tag>
                            </span>
                            <span class="c333 fs13">{{ item.name }} ({{ item.mobile }})
                                <el-tag class="ml8"
                                        :type="item.lname === '本人' ? 'warning' : 'info' " size="mini" effect="plain"
                                        :style="item.lname === '本人' ? {background: '#FFF1E9', color: '#E37318'} : {background: '#F4F4F4', color: '#666666'}"
                                >{{item.lname}}</el-tag>
                            </span>
                            <span class="editAddr">
                                <i class="el-icon-edit" style="color: #22b573;"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="openAddAddress" size="mini" type="success" icon="el-icon-circle-plus-outline"
                           style="background: #22B573; border: none;">新增收貨地址</el-button>
                <el-button v-if="applicantList && applicantList.length > 0" size="mini" type="danger" style="background: #EF436D"  icon="el-icon-circle-plus-outline">下一步</el-button>
            </span>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" :visible.sync="addAddressDialogVisible" right class="forward" color="red"
                   title="新增收件人" width="1008">
            <div style="padding: 0 40px 0 40px;">
                <el-form :model="addAddressForm" ref="addAddressForm" label-width="100px" :rules="rules" style="display: flex">
                    <div>
                        <el-form-item label="所在區域" prop="region" required style="margin-bottom: 18px;">
                            <el-cascader
                                    v-model="addAddressForm.region"
                                    :options="areaList"
                                    placeholder="請選擇"
                                    @change="changeArea"></el-cascader>
                        </el-form-item>
                        <el-form-item label="姓名" prop="name" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.name" placeholder="請輸入收件人姓名" />
                        </el-form-item>
                        <el-form-item label="身份證號" prop="idCard" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.idCard" placeholder="請輸入收件人身份證號" />
                        </el-form-item>
                        <el-form-item label="固定號碼" prop="fixedNumber">
                            <el-input v-model="addAddressForm.fixedNumber" placeholder="請輸入" />
                        </el-form-item>
                    </div>
                    <div style="flex: 1;">
                        <el-form-item label="詳細地址" prop="address" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.address" placeholder="請輸入詳細地址例如**街**號" />
                        </el-form-item>
                        <el-form-item label="手機號碼" prop="mobile" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.mobile" placeholder="請輸入手機號碼" />
                        </el-form-item>
                        <el-form-item label="郵政編碼" prop="postalCode" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.postalCode" placeholder="請輸入郵政編碼" />
                        </el-form-item>
                        <el-form-item label="設定標籤" prop="label" class="tags-item" label-width="auto">
                            <el-radio-group @input="handleRadioInput" v-model="addAddressForm.label" class="tags-radio">
                                <el-radio
                                        v-for="item in identityList" :key="item.label" :label="item.label">{{ item.name }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>
                </el-form>
                <div style="display: flex; align-items: center; margin-top: 32px;">
                    <el-switch v-model="addAddressForm.isDef" active-text="預設寄送地址" style="margin-right: 16px;" />
                </div>
                <div style="display: flex; justify-content: flex-end; gap: 24px; margin-top: 32px;">
                    <el-button  size="mini" @click="closeAddAddress">取消</el-button>
                    <el-button size="mini" type="danger" style="background: #EF436D;" @click="submitAddAddress">確定</el-button>
                </div>
            </div>
        </el-dialog>
        <!--集运订单-->
        <el-dialog :close-on-click-modal="false" :visible.sync="traOrderDialogVisible" right class="forward" color="red"
                   title="打包回臺-新增集運訂單" width="1008px">
            <div class="tra-order-dialog-content custom-scrollbar"  style="width: 100%; height: 60vh; overflow-y: auto">
                <!-- 顶部标题与包裹统计 -->
                <div class="tra-order-header">
                    <span class="warehouse">
                        <img style="width: 14px; height: 20px;" src="__CDN__/assets/img/pc/new_index/parcel13.svg" alt="">
                        <span>倉庫：{{warehouseType}}</span>
                    </span>
                    <span class="order-summary">已選{{selectedParcels.length}}個包裹，包裹總重量：<span
                            class="red">{{totalWeight}}KG</span></span>
                </div>

                <!-- 包裹列表 -->
                <div class="tra-order-parcel-list">
                    <el-table :data="selectedParcels" class="custom-header" style="width: 100%;">
                        <el-table-column label="貨品信息" min-width="180">
                            <template slot-scope="scope">
                                <div style="display: flex; align-items: center;">
                                    <div class="order-detail-goods-img" style="display: flex; justify-content: center; align-items: center">
                                        <img v-if="scope.row.goods_url" :src="scope.row.goods_url" alt="">
                                        <img  v-else src="__CDN__/assets/img/pc/new_index/no_package.png" alt="">
                                    </div>
                                    <div style="margin-left: 12px;">
                                        <div>
                                            <span class="order-detail-goods-type red">{{ scope.row.wbill_name }}</span>
                                            {{ scope.row.waybill }}
                                        </div>
                                        <div class="order-detail-goods-desc">縂價保： </div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="入庫時間" prop="price"  width="">
                            <template slot-scope="scope">
                                {{ formatDateTime(scope.row.createtime) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="品名" prop="pay"  width="110">
                            <template slot-scope="scope">
                                {{ scope.row.goods_name }}
                            </template>
                        </el-table-column>
                        <el-table-column label="商品重量" prop="qty"  width="110">
                            <template slot-scope="scope">
                                {{ scope.row.scale }}KG
                            </template>
                        </el-table-column>
                        <el-table-column label="才積重量" prop="total"  width="110">
                            <template slot-scope="scope">
                                <!--                                {{ scope.row.total }}-->
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 收货地址 -->
                <div class="tra-order-section">
                    <div class="tra-order-subtitle">
                        <span class="fs14 red">收貨地址</span>
                        <span class="choose-other-address" @click="addrlsDialogVisible = true">選擇其他收貨地址</span>
                    </div>
                    <div class="address-radio-group">
                        <div>
                            <el-radio v-model="orderForm.deliveryRadio" label="1"><span class="fs13">宅配</span></el-radio>
                            <el-radio v-model="orderForm.deliveryRadio" label="2"><span class="fs13">7-11到店自取</span></el-radio>
                        </div>
                        <div class="fw fs14 c333">新竹優先</div>
                    </div>
                    <div v-if="orderForm.deliveryRadio === '1'" class="address-detail">
                        <div class="fs13 fw c333">{{addrInfo.detail}}</div>
                        <div class="fs13 c333">{{addrInfo.name}}（{{addrInfo.mobile}}）</div>
                        <div class="fs13 c333">
                            <el-tag type="warning" size="mini">申報人</el-tag>
                            <span>{{userInfo.username}} ({{userInfo.card}})</span>
                        </div>
                    </div>
                    <div v-if="orderForm.deliveryRadio === '2'" class="address-detail">
                        <el-cascader v-model="orderForm.specifiedAddr" :options="areaList" placeholder="請選擇超商地址或門市名" clearable></el-cascader>
                    </div>
                </div>

                <!-- 选择運輸方式 -->
                <div class="tra-order-section">
                    <div class="fs14 red mb16">選擇運輸方式</div>
                    <div class="mb8 shui" v-if="Number(selectTran.transport) === 15">
                        <el-radio-group v-model="orderForm.feeRadio" @change="feeChange">
                            <el-radio v-for="(item,index) in feeOpt" :key="index" :label="item.style">
                                {{item.style === 1 ? '不包稅' : item.style === 2 ? '包頻繁稅' : '全包稅'}}
                            </el-radio>
                        </el-radio-group>
                    </div>
                    <div class="channel-info">

                        <div class="channel-item" :class="{ 'selected': selectedChannelIndex === index }"
                             v-for="(item, index) in transportTypes" :key="index" @click="selectChannel(index)">
                            <div class="channel-title">{{item.name}}</div>
                            <div class="channel-conteent">
                                <!-- <span class="fs13 c333"><span class="red fs14">2025-05-03</span>前派件</span> -->
                                <span class="fs13 c333 trans-fee">
                                    <template v-if="getFeeByTransport(item.transport)">
                                        <span v-if="getFeeByTransport(item.transport).volume_unit">
                                            {{getFeeByTransport(item.transport).volume_unit}}元/材積
                                        </span>
                                        <span v-if="getFeeByTransport(item.transport).scale_unit">
                                            {{getFeeByTransport(item.transport).scale_unit}}元/公斤
                                        </span>
                                        <span v-if="getFeeByTransport(item.transport).fr_weight">
                                            <span>{{getFeeByTransport(item.transport).fr_weight}}元/首重</span>
                                            <span>{{getFeeByTransport(item.transport).fl_weight}}元/续重</span>
                                        </span>
                                    </template>
                                    <span v-if="item.transport === 15 && totalWeight > 50" class="fs12 red"
                                          style="margin-top: 4px;">該運輸方式不支持海快（超過50KG）</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="insurance-info">
                        <div class="overweight-fee">
                            <span style="display: flex; align-items: center; gap: 8px;">
                                <img src="__CDN__/assets/img/pc/new_index/parcel14.svg" alt="">
                                <span class="fs14 c333">超時險</span>
                            </span>
                            <span v-if="firstTaxChecked" class="fs18 red">NT$ {{timeoutAmount}}</span>
                        </div>
                        <div v-if="timeoutInfo.describe" class="overweight-desc">
                            <span>{{timeoutInfo.describe}}</span>
                        </div>
                        <div>
                            <el-checkbox v-model="firstTaxChecked"><span class="fs13">同意</span></el-checkbox>
                            <span>
                                <span class="fs13 red underline pointer" @click="openProtocol(1)">《超時險服務協議》</span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 丢失保价 -->
                <div class="tra-order-section">
                    <div class="fs14 red mb16">丟失保價</div>
                    <div class="insurance-block">
                        <!-- <div class="insurance-block-first">
                                    <img src="__CDN__/assets/img/pc/new_index/parcel15.svg" style="width:20px;height:20px;" alt="">
                                    <span class="c666 fs13">此訂單贈送丟失保價7元</span>
                                    <span style="flex:1;"></span>
                                    <span class="fs13 c333">0元</span>
                                </div> -->
                        <div style="margin-bottom: 8px; margin-left: 4px">
                            <!--                        <div style="display:flex; justify-content: space-between; gap: 22px; margin-bottom: 8px;">-->
                            <!--                            <el-button type="danger">加購1000元</el-button>-->
                            <el-input-number @change="handleLost" class="baojia-input" :disabled="!secondTaxChecked" :min="0" :max="200000" :precision="0"
                                             placeholder="請輸入保價金額" v-model="orderForm.lostPrice" style="flex:1" />
                        </div>
                        <div style="background:#FFF5E5;border-radius:4px;padding:10px 16px;display:flex;align-items:center;">
                            <i class="el-icon-warning-outline" style="color: #F2CB51; margin-right: 4px;"></i>
                            <span
                                    style="color:#E37318;font-size:14px;">如包裹全部丟失，將賠償保價金額{{orderForm.lostPrice}}+商品金額{{goodsPrice}}+歉意金{{Number(orderForm.lostPrice) * 0.1 + 15 }}+基礎運費{{selectFee?.taxpre * Math.max(5,calculateWeight(totalWeight)) || 1400}}，合計{{lostCompensation}}；</span>
                            <span v-if="!orderForm.lostPrice" style="color: #F10107;font-size:14px;">*最高賠付金額NT$3000。</span>
                        </div>
                        <div style="margin-top: 12px">
                            <el-checkbox v-model="secondTaxChecked" @change="checkedLostStatus"><span class="fs13">同意</span></el-checkbox>
                            <span>
                                <span class="fs13 red pointer underline" @click="openProtocol(2)">《丟失保價協議》</span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 附加服务 -->
                <div class="tra-order-section">
                    <div class="fs14 red mb16">出貨附加服務</div>
                    <div>
                        <el-checkbox v-model="orderForm.paster"><span class="fs13">易碎品貼紙</span></el-checkbox>
                    </div>
                </div>
                <div
                        style="height: 4px; background: #f7f7f7; margin-bottom: 24px;  width: calc(100% + 88px);margin-left: -44px;">
                </div>

                <!-- 费用明细 -->
                <div class="tra-order-fee-detail"
                     style="background: #F7F7F7; border-radius: 6px; padding: 18px 24px; margin-bottom: 18px;">
                    <div style="display: flex; flex-direction: column; gap: 4px;">
                        <div style="display: flex; justify-content: space-between;">
                            <span class="fs13 c666">重量合計：</span>
                            <span class="fs13 c333">
                                    <span class="red fw">{{selectedParcels.length}}</span>個包裹，重量<span class="red fw">{{totalWeight}}</span>KG</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span class="fs13 c666">計費重量：</span>
                            <span class="fs13 c333"> <span class="red fw">{{Math.max(5,calculateWeight(totalWeight))}}</span>KG</span>
                        </div>
                        <div v-if="orderForm.transportType" class="fs13" style="display: flex; justify-content: space-between;">
                            <span class="c666">基礎運費：</span>
                            <span style="display: flex; flex-direction: column; text-align: right">
                                    <span  class="red fw">NT$ {{selectFee.taxpre * Math.max(5,calculateWeight(totalWeight)) || 1400}}</span>
                                    <span v-if="orderForm.taxpre" class="c999 fw">（{{selectFee.taxpre}}*{{Math.max(5,calculateWeight(totalWeight))}}KG={{selectFee.taxpre * Math.max(5,calculateWeight(totalWeight))}}元）</span>
                                </span>
                        </div>  
                        <div v-if="selectTran.transport == 15 && feeList.length" style="display: flex; justify-content: space-between;">
                                <span class="fs13 c666">{{
                                    selectFee?.style == 1 ? '不包稅：' : selectFee.style == 2 ? '包頻繁稅：' : '全包稅：'
                                    }}</span>
                            <span>
                                    <span class="fs13 red fw">NT$ {{shuiFee || 0}} </span>
                                <!--                                    <span v-if="selectFee.style != 1" class="fs13 c999 mr8">（小於5KG固定收取50元）</span>-->
                                </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; line-height: 2;">
                            <span class="fs13 c666">折扣卡：</span>
                            <span style="color: #333;">無可用</span>
                        </div>
                        <div v-if="firstTaxChecked" style="display: flex; justify-content: space-between; line-height: 2;">
                            <span class="fs13 c666">超時險：</span>
                            <span class="fw red">NT$ {{timeoutAmount}}</span>
                        </div>
                        <div v-if="secondTaxChecked" style="display: flex; justify-content: space-between; line-height: 2;">
                            <span class="fs13 c666">丟失保價：</span>
                            <span class="fw red">NT$ {{lostAmount}}</span>
                        </div>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between; line-height: 2;">
                    <span class="fs18 c333 fw">合計：</span>
                    <span class="red fs18 fw">NT$ {{ orderForm.totalAmount || 0}}</span>
                </div>
                <!-- 底部按钮 -->
                <div class="tra-order-footer">
                    <button @click="closeTraOrder"
                            style="padding: 6px 30px; border-radius: 4px; border: 1px solid #eee; background: #fff; color: #EF436D;">取消</button>
                    <button
                            :disabled="isHaikuaiDisabled"
                            :style="'padding: 6px 30px; border-radius: 4px; border: none; ' + (isHaikuaiDisabled ? 'background: #eee; color: #999; cursor: not-allowed;' : 'background: #EF436D; color: #fff; cursor: pointer;')"
                            @click="submitSendOut">送出</button>
                </div>
                <div v-if="isHaikuaiDisabled" class="fs13 red" style="margin-top: 8px;">當前包裹總重量超過50KG，無法選擇海快運輸方式，請選擇其他運輸方式</div>
            </div>
        </el-dialog>
        <el-dialog title="" :visible.sync="protocolDialogVisible" width="1008px">
            <div v-if="protocolType === 1">
                {include file="assets/timeout" /}
            </div>
            <div v-else>
                {include file="assets/lost" /}
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="protocolDialogVisible = false">返 回</el-button>
            </span>
        </el-dialog>

        <!--         集运订单详情弹窗 -->
        <el-dialog title="" center :close-on-click-modal="false" class="order-detail" :visible.sync="orderPackDialogVisible" width="980px" >
            <div class="packup-dialog-wrap" style="height: 100%; display: flex; align-items: stretch;">
                <div class="packup-dialog-left custom-scrollbar relative" style="overflow-y: auto; overflow-x: hidden;">
                    <div class="packup-order-status">
                        <span class="packup-status-icon">
                            <img :src="showTranIcon" alt="">
                        </span>
                        <span class="fs14 c333">{{orderDetail.info.name}}</span>
                        <span class="packup-status-pay absolute">{{
                            orderDetail.info.pay_status == 0 ? '未支付' :
                                orderDetail.info.pay_status == 1 ? '部分支付' :
                                    orderDetail.info.pay_status == 2 ? '已支付' :
                                        orderDetail.info.pay_status == 3 ? '多支付' : ''
                            }}</span>
                    </div>
                    <div class="packup-order-info">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                            <span class="packup-status-icon">
                                <img src="__CDN__/assets/img/pc/new_index/parcel13.svg" alt="">
                            </span>
                            <span class="c333 fs14">倉庫：{{orderDetail.info.title}}</span>
                        </div>
                        <div class="packup-order-row c333">
                            <span class="fs14 fw">訂單編號：</span>
                            <span class="fs18">{{orderDetail.info.order_no}}</span>
                        </div>
                        <div class="packup-order-row c333 fs14">
                            <span>創建時間：</span>
                            <span>{{formatDate(orderDetail.info.createtime,1)}}</span>
                        </div>
                        <div class="packup-order-btns Source-Han-Sans">
                            <button v-if="false" class="packup-btn packup-btn-calc">取消訂單</button>
                            <button v-if="orderDetail.info.pay_status == 0" class="packup-btn packup-btn-pay" @click="openPayDialog(orderDetail.info)">去付款</button>
                        </div>
                    </div>
                    <div class="line4" style="height: 4px; background: #f4f4f4; width: calc(100% + 50px);margin-left: -24px; flex-shrink: 0;"></div>
                    <div class="packup-section">
                        <div class="packup-section-title">收貨地址</div>
                        <div class="packup-address">
                            <div class="addr-svg">
                                <img alt="" src="__CDN__/assets/img/pc/new_index/parcel09.svg">
                            </div>
                            <div class="packup-address-info">
                                <div>{{orderDetail.addrInfo.detail}}</div>
                                <div style="margin-top: 4px">{{orderDetail.addrInfo.name}}（{{orderDetail.addrInfo.mobile}}）</div>
                            </div>
                            <div class="packup-address-edit pointer" @click="editAddr">
                                <i class="el-icon-edit" style="font-size: 12px; margin-right: 8px"></i>
                                <span >我要修改收貨地址（{{orderDetail.info.order_status == 2 ? '需付費200元' : '免費'}}）
                                    <el-tooltip v-if="orderDetail.info.order_status ==2" popper-class="my-tooltip" effect="light" placement="bottom-start">
                                        <template slot="content">
                                            <div class="Source-Han-Sans" style="font-size: 9px; color: rgba(0, 0, 0, 0.6); line-height: 16px">
                                                已裝車後無法再查找包裹改地址，只能到台灣清關出來以後，逐件查找再拉回報關行重新列印貨運單。此時修改地址需收費200元，時效會耽誤1天。且因清關現場作業速度很快，可能攔截不到。
                                            </div>
                                            <div class="Source-Han-Sans" style="font-size: 9px;color: rgba(0, 0, 0, 0.6); line-height: 16px">若發生地址未修改成功的情況可聯絡客服申請退款。</div>
                                        </template>
                                        <i class="el-icon-question" style="color: #C7C7C7"></i>
                                    </el-tooltip>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="packup-section">
                        <div class="packup-section-title">報關方式</div>
                        <div class="packup-declare">
                            <div style="display: flex; gap: 12px; align-items: center">
                                <div class="my-tag">申報人</div>
                                <span>{{orderDetail.applInfo.username}}({{orderDetail.applInfo.card}})</span>
                            </div>
                            <div style="display: flex; gap: 12px; align-items: center">
                                <div class="my-tag">報關方式</div>
                                <span>{{customsType}}</span>
                            </div>
                        </div>
                    </div>
                    <div v-if="orderDetail.lostInfo?.bj_price || 0" class="packup-section">
                        <div class="packup-section-title">丟失保障</div>
                        <div class="packup-insurance">
                            <div class="packup-insurance-info">
                                <span><i class="el-icon-warning" style="color: #FF9200; font-size: 20px;"></i></span>
                                <span class="Source-Han-Sans fs12">如包裹全部丟失，將賠償商品價值{{goodsPrice}}元，保價金額{{orderDetail.lostInfo?.bj_price}}元，歉意金{{apologyFee}}元，以及運費{{Number(orderDetail.info.base_price)}}元，合計{{compensationTotalPrice}}元</span>
                            </div>
                        </div>
                    </div>
                    <div class="packup-section">
                        <div class="fw14 c333 mb12"><span class="red">{{orderDetail.info.num}}</span> 個包裹</div>
                        <div class="packup-summary-list">
                            <div class="packup-summary-row">
                                <div class="fs13 c666">重量合計：</div>
                                <div style="display: flex; flex-direction: column; gap: 8px;text-align: right">
                                    <div>計算重量：{{calculateWeight(orderDetail.info.scale || 0)}}KG</div>
                                    <div>實際重量：{{Number(orderDetail.info.scale).toFixed(1)}}KG</div>
                                </div>
                            </div>
                            <div class="packup-summary-row">
                                <div class="fs13 c666">基礎運費：</div>
                                <div style="display: flex; flex-direction: column; gap: 8px">
                                    <div class="packup-summary-value">NT$ {{orderDetail.info.base_price}}</div>
                                    <div class="fs13 c666"></div>
                                </div>
                            </div>
                            <div class="packup-summary-row" v-if="orderDetail.info.transport == 15">
                                <div class="fs13 c666">{{shuiType}}</div>
                                <div style="display: flex; flex-direction: column; gap: 8px">
                                    <div class="packup-summary-value" style="text-align: right">NT$ {{orderDetail.info.tax}}</div>
                                    <!--                                    <div class="fs13 c666">(小於5KG固定收取50元)</div>-->
                                </div>
                            </div>
                            <div v-if="orderDetail.info.disp" class="packup-summary-row">
                                <div class="fs13 c666">派件費：</div>
                                <div class="packup-summary-value">NT$ {{orderDetail.info?.disp || 0}}</div>
                            </div>
                            <div v-if="orderDetail.info.super_disp" class="packup-summary-row">
                                <div class="fs13 c666">超大派件費：</div>
                                <div class="packup-summary-value">NT$ {{orderDetail.info?.super_disp || 0}}</div>
                            </div>
                            <div v-if="orderDetail.timeoutInfo?.collect_price" class="packup-summary-row">
                                <div class="fs13 c666">超時險：</div>
                                <div class="packup-summary-value">NT$ {{orderDetail.timeoutInfo?.collect_price || 0}}</div>
                            </div>
                            <div v-if="orderDetail.lostInfo?.collect_price" class="packup-summary-row">
                                <div class="fs13 c666">丟失保價：</div>
                                <div class="packup-summary-value">NT$ {{orderDetail.lostInfo?.collect_price || 0}}</div>
                            </div>
                            <div class="packup-summary-row">
                                <div class="fs18 fw c3d3">合計：</div>
                                <div class="packup-summary-total-value"> <span v-if="orderDetail.info.actual_money">NT$</span>  {{ orderDetail.info.actual_money || 0}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-line"></div>
                <div class="packup-dialog-right custom-scrollbar" style="overflow-y: auto;">
                    <div class="packup-goods-list" v-for="(item,index) in orderDetail.goodsList" :key="index">
                        <div class="packup-goods-item">
                            <div class="packup-goods-header">
                                <div class="fs13" style="display: flex; align-items: center; gap: 9px">
                                    <div style="width: 14px;height: 14px;">
                                        <svg class="icon fs14" aria-hidden="true">
                                            <use :xlink:href="item.icon"></use>
                                        </svg>
                                    </div>
                                    <div >{{item.sname}}{{item.waybill || '快遞單號異常'}}</div>
                                </div>
                                <div class="fw" style="display: flex; align-items: center">包裹總重量：
                                    <div class="red mr12">{{item.scale}}KG</div>
                                    <div style="width: 20px; height: 20px;" class="pointer" v-if="false">
                                        <img style="width: 100%; height: 100%" src="__CDN__/assets/img/pc/new_index/img.svg" alt="">
                                    </div>
                                </div>
                            </div>
                            <div class="packup-goods-body">
                                <div class="packup-goods-img">
                                    <img :src="item.goods_url || '__CDN__/assets/img/pc/new_index/no_package.png'" alt="">
                                </div>
                                <div class="packup-goods-info">
                                    <div class="red fs14">[{{item.goods_name}}]</div>
                                    <div class="c333 fs13">暂无商品详细信息</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="bindLineDialogVisible" custom-class="line-binding-dialog" width="904px" title="">
            <div class="line-binding-header">
                <div class="line-binding-logo">
                    <img src="__CDN__/assets/img/pc/ez_logo.png" alt="EZ集運道">
                    <span class="line-binding-title">for LINE <span class="c333 fs16">導覽</span></span>
                </div>
            </div>
            <div class="line-binding-steps">
                <div class="line-binding-step">
                    <div class="line-binding-step-number active" :style="{ backgroundColor: lineStatus ? '#D0D0D0' : '#22B573' }">1</div>
                    <div class="line-binding-step-arrow">
                        <img :src="!lineStatus ? '__CDN__/assets/img/pc/step_arrow01.svg' : '__CDN__/assets/img/pc/step_arrow02.svg'" alt="">
                    </div>
                    <div class="line-binding-step-content">
                        <div class="line-binding-step-image">
                            <img src="__CDN__/assets/img/pc/linebg01.png" alt="LINE account">
                        </div>
                        <div class="line-binding-step-title ">綁定LINE帳號，快捷登錄！</div>
                        <div class="line-binding-step-link">
                            <img v-if="lineStatus" src="__CDN__/assets/img/pc/line_icon.svg" alt="LINE account">
                            <span v-if="lineStatus" style="color: #22B573; font-size: 16px;">已綁定（LINE帳號 ：<?=($user['username']);?>）</span>
                            <span v-else class="fs16 underline red pointer" @click="toBindLine">綁定LINE帳號 >></span>
                        </div>
                    </div>
                </div>
                <div class="line-binding-step"> 
                    <div class="line-binding-step-number inactive" :style="{ backgroundColor: lineStatus ? '#22B573' : '#D0D0D0' }">2</div>
                    <div class="line-binding-step-arrow">
                        <img :src="!lineStatus ? '__CDN__/assets/img/pc/step_arrow02.svg' : '__CDN__/assets/img/pc/step_arrow01.svg'" alt="">
                    </div>  
                    <div class="line-binding-step-content">
                        <div class="line-binding-step-image">
                            <img style="width: 128px; height: 128px;"  v-if="lineStatus && !bindRobot || (lineStatus && bindRobot)"   src="__CDN__/assets/img/pc/qrcode.png" alt="QR Code">
                            <img style="width: 100%; height: 100%;"  v-else src="__CDN__/assets/img/pc/linebg02.png" alt="add robot">

                        </div>
                        <div class="fs16 fw line-binding-step-title" :style="{color: bindRobot ? '#333333' : '#999999'}">掃QR碼添加機器人接收物流信息</div>
                        <div v-if="lineStatus" class="line-binding-step-link">
                            <img v-if="bindRobot" src="__CDN__/assets/img/pc/isfriend.svg" alt="LINE account">
                            <img v-else src="__CDN__/assets/img/pc/notfriend.svg" alt="LINE account">
                            <span v-if="bindRobot" style="color: #22B573; font-weight: 500; font-size: 16px;">已添加好友</span>
                            <a v-else href="" style="color: #E37318; font-weight: 500; font-size: 16px;">不是好友</a>
                        </div>
                    </div>              
                </div>
            </div>
        </el-dialog>
        </div>
</div>

<script>
    const warehouse =  JSON.parse('<?=json_encode($wh, JSON_UNESCAPED_UNICODE)?>');
    const lineStatus =  '<?=$line_bang?>';
    const lineBot =  '<?=$line_bot?>';
    const serviceList = {$addser}.data;
    const user = {$user};
    const tp = JSON.parse('<?=json_encode($tp, JSON_UNESCAPED_UNICODE)?>');
    console.log(tp, 'tp');
    console.log(serviceList, 'serviceList');
    const app = new Vue({
        el: '#my-parcel',
        mixins: [bottomNavMixin], // 引入底部导航栏功能和钱包组件功能
        data: {
            page:1,
            pageSize:10,
            tp: tp,
            parcelData: [],
            detailData: [],
            detailLength: 0,
            loading: false,
            tabLoading: false, // 标签页加载状态 - 用于初始加载和切换
            lineStatus: 0, // LINE登录状态
            bindRobot : 0, // 是否扫码机器人
            dataPage: '1',
            searchValue: '',    //申报人搜索
            applicantList: [],
            companyList: [],
            addressList: [],
            areaList:[],
            areaDetail:[],
            addApplicantForm: {
                name: '',
                mobile: '',
                idCard: ''
            },
            searchLoading: false,
            addCompanyForm: {
                companyCode: '',
                companyMobile: ''
            },
            transportIcon: [
                '__CDN__/assets/img/pc/transport01.svg',
                '__CDN__/assets/img/pc/transport02.svg',
                '__CDN__/assets/img/pc/transport03.svg',
            ],
            addApplicantRules: {
                name: [{ required: true, message: '請輸入收件人姓名', trigger: 'blur' }],
                mobile: [
                    { required: true, message: '請輸入手機號碼', trigger: 'blur' },
                    { pattern: /^09\d{8}$/, message: '手機號碼必須為09開頭的10位數字', trigger: 'blur' }
                ],
                idCard: [
                    { required: true, message: '請輸入收件人身份證號', trigger: 'blur' },
                    { pattern: /^[A-Z][0-9]{9}$/, message: '身份證號必須為1位大寫字母+9位數字', trigger: 'blur' }
                ]
            },
            orderRow:{},
            otherLength: 0, // 其他包裹数量
            userInfo: {},   // 申報人信息
            addrInfo: {},   // 地址信息
            goodsId: '', // 商品ID
            wareHouseId: '',
            firstSelectedCheckList: [],
            secondSelectedCheckList: [],
            serviceList: serviceList,
            firstCheckList: [],
            secondCheckList: [],
            addServiceList: [],     // 附加服務列表
            selectAddServiceList: [], // 已选附加服务列表
            uMoney: user.money, // 用户余额
            uMobile:user.mobile, // 用户手机号
            uNouse:user.nouse, // 支付方式
            // tAmount: 0,  // 附加服務總金額
            selectLabels: [],
            previewSrcList: ['__CDN__/assets/img/pc/new_index/goods01.png'],
            goodsImgList: [
                '__CDN__/assets/img/pc/new_index/goods02.png',
                '__CDN__/assets/img/pc/new_index/goods03.png'
            ],
            TWLogisticsIcon: [
                '__CDN__/assets/img/pc/darong.png',
                '__CDN__/assets/img/pc/black_cat.png',
                '__CDN__/assets/img/pc/xinzhu.png'
            ],
            currentPage: 1,
            currentPageSize: 5,
            pageSize: [5, 10, 20, 50],
            label: ['未入庫', '已入庫', '待出貨', '已出貨'],
            activeIndex: 2,  // 当前激活的选项卡索引
            activeName: '',
            activeChange: '1',
            warehouseOptions: [],
            transportTypes:[], // 運費列表
            selectTran:{}, // 選擇的運費列表
            form: {
                warehouseName: '',  //倉庫名稱
                deliveryNum: '',  // 快遞單號
                orderNum: '',  // 訂單編號
            },
            serviceNames: [],
            logisticsDialogVisible: false,  // 集運狀態
            serviceDialogVisible: false,    // 附加服務
            registrationDialogVisible: false,   // 關聯登記
            firstForwardDialogVisible: false,    // 包裹轉寄
            secondForwardDialogVisible: false,
            thirdForwardDialogVisible: false,
            remarksDialogVisible: false,
            inServiceDialogVisible: false,    // 编辑附加服務二級彈窗
            payServiceDialogVisible: false, // 支付附加服務
            payDialogVisible: false, // 轉寄支付
            vsDialogVisible: false, // 查看附加服務二級彈窗
            applicantDialogVisible: false, // 選擇申報人
            applicantListDialogVisible: false, // 申報類型
            addApplicantDialogVisible: false, // 新增申報人
            addrlsDialogVisible: false, // 收貨地址
            orderAddrDialogVisible:false, // 订单收货地址
            addAddressDialogVisible: false, // 新增收貨地址
            traOrderDialogVisible: false, // 集运訂單
            protocolDialogVisible: false, // 協議彈窗
            orderPackDialogVisible: false, // 打包回臺訂單
            bindLineDialogVisible: false, // 綁定LINE導覽
            amount: 55,          // 支付金额
            password: ['', '', '', ''], // 密码数组（保留用于其他地方）
            verifyCode: ['', '', '', ''], // 验证码数组
            uPassword: '', // 用户输入的密码
            currentInput: 0,      // 当前可输入的input索引
            isCountingDown: false, // 是否正在倒计时
            countdown: 90,        // 倒计时秒数
            goodsNum: {
                count: '',
                deCount: '',
            },
            goodsInfo: {},
            orderForm: {
                transportType: '',    // 運輸方式
                deliveryRadio: '1',   // 收貨方式
                taxId: null, // 運輸方式稅金ID
                scale_unit: null, // 稅後金額(元/公斤)
                taxpre:null, // 稅前金額(元/公斤)
                volume_unit: null, // 元/材积
                totalAmount:0, // 总金额
                paster: false, // 易碎品贴纸
                specifiedAddr: '', // 指定地址
                feeRadio: 1,  // 運費計算方式
                tp:null, // 運費計算方式id
                lostPrice: 0, // 丢失保价金额
                fr_weight: null,  // 空运首重单价
                fl_weight: null     // 空运续重单价
            },
            showDialog: false,
            isSwitch: false,
            isPay: false, // 是否支付
            countdown: 25,
            countingDown: false,
            isAgree: false, // 是否同意附加服务条款
            secondIsSwitch: false,  // 附加服務展開收起
            oremarks: '',
            wuliuList:[],
            TWLogistics: [],
            ruleForm: {
                userName: '',
                mobile: '',
                address: '',
                remarks: '',
                pickUpCode: '',
            },
            rules: {
                userName: [{ required: true, message: '請输入姓名', trigger: 'blur' }],
                name: [{ required: true, message: '請輸入收件人姓名', trigger: 'blur' }],
                mobile: [
                    { required: true, message: '請輸入手機號碼', trigger: 'blur' },
                    { pattern: /^09\d{8}$/, message: '手機號碼必須為09開頭的10位數字', trigger: 'blur' }
                ],
                idCard: [
                    { required: true, message: '請輸入收件人身份證號', trigger: 'blur' },
                    { pattern: /^[A-Z][0-9]{9}$/, message: '身份證號必須為1位大寫字母+9位數字', trigger: 'blur' }
                ],
                companyCode: [
                    { required: true, message: '請輸入公司編碼', trigger: 'blur' },
                    { pattern: /^\d{8}$/, message: '公司編碼必須為8位數字', trigger: 'blur' }
                ],
                companyMobile: [
                    { required: true, message: '請輸入手機門號', trigger: 'blur' },
                    { pattern: /^02\d{8}$/, message: '手機門號必須為02開頭的10位數字', trigger: 'blur' }
                ],
                address: [{ required: true, message: '請输入地址', trigger: 'blur' }],
                pickUpCode: [{ required: true, message: '請输入取件碼', trigger: 'blur' }],
                region: [{ required: true, message: '請選擇區域', trigger: 'change' }],
                postalCode: [
                    { required: true, message: '請輸入郵政編碼', trigger: 'blur' },
                    { pattern: /^\d{5,6}$/, message: '郵遞區號只能是5位或6位數字', trigger: 'blur' }
                ],
                address: [{ required: true, message: '請輸入詳細地址', trigger: 'blur' }],
                region: [{ required: true, message: '請選擇區域', trigger: 'change' }],
            },
            currentDialogItem: {},
            selectedParcels: [],    // 已选包裹
            totalWeight: 0,        // 包裹总重量
            statusMap: {
                0: 1,  // 未入库
                1: 2,  // 已入库
                2: 3,  // 待出货
                3: 4   // 已出货
            },
            // expandedItems: {}, // 用于跟踪每个项目的展开状态
            addAddressForm: {
                region: '',
                name: '',
                idCard: '',
                fixedNumber: '',
                address: '',
                mobile: '',
                postalCode: '',
                label: 1,
                isDef: 0
            },
            identityList: [],
            feeList:[],  // 運費列表
            feeOpt:[], // 海快列表
            selectFee:{},
            firstTaxChecked: false,
            secondTaxChecked: false,
            goodsPrice: 0, // 商品价格
            calculateTaxMoney: 0, // 计算后的税金
            protocolType: 1, // 協議類型
            parcelVolume: '', // 包裹体积
            timeoutInfo: {}, // 超时险参数
            lostInfo:{}, // 丢失保价参数
            filteredTran:{}, // 过滤后的运输方式
            selectedChannelIndex: null, // 不默认选中
            orderId: '', // 订单ID
            orderDetail:{
                info:{}, // 订单信息
                addrInfo: {}, // 地址信息
                addrInfo: {}, // 地址信息
                applInfo: {}, // 申报人信息
                goodsList:[], // 商品列表
                ct:{}, // 计算税金
                timeoutInfo:{}, // 超时险参数
                lostInfo:{}, // 丢失保价参数
            },
        },
        computed: {
            paginatedData() {
                const start = (this.currentPage - 1) * this.currentPageSize;
                const end = start + this.currentPageSize;
                return this.parcelData.slice(start, end);
            },
            // 附加服務總金額
            tAmount() {
                const combinedList = [...this.firstSelectedCheckList, ...this.secondSelectedCheckList];
                return combinedList.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);
            },
            // 获取弹窗标题
            getDialogTitle() {
                const titleMap = {
                    '確認型號': '備註',
                    '清點數量': '數量',
                    '包裹拆分': '需要拆分的包裹'
                };
                return titleMap[this.currentDialogItem.title] || '';
            },
            // 是否为包装服务
            isPackagingService() {
                return ['包裝氣泡棉', '包裝氣柱袋'].includes(this.currentDialogItem.title);
            },
            // 是否显示备注输入框
            showRemarksInput() {
                return ['確認型號', '清點數量', '包裹拆分'].includes(this.currentDialogItem.title);
            },
            payBtnStyle() {
                return this.uMoney < this.tAmount
                    ? { background: '#EEEEEE', color: '#666666', border: '1px solid #EEEEEE' }
                    : { background: '#EF436D' };
            },
            transTitle() {
                if(!this.orderForm.transportType) return '';
                let firstTitle = this.filteredTran.name;
                let secondTitle = '';
                this.filteredTran.style === 1 ? secondTitle = '包税' : this.filteredTran.style === 2 ? secondTitle = '包频繁税' : this.filteredTran.style === 3 ? secondTitle = '全包税' : '';
                return `${firstTitle}-${secondTitle}`;
            },
            initPrice() {
                if(!this.orderForm.transportType) return '';
                return this.filteredTran?.scale_unit || 0;
            },
            // 超时险收费金额
            timeoutAmount() {
                if (!this.timeoutInfo || !this.timeoutInfo.cg_proprotion) {
                    return 0;
                }
                
                // 获取运费基础金额，然后应用超时险比例
                let transportFee = 0;
                const transportType = Number(this.selectTran.transport);
                
                // 海快计算基础运费
                if (transportType === 15) {
                    const weightForCalc = Math.max(5, this.totalWeight);
                    const calculatedWeight = this.calculateWeight(weightForCalc);
                    transportFee = this.selectFee?.taxpre * calculatedWeight;
                } 
                // 海运计算基础运费
                else if (transportType === 17) {
                    const weightForCalc = Math.max(5, this.totalWeight);
                    const caijiWeight = (this.parcelVolume || 0) / Number(this.selectFee.volume);
                    const calculatedWeight = this.calculateWeight(weightForCalc);
                    const firstFee = this.orderForm.volume_unit * caijiWeight;
                    const secondFee = this.orderForm.scale_unit * calculatedWeight;
                    const thirdFee = 1400;
                    transportFee = Math.max(firstFee, secondFee, thirdFee);
                }
                // 空运计算基础运费
                else if (transportType === 16) {
                    const weightForCalc = Math.max(5, this.totalWeight);
                    const caijiWeight = (this.parcelVolume || 0) / Number(this.selectFee.volume);
                    const calculatedWeight = this.calculateWeight(weightForCalc);
                    const fr_weight = this.orderForm.fr_weight;
                    const fl_weight = this.orderForm.fl_weight;
                    
                    if (Number(caijiWeight) > (Number(calculatedWeight) * 3)) {
                        transportFee = fr_weight + (Number(caijiWeight) / 2 - 1) * fl_weight;
                    } else {
                        transportFee = fr_weight + (calculatedWeight - 1) * fl_weight;
                    }
                }
                
                // 计算超时险金额
                return Math.ceil(Number(this.timeoutInfo.cg_proprotion) * transportFee);
            },
            // 丢失价保收费金额
            lostAmount() {
                return Math.ceil(Number(this.orderForm.lostPrice) + Number(this.orderForm.lostPrice) * Number(this.lostInfo.cg_proprotion) );
            },
            // 丢失价保赔付金额
            lostCompensation() {
                return Number(this.orderForm.lostPrice) + this.goodsPrice + Number(this.orderForm.lostPrice) * 0.1 + 15 + (this.selectFee?.taxpre * Math.max(5,this.calculateWeight(this.totalWeight)) || 1400) > 200000
                    ? 200000
                    : Number(this.orderForm.lostPrice) + this.goodsPrice + Number(this.orderForm.lostPrice) * 0.1 + 15 + (this.selectFee?.taxpre * Math.max(5,this.calculateWeight(this.totalWeight)) || 1400);
            },
            warehouseType(){
                return warehouse.find(item=>Number(item.id) === Number(this.wareHouseId))?.title || '';
            },
            isHaikuaiDisabled() {
                // 当前选中的是海快且超重
                return Number(this.selectTran.transport) === 15 && this.totalWeight > 50;
            },
            shuiFee() {
                if (Number(this.selectFee.style) === 1) {
                    return 0;
                } else {
                    if (this.calculateWeight(this.totalWeight) >= 5) {
                        return (this.calculateWeight(this.totalWeight) - 5) * this.selectFee?.tax + this.selectFee?.base_tax;
                    } else {
                        return this.selectFee?.base_tax;
                    }
                }
            },
            showTranIcon() {
                switch (this.orderDetail.info.transport) {
                    case 15:
                        return this.transportIcon[0] // 海快
                    case 16:
                        return this.transportIcon[1] // 極兔
                    case 17:
                        return this.transportIcon[2] // 順豐
                    default:
                        return '';
                }
            },
            shuiType() {    // 税费类型
                if (Number(this.orderDetail.ct.style) === 1) {
                    return '不包稅';
                } else if (Number(this.orderDetail.ct.style) === 2) {
                    return '包頻繁稅';
                } else if (Number(this.orderDetail.ct.style) === 3) {
                    return '全包稅';
                } else {
                    return '';
                }
            },
            apologyFee() {  // 道歉金
                return this.orderDetail.lostInfo?.bj_price * 0.1 + 15;
            },

            compensationTotalPrice() {  // 赔偿总金额
                return this.orderDetail.lostInfo?.bj_price + this.apologyFee + this.orderDetail.info.base_price;
            },

            // 海快运费计算
            seaExpressAmount() {
                if (Number(this.orderForm.transportType) !== 15) return 0;
                
                const weightForCalc = Math.max(5, this.totalWeight);
                const caijiWeight = (this.parcelVolume || 0) / Number(this.selectFee.volume);
                const calculatedWeight = this.calculateWeight(weightForCalc);

                let firstFee = this.selectFee.volume_unit * caijiWeight;

                let secondFee = this.selectFee.taxpre * calculatedWeight;

                let transportFee = Math.max(firstFee, secondFee);

                const timeoutFee = this.firstTaxChecked ? this.timeoutAmount : 0;
                const lostFee = this.secondTaxChecked ? (Number(this.orderForm.lostPrice) + Number(this.orderForm.lostPrice) * (this.lostInfo?.cg_proprotion || 0)) : 0;
                
                return Math.ceil(transportFee + timeoutFee + lostFee + this.shuiFee);
            },
            // 海运运费计算
            seaFreightAmount() {
                if (Number(this.orderForm.transportType) !== 17) return 0;
                
                const weightForCalc = Math.max(5, this.totalWeight);
                const caijiWeight = (this.parcelVolume || 0) / Number(this.selectFee.volume);
                const calculatedWeight = this.calculateWeight(weightForCalc);
                
                let firstFee = this.selectFee.volume_unit * caijiWeight;
                let secondFee = this.selectFee.scale_unit * calculatedWeight;
                let thirdFee = 1400;
                let transportFee = Math.max(firstFee, secondFee, thirdFee);
                
                const timeoutFee = this.firstTaxChecked ? this.timeoutAmount : 0;
                const lostFee = this.secondTaxChecked ? (Number(this.orderForm.lostPrice) + Number(this.orderForm.lostPrice) * (this.lostInfo?.cg_proprotion || 0)) : 0;
                
                return Math.ceil(transportFee + timeoutFee + lostFee);
            },
            // 空运运费计算
            airFreightAmount() {
                if (Number(this.orderForm.transportType) !== 16) return 0;
                
                const weightForCalc = Math.max(5, this.totalWeight);
                const caijiWeight = (this.parcelVolume || 0) / Number(this.selectFee.volume);
                const calculatedWeight = this.calculateWeight(weightForCalc);
                const fr_weight = this.selectFee.fr_weight;
                const fl_weight = this.selectFee.fl_weight;
                
                let transportFee;
                if (Number(caijiWeight) > (Number(calculatedWeight) * 3)) {
                    transportFee = fr_weight + (Number(caijiWeight) / 2 - 1) * fl_weight;
                } else {
                    transportFee = fr_weight + (calculatedWeight - 1) * fl_weight;
                }
                
                const timeoutFee = this.firstTaxChecked ? this.timeoutAmount : 0;
                const lostFee = this.secondTaxChecked ? (Number(this.orderForm.lostPrice) + Number(this.orderForm.lostPrice) * (this.lostInfo?.cg_proprotion || 0)) : 0;
                
                return Math.ceil(transportFee + timeoutFee + lostFee);
            },
            // 根据运输类型获取总金额
            totalTransportAmount() {
                const transportType = Number(this.orderForm.transportType);
                
                if (transportType === 15) {
                    return this.seaExpressAmount;
                } else if (transportType === 17) {
                    return this.seaFreightAmount;
                } else if (transportType === 16) {
                    return this.airFreightAmount;
                }
                return 0;
            },

            // 报关方式
            customsType() {
                if (this.orderDetail.info.transport === 15) {
                    switch (this.orderDetail.ct.style) {
                        case 1:
                            return 'X1 不包稅';
                        case 2:
                            return 'X2 包頻繁稅';
                        case 3:
                            return 'X3 全包稅';
                        default:
                            return '';
                    }
                }
                return 'X3 全包稅';
            }
        },
        watch: {
            // 监听密码数组，输入满4位自动触发（保留用于其他地方）
            password: {
                handler(val) {
                    if (val.every(v => v.length === 1 && /^\d$/.test(v))) {
                        this.secondForwardDialogVisible = false;
                        this.firstForwardDialogVisible = false;
                        this.thirdForwardDialogVisible = false;

                        console.log(this.password, 'password')
                        // 清空密码
                        this.password = ['', '', '', ''];
                        this.currentInput = 0;
                        // 重新聚焦第一个输入框
                        this.$nextTick(() => {
                            if (this.$refs.inputs && this.$refs.inputs[0]) {
                                this.$refs.inputs[0].focus();
                            }
                        });
                        this.uPassword = this.password.join('');
                        // 这里不直接调用 payOrderDetail，而是由验证码验证
                    }
                },
                deep: true
            },
            // 监听验证码数组，输入满4位自动验证
            verifyCode: {
                handler(val) {
                    if (val.every(v => v.length === 1 && /^\d$/.test(v))) {
                        console.log(this.verifyCode, 'verifyCode')
                        // 验证验证码
                        this.verifyCodeAndPay();
                    }
                },
                deep: true
            },
            // 监听支付弹窗状态，关闭时重置倒计时
            payDialogVisible: {
                handler(val) {
                    if (!val) {
                        // 弹窗关闭时重置状态
                        this.isCountingDown = false;
                        this.countdown = 90;
                        this.verifyCode = ['', '', '', ''];
                        this.currentInput = 0;
                    }
                }
            },
            parcelData() {
                // 当数据变化时自动重置到第一页
                this.currentPage = 1;
            },
            firstTaxChecked() {
                this.calculateTotalAmount(this.orderForm.lostPrice)
            },
            secondTaxChecked() {
                this.calculateTotalAmount(this.orderForm.lostPrice)
            },
        },
        mounted() {
            this.lineBind();
            // 处理仓库类型数据
            this.warehouseOptions = warehouse.map(item=>({
                id: String(item.id),
                name: item.title
            }))

            if (this.warehouseOptions.length > 0) {
                this.activeName = this.warehouseOptions[0].id;
                // 自动选择第一个仓库并触发筛选 - 修复初始化时默认选中第一个标签问题
                if (this.activeIndex === 0 && this.warehouseOptions.length > 0) {
                    this.form.warehouseName = this.warehouseOptions[0].id;
                }
            }
            // 自动聚焦第一个输入框
            this.$nextTick(() => {
                if (this.$refs.inputs && this.$refs.inputs[0]) {
                    this.$refs.inputs[0].focus();
                }
            });
            
            // 设置加载状态
            this.tabLoading = true;
            
            const start = Date.now();
            // 页面初始化时只获取数据，不进行搜索
            this.getParcelList()
                .finally(() => {
                    const elapsed = Date.now() - start;
                    const remain = Math.max(0, 600 - elapsed);
                    
                    setTimeout(() => {
                        this.tabLoading = false;
                    }, remain);
                });
            // 添加全局点击事件监听
            document.addEventListener('click', this.handleGlobalClick);

        },
        beforeDestroy() {
            // 组件销毁前移除事件监听
            document.removeEventListener('click', this.handleGlobalClick);
        },
        methods: {
            // 安全获取台湾物流信息
            getTwInfo(row, field) {
                return row && row.tw_info && row.tw_info[field] ? row.tw_info[field] : '';
            },

            // 控制表格行是否可选择
            checkSelectable(row, index) {
                // 如果 order_id 大于 0，则禁止选择
                return !(row.order_id && row.order_id > 0);

            },
            // // 转换日期
            formatDate(timestamp, type) {
                return utils.formatDate(timestamp, type);
            },

            maskMobile(mobile) {
                return utils.maskMobile(mobile)
            },

            formatDateTime(timestamp,type) {
                if (!timestamp) return '';
                const date = new Date(timestamp * 1000); // 转换为毫秒
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                switch (type) {
                    case 1: // YYYY-MM-DD
                        return `${year}-${month}-${day}`;
                    case 2: // YYYY-MM-DD HH:mm:ss
                        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    case 3: // MM-DD HH:mm
                        return `${month}-${day} ${hours}:${minutes}`;
                    case 4: // HH:mm:ss
                        return `${hours}:${minutes}:${seconds}`;
                    default:
                        return `${year}-${month}-${day}`;
                }

            },

            stringToArray(str, type = 'array', separator = ',') {
                return utils.stringToArray(str, type, separator);
            },
            calculateWeight(weight) {
                return utils.calculateWeight(weight);
            },
            // 获取包裹列表
            async getParcel(status) {
                let start = Date.now();
                let params = {
                    page:this.page,
                    pageSize: this.pageSize,
                }
                try {
                    // 判断是否为包裹模式还是订单模式
                    const isParcelMode = [0, 1].includes(this.activeIndex);
                    const apiUrl = isParcelMode ? 'myParcel' : 'order_package_status';
                    const res = await axios.post(apiUrl, { status });
                    if (res.data.code == 0) {
                        // 根据接口类型获取不同的数据结构
                        const kdData = res.data.kd || [];
                        const detailData = res.data.detail || [];
                        
                        let parcelData = res.data.list || [];
                        // 创建 kd 数据的映射，以 pid 为键
                        const kdMap = {};
                        kdData.forEach(kdItem => {
                            if (!kdMap[kdItem.pid]) {
                                kdMap[kdItem.pid] = [];
                            }
                            kdMap[kdItem.pid].push(kdItem);
                        });
                        
                        this.orderId = parcelData.find(item=>item.order_id !=0)?.order_id || 0;

                        // 将物流信息合并到数据中
                        this.parcelData = parcelData.map(pgItem => {
                            const detail = detailData.find(d => d.id === pgItem.id);
                            const otherLength = detail && detail.list ? detail.list.length : 0;
 
                            // 查找对应的物流信息
                            const logisticsInfo = kdMap[pgItem.id] || [];

                            // 返回合并后的数据
                            return {
                                ...pgItem,
                                logistics: logisticsInfo.length > 0 ? logisticsInfo[0] : null
                            };
                        });

                        if(this.activeIndex ===3){
                            this.getTWLogistics(this.parcelData);
                        }

                        
                        // 只有包裹模式需要获取订单详情
                        if (isParcelMode) {
                            await this.getOrderDetail();
                        }
                        
                        console.log('包裹及订单列表:', this.parcelData);
                    } else {
                        // 获取失败或无数据时，确保清空数据
                        this.parcelData = [];
                        console.log('获取无结果，已清空数据');
                    }
                } catch (e) {
                    // 网络错误时也要清空数据
                    this.parcelData = [];
                    this.$message.error('网络错误');
                }
            },

            // 获取订单详情
            async getOrderDetail() {
                if(this.orderId == 0) return;
                let params = {
                    id: this.orderId
                }
                let res = await axios.post('order_info',params);
                if(res.data.code == 0) {
                    this.orderDetail.info = res.data?.order || {};
                    this.orderDetail.addrInfo = res.data?.addr;
                    this.orderDetail.applInfo = res.data?.appl || {};
                    this.orderDetail.goodsList = res.data?.pg || [];
                    this.orderDetail.ct = res.data?.ct || {};
                }
                console.log(res.data,'订单详情');
            },

            getParcelList() {
                // 返回Promise以便mounted中finally处理
                return this.getParcel(this.statusMap[this.activeIndex]);
            },
            // 搜索包裹
            async searchParcel() {
                try {
                    // 判断是否为包裹搜索模式（activeIndex: 0,1,3）还是订单搜索模式（activeIndex: 2）
                    const isParcelMode = [0, 1].includes(this.activeIndex);
                    const status = this.statusMap[this.activeIndex];
                    
                    // 构建请求参数
                    const params = isParcelMode 
                        ? {
                            wh: this.form.warehouseName,
                            wb: this.form.deliveryNum.trim(),
                            status
                          }
                        : {
                            wh: this.form.warehouseName,
                            no: this.form.orderNum.trim(),
                            status
                          };
                    
                    // 调用对应的搜索接口
                    const apiUrl = isParcelMode ? 'searchParcel' : 'order_package_status';
                    const res = await axios.post(apiUrl, params);
                    
                    if (res.data.code == 0) {
                        // 根据接口类型获取不同的数据结构
                        let listData = [];
                        const kdData = res.data.kd || [];
                        
                        listData = res.data.list || [];

                        // 创建物流信息映射表
                        const kdMap = kdData.reduce((map, kdItem) => {
                            if (!map[kdItem.pid]) map[kdItem.pid] = [];
                            map[kdItem.pid].push(kdItem);
                            return map;
                        }, {});

                        // 合并包裹数据与物流信息
                        this.parcelData = listData.map(pgItem => ({
                            ...pgItem,
                            logistics: (kdMap[pgItem.id] || [])[0] || null
                        }));


                        if (this.activeIndex === 3) {
                            this.getTWLogistics(this.parcelData);
                        }
                        console.log('搜索结果:', this.parcelData);
                    } else {
                        // 搜索失败或无数据时，确保清空数据
                        this.parcelData = [];
                        console.log('搜索无结果，已清空数据');
                    }
                    
                    // 强制更新视图
                    this.$nextTick(() => this.$forceUpdate());
                } catch (e) {
                    // 网络错误时也要清空数据
                    this.parcelData = [];
                    this.$message.error('网络错误');
                } finally {
                    // 由外层控制loading状态，这里不再处理
                }
            },

            // 确认收货
            OrderReceipt(row){
                try {
                    let params = {
                        no: row.order_no
                    };
                    axios.post('order_complete',params).then(res=>{
                        if(res.data.code == 0) {
                            // 更新当前行的状态为已收货
                            row.order_status = 3;
                            this.$message.success(res.data.msg || '確認收貨成功');
                            // 可选：重新获取列表数据以确保数据同步
                            // this.getParcelList();
                        }else {
                            this.$message.error(res.data.msg);
                        }
                    })
                }catch(err){
                    console.error('確認收貨錯誤信息',err);
                }
            },

            async delOrderRow(num) {
                try {
                    let params = {
                        no:num
                    };
                    let res = await axios.post('order_delete', params);
                    console.log(res);
                    if (res.data.code == 0) {
                        this.$message.success(res.data.msg || '刪除成功');
                        this.getParcelList();
                    } else {
                        this.$message.error(res.data.msg);
                    }

                }catch(err){
                    console.error('删除订单错误信息',err);
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },

            // 拒收包裹
            async handleRejectConfirm(row) {
                if (!row) return;
                try {
                    const res = await axios.post('refuse', { pid: row.id, rf: 1 });

                    if (res.data.code === '0') {
                        this.$message.success(res.data.msg || '拒收成功');
                        await this.getParcelList();
                    } else {
                        this.$message.error(res.data.msg || '拒收失败');
                    }
                } catch (e) {
                    this.$message.error('网络错误');
                }
            },

            // 重置
            resetInput() {
                this.form.warehouseName = '';
                this.form.deliveryNum = '';
                this.form.orderNum = '';
                this.getParcelList();
            },
            // 倉庫選擇
            onWarehouseChange() {
                // 清空当前数据，避免显示旧数据
                this.parcelData = [];
                this.form.deliveryNum = '';
                this.form.orderNum = '';
                
                // 设置加载状态
                this.tabLoading = true;
                
                // 调用搜索并在完成后关闭loading状态
                this.searchParcel()
                    .finally(() => {
                        this.tabLoading = false;
                    });
            },
            changeTabs(index) {
                // 清空搜索表单
                this.form.deliveryNum = '';
                this.form.orderNum = '';
                
                // 清空当前数据，避免使用错误的数据结构
                this.parcelData = [];
                
                // 更新索引
                this.activeIndex = index;
                this.currentPage = 1; // 强制重置页码
                
                // 重置仓库选择为第一项
                if (this.warehouseOptions.length > 0) {
                    this.activeName = this.warehouseOptions[0].id;
                    this.form.warehouseName = this.warehouseOptions[0].id;
                }
 
                // 设置标签页加载状态
                this.tabLoading = true;
                
                // 只调用getParcel获取初始数据，不再重复调用searchParcel
                this.getParcel(this.statusMap[this.activeIndex])
                    .finally(() => {
                        this.tabLoading = false;
                    });
            },

            // 获取指定仓库ID的包裹数量
            getWarehouseCount(warehouseId) {
                // 如果数据还未加载完成，返回0
                if (this.tabLoading || !this.parcelData || this.parcelData.length === 0) {
                    return 0;
                }
                
                // 统计当前仓库的包裹数量
                if (warehouseId === '') {
                    return this.parcelData.length; // 全部数据
                } else {
                    return this.parcelData.filter(item => String(item.wh_id) === String(warehouseId)).length;
                }
            },
            
            // 格式化选项卡标签，只有有数据时才显示数量
            formatTabLabel(item) {
                const count = this.getWarehouseCount(item.id);
                return count > 0 ? `${item.name} (${count})` : item.name;
            },
            
            changeWarehouse(tab) {

                // 同时更新两个属性以保持一致性
                this.activeName = tab.name;
                this.form.warehouseName = tab.name; // 更新表单中的仓库ID，确保searchParcel能获取到正确的参数

                this.currentPage = 1;
                
                // 设置加载状态
                this.tabLoading = true;
                
                this.searchParcel()
                    .finally(() => {
                        this.tabLoading = false;
                    });
            },
            // 分頁
            handleCurrentChange(page) {
                this.currentPage = page;
            },
            handleSizeChange(val) {
                this.currentPageSize = val
                this.currentPage = 1
            },
            // 物流信息
            async openLogistics(val) {
                let id = val.id;

                this.logisticsDialogVisible = true;
                // const data = {
                //     pid: id,
                // };

                try {
                    let res = await axios.get('package_logistics?pid=' + id);
                    if(res.data.code == 0) {
                        const list = res.data.data;
                        // 反转列表顺序后，给第一条（最新）加color属性
                        this.wuliuList = list.map(item=>({
                            timestamp: item.time,
                            content: item.context,
                            status: item.status
                        }));
                        if (this.wuliuList.length > 0) {
                            this.wuliuList[0].color = '#67c23a';
                        }
                    }else {
                        this.$message.error(res.data.msg || '获取物流信息失败');
                    }
                }catch(err){
                     this.$message.error('网络错误');
                  }
            },
            // 附加服務
            async openService(item) {
                this.serviceDialogVisible = true;
                const dictId = [...new Set(this.serviceList.map(item => item.dict_id))];
                this.serviceNames = [...new Set(this.serviceList.map(item => item.name))];
                this.firstCheckList = this.serviceList.filter(item => item.dict_id === dictId[0]);
                this.secondCheckList = this.serviceList.filter(item => item.dict_id === dictId[1]);
                this.goodsInfo = item;
                // 清空选择列表，确保多选框始终未选中
                this.firstSelectedCheckList = [];
                this.secondSelectedCheckList = [];
                try {
                    const res = await axios.post('get_service', {
                        pid: [this.goodsInfo.id]
                    });
                    if (res.data.code === '0') {
                        this.addServiceList = res.data.data;
                        console.log(this.addServiceList, '附加服務列表');
                    }
                } catch (e) {
                    this.$message.error('网络错误');
                }
            },
            closeService() {
                this.serviceDialogVisible = false;
                this.getParcelList();
                this.resetForm();
            },
            confirmService() {
                if (this.selectAddServiceList.length > 0) {
                    this.payServiceDialogVisible = true;
                    this.getParcelList();
                } else {
                    this.$message.warning('請選擇附加服務');
                }
            },
            // 支付附加服務
            closePayService() {
                this.payServiceDialogVisible = false;
                this.isAgree = false;
            },

            openPayService() {
                if (!this.isAgree) {
                    this.$message.error('請同意附加服務條款');
                    return;
                }
                this.payDialogVisible = true;
            },
            async submitPayService() {
                try {
                    // if (this.uMoney < this.tAmount) {
                    //     this.$message.error('T幣餘額不足，請儲值後再支付');
                    //     return;
                    // }
                    
                    const res = await axios.post('addService', {
                        pid: this.goodsInfo.id,
                        sid: this.selectLabels,
                        aid: this.goodsInfo.addser_id,
                        money: this.tAmount,
                    });
                    if (res.data.code == 0) {
                        this.payServiceDialogVisible = false;
                        this.isAgree = false;
                        this.$message.success(res.data.msg || '支付成功');
                        await this.openService(this.goodsInfo);
                        this.selectAddServiceList = [];
                    } else {
                        this.$message.error(res.data.msg || '支付失敗');
                    }
                } catch (e) {
                    this.$message.error('網絡錯誤');
                }
            },
            closeInService() {
                this.inServiceDialogVisible = false;
                // 只重置二级弹窗相关的数据
                this.goodsNum = {
                    count: 1,
                    deCount: 0,
                };
                this.oremarks = '';
                this.currentDialogItem = {};
            },

            // 拒收包裹
            openReject() {
                this.rejectDialogVisible = true;
            },

            // 包裹轉寄
            openTransfer() {
                this.firstForwardDialogVisible = true;
            },
            updateCheckService(val) {
                this.selectLabels = [...this.firstSelectedCheckList, ...this.secondSelectedCheckList].map(item => item.id).sort((a, b) => a - b);
                this.selectAddServiceList = this.serviceList.filter(item => this.selectLabels.includes(item.id)).map(item => ({
                    title: item.title,
                    amount: item.amount
                }));

            },
            // 备注编辑
            openRemarks(item) {
                this.goodsInfo = item;
                this.remarksDialogVisible = true;
            },
            openFirstRegistration() {
                this.secondForwardDialogVisible = true;
            },
            closeFirstRegistration() {
                this.secondForwardDialogVisible = false;
                this.resetForm();
            },
            openSecondRegistration() {
                this.thirdForwardDialogVisible = true;
            },
            closeSecondRegistration() {
                this.thirdForwardDialogVisible = false;
                this.resetForm();
            },

            refreshData() {
                this.getParcelList();
                return this.form= {
                    warehouseName: '',  //倉庫名稱
                    deliveryNum: '',  // 快遞單號
                    orderNum: '',  // 訂單編號
                }
            },

            resetForm() {
                this.ruleForm = {
                    userName: '',
                    mobile: '',
                    address: '',
                    remarks: '',
                    pickUpCode: '',
                };
                // this.waybillId = '';
                this.selectLabels = [];
                this.goodsNum = { count: 1, deCount: 0, };
                this.firstSelectedCheckList = [];
                this.secondSelectedCheckList = [];
                this.addServiceList = [];
                this.addApplicantForm = { name: '', mobile: '', idCard: '' };
                this.addCompanyForm = { companyCode: '', companyMobile: '' };
                this.addAddressForm = {region: '', name: '', idCard: '', fixedNumber: '', address: '', mobile: '', postalCode: '', label: 1, isDef: 0};
            },
            addresseeSubmit() {
                this.$refs.ruleForm.validate((valid) => {
                    if (valid) {
                        //校验成功
                        this.payDialogVisible = true;
                        this.$message({
                            message: '提交成功',
                            type: 'success'
                        });
                    } else {
                        // 校验失败
                        this.$message({
                            message: '請填寫完整資料',
                            type: 'error'
                        });
                    }
                });
            },
            remarksClose() {
                this.remarksDialogVisible = false;
                this.oremarks = '';
            },
            async remarksSubmit() {
                try {
                    const res = await axios.post('setOremarks', {
                        pid: this.goodsInfo.id,
                        rk: this.oremarks
                    })
                    if (res.data.code === '0') {
                        this.remarksDialogVisible = false;
                        this.$message.success(res.data.msg)
                        await this.getParcelList();
                    }
                    this.oremarks = '';

                } catch (e) {
                    this.$message.error('網絡錯誤');
                }
            },
            // 附加服務二級彈窗
            openInservice(item) {
                const titles = ['確認型號', '清點數量', '包裹拆分', '包裝氣泡棉', '包裝氣柱袋'];
                if (titles.includes(item.title)) {
                    this.showDialog = true;
                    this.currentDialogItem = item;
                    this.inServiceDialogVisible = true;
                } else {
                    this.showDialog = false;
                }
            },

            // 只允许输入数字，并自动跳到下一个输入框
            onInput(idx, e) {
                let val = e.target.value.replace(/\D/g, ''); // 只允许数字

                // 根据当前弹窗类型决定更新哪个数组
                if (this.payDialogVisible) {
                    this.$set(this.verifyCode, idx, val);
                } else {
                    this.$set(this.password, idx, val);
                }

                if (val && idx < 3) {
                    this.currentInput = idx + 1;
                    this.$nextTick(() => {
                        this.$refs.inputs[this.currentInput].focus();
                    });
                }
            },
            // 只允许聚焦到当前input
            onFocus(idx) {
                if (idx !== this.currentInput) {
                    this.$refs.inputs[this.currentInput].focus();
                }
            },
            // 退格时回到前一个输入框
            onBackspace(idx, e) {
                let currentArray = this.payDialogVisible ? this.verifyCode : this.password;
                if (!currentArray[idx] && idx > 0) {
                    this.currentInput = idx - 1;
                    this.$nextTick(() => {
                        this.$refs.inputs[this.currentInput].focus();
                    });
                }
            },
            handleSelectionChange(val) {
                this.selectedParcels = val;
                this.parcelVolume = this.selectedParcels.reduce((sum, item) => {
                    if (item.length && item.width && item.height) {
                        return sum + Math.ceil(item.length * item.width * item.height);
                    }
                    return sum;
                }, 0);
                console.log('包裹体积：', this.parcelVolume);
                console.log('selectedParcels', this.selectedParcels);
                this.totalWeight = val.reduce((total, item) => (total * 100 + (Number(item.scale) * 100 || 0)) / 100, 0);
                console.log('totalWeight', this.totalWeight);

            },
            handleShowMore() {
                this.isSwitch = !this.isSwitch;
            },
            // toggleExpand(index) {
            //     this.$set(this.expandedItems, index, !this.expandedItems[index]);
            // },
            // 查看附加服務
            async openVS(item) {
                try {
                    this.goodsInfo = item;
                    this.vsDialogVisible = true;
                    const id = {
                        pid: [this.goodsInfo.id]
                    }
                    const res = await axios.post('get_service', id);
                    if (res.data.code === '0') {
                        this.addServiceList = res.data.data;
                    }
                } catch (e) {
                    this.$message.error('网络错误');
                }
            },
            closeVS() {
                this.vsDialogVisible = false;
                this.goodsInfo = {};
            },
            // 打包回台
            handlePack() {
                let pid = this.selectedParcels.map(item => item.id);
                const flag = this.selectedParcels.length === 0 ? true : this.selectedParcels.every(item => item.goodstype_id === this.selectedParcels[0].goodstype_id);
                if (this.selectedParcels.length < 1) {
                    this.$message.warning('請選擇包裹');
                    return;
                }
                if(!flag) {
                    this.$message.warning('請選擇貨物類型一致的包裹');
                    return;
                }
                axios.post('get_service', {
                    pid
                })
                this.applicantDialogVisible = true;
            },
            // 切换申报人类型
            changeApplicantType(tab, event) {
                console.log(tab, event);
            },
            openApplicant() {
                this.applicantDialogVisible = false;
                this.applicantListDialogVisible = true;
                this.getApplicantList();
            },
            async searchApplicant() {
                this.searchLoading = true;
                try {
                    // 如果搜索值为空，则获取全部数据
                    if (!this.searchValue.trim()) {
                        await this.getApplicantList();
                        return;
                    }

                    // 根据当前选中的申报人类型（个人/公司）调用不同的接口
                    const type = this.activeChange === '1' ? 0 : 1;

                    const res = await axios.post('searchApplicant', {
                        // keyword: this.searchValue.trim(),
                        // type: type
                    });

                    if (res.data.code === '0') {
                        // 根据类型更新对应的列表
                        if (type === 0) {
                            this.applicantList = res.data.data;
                        } else {
                            this.companyList = res.data.data;
                        }
                        this.searchLoading = false;
                    } else {
                        this.$message.error(res.data.msg || '查詢失敗');
                    }
                } catch (err) {
                    console.error('searchApplicant error:', err);
                    this.$message.error('網絡錯誤');
                }
            },
            addApplicant() {
                this.$nextTick(() => {
                    if (this.$refs.addApplicantForm) {
                        this.$refs.addApplicantForm.resetFields();
                    }
                    if (this.$refs.addCompanyForm) {
                        this.$refs.addCompanyForm.resetFields();
                    }
                });
                this.addApplicantDialogVisible = true;
            },
            closeAddApplicant() {
                this.addApplicantDialogVisible = false;
                this.$nextTick(() => {
                    if (this.$refs.addApplicantForm) {
                        this.$refs.addApplicantForm.resetFields();
                    }
                    if (this.$refs.addCompanyForm) {
                        this.$refs.addCompanyForm.resetFields();
                    }
                });
                this.resetForm();
            },
            async submitAddApplicant() {
                try {
                    // 根据 activeChange 选择表单
                    const formRef = this.activeChange === '1' ? 'addApplicantForm' : 'addCompanyForm';
                    const valid = await this.$refs[formRef].validate();
                    if (valid) {
                        const formUser = {
                            uname: this.addApplicantForm.name,
                            mb: this.addApplicantForm.mobile,
                            card: this.addApplicantForm.idCard,
                            ty: 0
                        };
                        const formCompany = {
                            code: this.addCompanyForm.companyCode,
                            mb: this.addCompanyForm.companyMobile,
                            ty: 1
                        };
                        const res = await axios.post('addApplicant', this.activeChange === '1' ? formUser : formCompany);
                        if (res.data.code === '0') {
                            this.$message.success(res.data.msg);
                            this.addApplicantDialogVisible = false;
                            this.resetForm();
                            this.getApplicantList();
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    }
                } catch (err) {
                    if (err && err.message && err.message.indexOf('validate') > -1) {
                        console.error(err.message, 'err.message');
                    } else {
                        this.$message.error('網絡錯誤');
                    }
                }
            },
            async getApplicantList(){
                const type = {
                    ty: this.activeChange === '1' ? 0 : 1
                };
                console.log(type, 'type');
                const res = await axios.get('addApplicant');
                if(res.data.code === '0'){
                    console.log(res.data.data, 'res.data.data');
                    const data = res.data.data;
                    this.applicantList = data.filter(item => item.type === 0);
                    console.log(this.applicantList, 'applicantList');
                    this.companyList = data.filter(item => item.type === 1);
                    console.log(this.companyList, 'companyList');
                }
            },
            selectApplicant(item) {
                this.userInfo = item;
                this.$nextTick(() => {
                    // 根据当前激活的标签页选择对应的列表
                    const selector = this.activeChange === '1' ? '.applicant-item' : '.company-item';
                    const items = document.querySelectorAll(selector);
                    items.forEach(el => {
                        if (el.dataset.id === item.id.toString()) {
                            el.classList.add('selected');
                        } else {
                            el.classList.remove('selected');
                        }
                    });
                });
                console.log(this.userInfo, '用戶信息');
            },
            openAddrList() {
                const selectedApplicant = document.querySelector('.applicant-item.selected');
                if (selectedApplicant) {
                    this.applicantListDialogVisible = false;
                    this.addrlsDialogVisible = true;
                    this.getAddress();
                } else {
                    this.$message.warning('請先選擇申報人');
                }
            },
            selectAddress(item) {
                // 添加选中状态
                this.addrInfo = item;
                this.$nextTick(() => {
                    const items = document.querySelectorAll('.address-item');
                    items.forEach(el => {
                        if (el.dataset.id === item.id.toString()) {
                            el.classList.add('selected');
                        } else {
                            el.classList.remove('selected');
                        }
                    });
                });
                console.log(this.addrInfo, '地址信息');
            },
            getRegionList() {
                axios.get('/index/user/addAddress').then(res=> {
                    if(res.data.code === '0') {
                        console.log('地址信息', res.data);
                        this.areaList = (res.data?.area_list || []).map(city => ({
                            value: city.id,
                            label: city.name,
                            children: city.list.map(district => ({
                                value: district.id,
                                label: district.name
                            }))
                        }));
                        this.identityList = (res.data?.label_list || []).map((item, index) => ({
                            label: index + 1,  // 保持原始id用于提交
                            name: item.name,
                            id: item.id
                        }));
                        if (this.identityList.length > 0) {
                            this.addAddressForm.label = 1;  // 使用新的label值
                        }
                        console.log('this.areaList', this.areaList);
                        console.log('this.identityList', this.identityList);

                    } else {
                        this.$message.error(res.data.msg);
                    }
                }).catch(err => {
                    this.$message.error('網絡錯誤');
                });
            },
            handleRadioInput(value) {
                console.log('Selected radio value:', value);

            },
            changeArea(value) {
                console.log('Selected area:', value);
            },
            openAddAddress() {
                this.addAddressDialogVisible = true;
                this.getRegionList();
                this.resetForm();
                // 只在表单初始化时重置验证状态
                this.$nextTick(() => {
                    if (this.$refs.addAddressForm) {
                        this.$refs.addAddressForm.resetFields();
                    }
                });
            },
            getAddress() {
                // const applicantId = id ;
                axios.get('/index/user/addresses').then(res => {
                    if (res.data.code === '0') {
                        this.addressList = res.data.data.reverse();
                        console.log('地址列表', this.addressList);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                }).catch(err => {
                    this.$message.error('網絡錯誤');
                });
            },
            closeAddAddress() {
                this.addAddressDialogVisible = false;
                this.resetForm();
            },
            async submitAddAddress() {
                try {
                    // 先进行表单验证
                    const valid = await this.$refs.addAddressForm.validate();
                    if (valid) {
                        const selectedLabel = this.identityList.find(item => item.label === this.addAddressForm.label);
                        const identityLabel = selectedLabel ? selectedLabel.id : null;
                        const formAddress = {
                            type: 0,
                            isdef: this.addAddressForm.isDef,
                            city: this.addAddressForm.region[0],    // 取数组第一个值作为city
                            dist: this.addAddressForm.region[1], // 取数组第二个值作为district
                            detail: this.addAddressForm.address,
                            name: this.addAddressForm.name,
                            mobile: this.addAddressForm.mobile,
                            card: this.addAddressForm.idCard,
                            number: this.addAddressForm.fixedNumber,
                            postal: this.addAddressForm.postalCode,
                            label: identityLabel,
                            appl: this.userInfo.id,
                        }
                        console.log('formAddress', formAddress);
                        const res = await axios.post('/index/user/addAddress', formAddress);
                        if (res.data.code === '0') {
                            this.$message.success(res.data.msg);
                            await this.getAddress();
                            await this.resetForm();
                            this.addAddressDialogVisible = false;
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    }else{
                        return false;
                    }

                } catch (err) {
                    if (err && err.message && err.message.indexOf('validate') > -1) {
                        console.error(err.message, 'err.message');
                    } else {
                        this.$message.error('網絡錯誤');
                    }
                }
            },
            handleGlobalClick(event) {
                // 如果点击的是下一步按钮，不执行清除选中状态
                if (event.target.closest('.dialog-footer')) {
                    return;
                }
                const selectors = ['.applicant-item', '.address-item'];
                selectors.forEach(selector => {
                    if (!event.target.closest(selector)) {
                        document.querySelectorAll(selector).forEach(el => el.classList.remove('selected'));
                    }
                });
            },

            // 关闭集运订单
            closeTraOrder() {
                this.traOrderDialogVisible = false;
                this.$nextTick(() => {
                    if (this.$refs.myTable) {
                        this.$refs.myTable.clearSelection();
                    }
                });
                // 清空选中包裹
                this.handleSelectionChange([]);
                this.selectedParcels = [];
                this.totalWeight = 0;
                // 重置弹窗相关数据
                this.selectFee = {};
                this.selectedChannelIndex = null;
                this.selectTran = {};
                this.firstTaxChecked = false;
                this.secondTaxChecked = false;
                this.timeoutInfo = {};
                this.lostInfo = {};
                // 只重置orderForm中与弹窗相关的字段
                this.orderForm = Object.assign({}, this.orderForm, {
                    transportType: '',
                    deliveryRadio: '1',
                    taxId: null,
                    scale_unit: null,
                    volume_unit: null,
                    totalAmount: 0,
                    paster: false,
                    specifiedAddr: '',
                    feeRadio: 1,
                    tp: null,
                    lostPrice: 300,
                    fr_weight: null,
                    fl_weight: null
                });
            },

            // 集运订单
            async openTraOrder() {
                const selectedAddress = document.querySelector('.address-item.selected');
                if(selectedAddress){
                    this.addrlsDialogVisible = false;
                    this.traOrderDialogVisible = true;
                    await this.getRegionList();
                    let id = this.selectedParcels.map(item=>item.id).join();;
                    let wareHouseId = [... new Set(this.selectedParcels.map(item=>item.wh_id))].join(); // 倉庫ID
                    this.wareHouseId = wareHouseId;
                    console.log('倉庫ID',wareHouseId)
                    let res = await axios.get(`corder?pid=${id}&wid=${wareHouseId}`);
                    if(res.data.code === '0') {
                        let list = res.data.count_tp_list;
                        if(Number(this.totalWeight) > 50) {    // 重量大于50公斤不能选择海快
                            // 運費列表
                            this.feeList = list.filter(item =>
                                item.wh_id == wareHouseId &&
                                item.transport != 15)
                            console.log('运费超过50',this.feeList)
                        } else {
                            this.feeList = list.filter(item =>
                                item.wh_id == wareHouseId)
                            console.log('运费不超过50',this.feeList)
                        }
                        // 返回的运输类型
                        if(Number(this.selectedParcels[0].goodstype_id === 3) || Number(this.selectedParcels[0].goodstype_id) === 4 || Number(this.selectedParcels[0].goodstype_id) === 5) {
                            this.transportTypes = res.data.wt_list.list
                                .filter(item => item.type == 17)
                                .map(item => ({
                                    transport: item.type,
                                    name: item.name
                                }));
                        }else {
                            this.transportTypes = res.data.wt_list.list.map(item => ({
                                transport: item.type,
                                name: item.name
                            }))
                        }

                        // 运费列表
                        console.log('运费列表',this.feeList)
                        // 運輸類型
                        console.log('運輸類型',this.transportTypes)

                        // 自动选中第一个运输方式，确保selectFee有初始值
                        if (this.feeList.length > 0 && this.transportTypes.length > 0) {
                            this.selectChannel(0);
                        } else {
                            console.log('feeList或transportTypes为空:', {
                                feeListLength: this.feeList.length,
                                transportTypesLength: this.transportTypes.length
                            });
                        }

                        this.timeoutInfo = res.data.insure_list[0];
                        this.lostInfo = res.data.insure_list[1];
                        console.log('超時保價參數',this.timeoutInfo);
                        console.log('丢失保价参数',this.lostInfo)
                    }
                }else {
                    this.$message.warning('請先選擇收貨地址');
                }
            },


            selectChannel(index) {
                console.log('transportTypes:', this.transportTypes);
                this.selectFee = {};
                this.selectedChannelIndex = index;
                this.selectTran = this.transportTypes[index];
                
                // 禁止选择超重海快
                if (Number(this.transportTypes[index].transport) === 15 && this.totalWeight > 50) {
                    this.$message.warning('該運輸方式不支持海快（超過50KG）');
                    
                    // 清空所有计算数据
                    this.selectFee = {};
                    this.firstTaxChecked = false;
                    this.secondTaxChecked = false;
                    this.selectFee.taxpre = 0;
                    this.selectFee.scale_unit = 0;
                    this.selectFee.volume_unit = 0;
                    this.selectFee.fr_weight = 0;
                    this.selectFee.fl_weight = 0;
                    this.orderForm.totalAmount = 0;
                    
                    return; // 不继续处理，防止选择不支持的运输方式
                }
                
                console.log('選擇的運輸項:', this.selectTran);

                // 選中的運費詳情
                if (Number(this.transportTypes[index].transport) !== 15) {
                    // 非海快运输方式，直接选择第一个运费方案
                    let goodsTypeId = this.selectedParcels[0].goodstype_id;
                    console.log('goodsTypeId', goodsTypeId);
                    if (this.feeList && this.feeList.length > 0) {
                        if (this.selectTran.transport == 16) {
                            this.selectFee = this.feeList.filter(item => (
                                item.transport == this.selectTran.transport &&
                                item.goodstype == goodsTypeId
                            ))[0];
                        } else {
                            this.selectFee = this.feeList.find(item => item.transport === this.selectTran.transport) || {};
                        }
                        console.log('選擇的運費列表1', this.selectFee);
                        this.orderForm.scale_unit = this.selectFee?.scale_unit || 0;
                        this.orderForm.volume_unit = this.selectFee?.volume_unit || 0;
                        this.orderForm.fr_weight = this.selectFee?.fr_weight || 0;
                        this.orderForm.fl_weight = this.selectFee?.fl_weight || 0;
                        console.log('this.orderForm', this.orderForm)
                    }
                    console.log('非海快运费列表', this.feeList)
                } else {
                    // 海快运输方式，需要根据feeRadio筛选
                    // 确保feeRadio有默认值
                    if (!this.orderForm.feeRadio) {
                        this.orderForm.feeRadio = 1; // 默认选择包税
                    }
                    console.log('海快运费列表', this.feeList)
                    console.log('海快运输方式，feeRadio:', this.orderForm.feeRadio);
                    this.feeChange(this.orderForm.feeRadio);
                }

                this.calculateTotalAmount(this.orderForm.lostPrice);
                console.log(this.orderForm.totalAmount, 'orderForm.totalAmount');
            },

            feeChange(val) {
                console.log('feeChange', val);
                console.log('selectTran.transport', this.selectTran.transport);
                console.log('feeList', this.feeList);

                this.selectFee = {};
                if (this.feeList && this.feeList.length > 0) {
                    if(Number(this.selectTran.transport) === 15){
                        this.feeOpt = this.feeList.filter(item=>item.transport == this.selectTran.transport && item.type == 1);
                        console.log('++++++++++++++++++++', this.feeOpt);
                        // 海快运输方式，根据style筛选
                        this.selectFee = this.feeOpt.find(item => item.style === val) || {};
                        console.log('找到的selectFee:', this.selectFee);

                        this.orderForm.taxpre = this.selectFee?.taxpre || 0;
                        this.orderForm.scale_unit = this.selectFee?.scale_unit || 0;
                        this.orderForm.volume_unit = this.selectFee?.volume_unit || 0;
                        this.orderForm.fr_weight = this.selectFee?.fr_weight || 0;
                        this.orderForm.fl_weight = this.selectFee?.fl_weight || 0;
                        console.log('選擇的運費列表2', this.selectFee);
                    }
                    else {
                        // 非海快运输方式，清空selectFee
                        this.selectFee = {};
                        this.orderForm.scale_unit = 0;
                        this.orderForm.volume_unit = 0;
                        this.orderForm.fr_weight = 0;
                        this.orderForm.fl_weight = 0;
                    }
                    this.calculateTotalAmount(this.orderForm.lostPrice);
                }
            },

            getFeeByTransport(transport) {
                if (transport == 15) {
                    return this.feeList.find(f => f.transport == 15 && f.type == 1 && f.style == this.orderForm.feeRadio) || {};
                } else {
                    return this.feeList.find(f => f.transport == transport) || {};
                }
            },


            openProtocol(type) {
                this.protocolDialogVisible = true;
                this.protocolType = type;
            },

            // 计算付款金额
            calculateTotalAmount(lost_price){
                if(this.selectTran.transport){
                    this.orderForm.transportType = this.selectTran.transport;
                }
                
                // 保存丢失保价金额，供计算属性使用
                this.orderForm.lostPrice = lost_price;
                
                // 使用计算属性来计算总金额
                this.orderForm.totalAmount = this.totalTransportAmount;
                
                console.log('实付金额:', this.orderForm.totalAmount);
                console.log('運輸方式++++++++++++++++++++++:', this.orderForm.transportType);
            },
            handleLost(val) {
                this.orderForm.lostPrice = val;
                this.calculateTotalAmount( this.orderForm.lostPrice);
                console.log('输入保价金额：',val)
            },

            checkedLostStatus(val) {
                console.log(val,'保價狀態切換');
                if(!this.secondTaxChecked) {
                    this.orderForm.lostPrice = 0;
                }else {
                    // 修复：当切换为true时，如果lostPrice为undefined/null/NaN，设置默认值
                    if (!this.orderForm.lostPrice || isNaN(this.orderForm.lostPrice)) {
                        this.orderForm.lostPrice = 0;
                    }
                }
                this.calculateTotalAmount(this.orderForm.lostPrice);
                console.log('保价金额+++++++++++++++：',this.orderForm.lostPrice);
                
            },

            async submitSendOut() {
                const formData = {
                    pid:[...this.selectedParcels.map(item => item.id)], // 包裹ID
                    wh_id: this.selectedParcels[0].wh_id, // 倉庫ID
                    transport: this.orderForm.transportType, // 運輸方式
                    tw: this.orderForm.deliveryRadio, // 送貨方式
                    addr: this.addrInfo.id,     // 收貨地址ID
                    applicant: this.userInfo.id,    // 申報人ID
                    tp: this.selectFee.id,   // 计算运输方式ID
                    cs: this.firstTaxChecked, // 超時保價
                    bj: this.orderForm.lostPrice, // 丢失保价金额
                    // insures: (() => {
                    //     const insures = [];
                    //     if (this.firstTaxChecked) {
                    //         insures.push({ id: this.timeoutInfo.id, price: 0 }); // 超时保价
                    //     }
                    //     if (this.secondTaxChecked) {
                    //         insures.push({ id: this.lostInfo.id, price: this.orderForm.lostPrice }); // 丢失保价
                    //     }
                    //     return insures;
                    // })(),
                    bz:'',
                    // type:1,
                    // bank:'',
                    paster:this.orderForm.paster,
                }
                try {
                    if(Number(this.orderForm.deliveryRadio) === 2){
                        if(!this.orderForm.specifiedAddr && this.orderForm.specifiedAddr.length === 0) {
                            this.$message.warning('請選擇到店自取地址');
                            return;
                        }
                    }
                    if (!this.selectFee.id) {
                        this.$message.warning('請選擇運費方案');
                        return;
                    }
                    console.log('formData', formData);

                    // 先显示确认对话框
                    await this.$confirm(`確定將${this.selectedParcels.length}個包裹打包回臺？`, '', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        confirmButtonClass: 'el-button--danger',
                    });

                    // 用户确认后发送请求
                    let res = await axios.post('corder',formData);
                    console.log(res,'订单提交信息');

                    if(Number(res.data.code) === 0) {
                        if(res.data.data){
                            this.orderId = res.data.data;
                            this.openOrderPack();
                        }
                        this.$message.success(res.data.msg);
                        await this.getParcelList();
                        this.traOrderDialogVisible = false;


                        // 清空选中包裹
                        this.handleSelectionChange([]);
                        this.selectedParcels = [];
                        this.totalWeight = 0;
                    }else {
                        this.$message.error(res.data.msg);
                    }
                }catch(err){
                    if (err === 'cancel') {
                        // 用户取消操作
                        this.$message({
                            type: 'info',
                            message: '已取消操作'
                        });
                    } else {
                        console.error('submitSendOut error:', err);
                        this.$message.error('网络错误');
                    }
                }
            },

            async openOrderPack() {
                try {
                    this.addrlsDialogVisible = false;
                    setTimeout(() => {
                        this.orderPackDialogVisible = true;
                    }, 800);
                    let params = {
                        id: this.orderId,
                        // id: 38
                    }
                    axios.post('order_info',params).then( res => {
                        if(Number(res.data.code) === 0) {
                            this.orderDetail.info = res.data?.order || {};
                            this.orderDetail.addrInfo = this.addrInfo || res.data?.addr;
                            this.orderDetail.applInfo = res.data?.appl || {};
                            this.orderDetail.goodsList = res.data?.list || [];
                            this.orderDetail.ct = res.data?.ct || {};
                            console.log('訂單詳情地址',this.orderDetail.addrInfo)
                            if(res.data.oi) {
                                this.orderDetail.timeoutInfo = res.data.oi.find(item=> item.insure_id === 1);
                                this.orderDetail.lostInfo = res.data.oi.find(item=> item.insure_id === 2);
                            }
                            console.log(this.orderDetail.lostInfo,'丢失保价信息');
                            console.log(res,'訂單閒情');
                        }
                    })
                } catch(err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },

            // 修改地址
            async editAddr() {
                this.addrlsDialogVisible = true;
                await this.getAddress();
            },

            async editOrderAddr(id) {
                let addrId = id;
                console.log('修改地址打开了');
                this.orderAddrDialogVisible = true;
                await this.getAddress();
            },

            openPayDialog(row) {
                this.orderRow = row;
                console.log('支付订单信息', this.orderRow);
                if (this.uNouse) {
                    this.payOrderDetail();
                } else {
                    this.payDialogVisible = true;
                    // 重置验证码输入状态
                    this.verifyCode = ['', '', '', ''];
                    this.currentInput = 0;
                    // 自动发送验证码
                    if (Number(this.uMoney) < Number(this.orderForm.totalAmount || this.orderRow.tb_money)) {
                        return
                    } else {
                        this.sendSmsCode();
                    }
                }
            },

            // 支付订单
            async payOrderDetail() {
                try {
                    let params = {
                        id: this.orderRow.id,
                        // password: this.uPassword // 如果后端需要密码，带上
                    };
                    console.log('支付参数',params);
                    let res = await axios.post('order_pay', params);

                    if (Number(res.data.code) === 0) {
                        this.orderPackDialogVisible = false;
                        this.$message.success(res.data.msg);
                        await this.getParcelList();
                    } else {
                        this.$message.error(res.data.msg || '支付失败，请稍后再试');
                    }
                    this.payDialogVisible = false;
                } catch (err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },

            // 发送短信验证码
            async sendSmsCode() {
                try {
                    if (this.isCountingDown) {
                        return;
                    }

                    let params = {
                        type: 2,
                        mobile: this.uMobile
                    };

                    let res = await axios.post('/index/transport/getCode', params, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    if (Number(res.data.code) === 1) {
                        this.$message.success('验证码发送成功');
                        this.startCountdown();
                    } else {
                        this.$message.error(res.data.msg || '验证码发送失败');
                    }
                } catch (err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },

            // 开始倒计时
            startCountdown() {
                this.isCountingDown = true;
                this.countdown = 90;

                const timer = setInterval(() => {
                    this.countdown--;
                    if (this.countdown <= 0) {
                        this.isCountingDown = false;
                        this.countdown = 90;
                        clearInterval(timer);
                    }
                }, 1000);
            },

            // 验证验证码并支付
            async verifyCodeAndPay() {
                try {
                    const code = this.verifyCode.join('');

                    // 验证验证码
                    let params = {
                        type: 2,
                        mobile: this.uMobile,
                        code: code
                    };

                    // 调用后端验证码验证接口
                    let verifyRes = await axios.post('/index/user/payCode', params,{
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    if (Number(verifyRes.data.code) === 0) {
                        // 验证码正确，清空验证码
                        this.verifyCode = ['', '', '', ''];
                        console.log(this.verifyRes,1111111111111111111111111111);
                        
                        this.currentInput = 0;

                        // 调用支付方法
                        await this.payOrderDetail();
                    } else {
                        // 验证码错误
                        this.$message.error(verifyRes.data.msg || '验证码错误，请重新输入');
                        // 清空验证码重新输入
                        this.verifyCode = ['', '', '', ''];
                        this.currentInput = 0;
                        this.$nextTick(() => {
                            if (this.$refs.inputs && this.$refs.inputs[0]) {
                                this.$refs.inputs[0].focus();
                            }
                        });
                    }

                } catch (err) {
                    this.$message.error('验证码验证失败，请稍后再试');
                    // 清空验证码重新输入
                    this.verifyCode = ['', '', '', ''];
                    this.currentInput = 0;
                    this.$nextTick(() => {
                        if (this.$refs.inputs && this.$refs.inputs[0]) {
                            this.$refs.inputs[0].focus();
                        }
                    });
                }
            },

            // 待出貨
            changeAddress() {
                this.addrlsDialogVisible = true;
            },
            startCountdown() {
                if (this.countingDown) return;
                this.countingDown = true;
                this.countdown = 25;
                const timer = setInterval(() => {
                    this.countdown--;
                    if (this.countdown <= 0) {
                        clearInterval(timer);
                        this.countingDown = false;
                        this.countdown = 25;
                    }
                    console.log(this.countdown, 'countdown');
                }, 1000);
            },
            handleNoNotice() {
                // TODO: 处理"收不到预先委任通知"按钮点击
            },


            forwardNumber(value) {
                return Math.ceil(Number(value) * 10) / 10;
            },
            toBindLine() {
                axios.get('/index/third_party/line_user_bind',{
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                }).then(res => {
                    console.log(res,'line_login');
                    if(Number(res.data.code) === 1) {
                        window.location.href = res.data.url;
                    }                    
                })
            },
            getCookie(name) {
                return document.cookie
                    .split('; ')
                    .find(row => row.startsWith(`${name}=`))
                    ?.split('=')[1];
            },
            lineBind() {
                this.lineStatus = lineStatus; // LINE登录状态
                this.bindRobot = lineBot; // 是否扫码机器人
                
                // 检查是否是从登录页面直接过来的
                const fromLogin = sessionStorage.getItem('fromLogin');
                if (fromLogin === 'true') {
                    // 是从登录页面过来的，根据lineStatus和bindRobot状态显示对话框
                    if (this.bindRobot == 1 && this.lineStatus == 1) {
                        this.bindLineDialogVisible = false;
                    } else {
                        this.bindLineDialogVisible = true;
                    }
                    // 清除标记，这样当页面刷新或再次进入时不会显示对话框
                    sessionStorage.removeItem('fromLogin');
                } else {
                    // 不是从登录页面过来的，不显示对话框
                    this.bindLineDialogVisible = false;
                }
                
                console.log(this.lineStatus, this.bindRobot, 'lineStatus');
            },
            async shareLogistics(row) {
                let logisticsNum = `http://www.jiyun.com/index/transport/logistics.html?waybill=${row.waybill}&wbname=${row.wbname}`;
                localStorage.setItem('logisticsNum', logisticsNum);
                utils.copyText(
                    logisticsNum,
                    () => {
                        // 成功回调
                        this.$message({
                            message: '已復製',
                            type: 'success'
                        });
                    },
                    (err) => {
                        // 失败回调
                        console.error('複製失敗:', err);
                        this.$message({
                            message: '複製失敗',
                            type: 'error'
                        });
                    }
                );
            },

            // 獲取台灣物流
            async getTWLogistics(data) {
                try {
                    if (data && data.length > 0) {
                        // 使用 Promise.all 来并发处理所有请求
                        const promises = data.map(async (item) => {
                            let wbname = item.wbname;
                            let waybill = item.waybill;
                            let params = {
                                name: wbname,
                                bill: waybill
                            }

                            try {
                                const res = await axios.post('tw_logistics', params);
                                if (res.data.code == 0) {
                                    return res.data.data; // 返回台湾物流数据
                                }
                                return null;
                            } catch (error) {
                                console.error(`获取台湾物流数据失败 (${wbname}-${waybill}):`, error);
                                return null;
                            }
                        });

                        // 等待所有请求完成
                        const results = await Promise.all(promises);

                        // 收集所有有效的台湾物流数据
                        const allTWLogistics = [];
                        results.forEach(result => {
                            if (result && Array.isArray(result)) {
                                allTWLogistics.push(...result);
                            }
                        });

                        // 将所有台湾物流数据赋值给 this.TWLogistics
                        this.TWLogistics = allTWLogistics;
                        console.log('台灣物流接口所有数据:', this.TWLogistics);

                        // 将 TWLogistics[0] 的数据作为 tw_info 添加到 parcelData 的每个项中
                        if (this.TWLogistics.length > 0) {
                            const twInfo = this.TWLogistics[0];
                            this.parcelData = this.parcelData.map(item => ({
                                ...item,
                                tw_info: twInfo
                            }));
                            console.log('已将 TWLogistics[0] 作为 tw_info 添加到每个包裹项中:', twInfo);
                            console.log('更新后的包裹数据:', this.parcelData);
                        }
                    }

                } catch (err) {
                    console.error('TWLogistics', err);
                }
            },
            openTWLogistics(row){
                this.getTWLogistics([row])
            }
        }
    })
</script>
