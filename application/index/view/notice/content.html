
<!-- 方法 1: 包含公共资源文件 -->
{include file="common/resources" /}
<!-- 额外需要的CSS文件 -->
{load href="__CDN__/assets/css/notice.css" /}

<div class="nav">
    <img src="__CDN__/assets/img/pc/nav_logo.png" alt="" style="height: 50%; cursor: pointer;" onclick="window.location.href='{:url('/index/login/login')}'">
    <div class="nav-right">
        <div style="display: flex; align-items: center; gap: 8px;">
            <img src="__CDN__/assets/img/pc/new_index/parcel18.svg" alt="APP下載">
            <a href="javascript:void(0)" class="terms-link" onclick="window.scrollTo({top: document.documentElement.scrollHeight, behavior: 'smooth'})">APP下載</a>
        </div>
        <!-- <div style="display: flex; align-items: center; gap: 8px;">
            <img src="__CDN__/assets/img/pc/new_index/parcel19.svg" alt="通知公告">
            <a href="#" class="terms-link">通知公告</a>
        </div> -->
<!--        <a href="{:url('/index/login/tran_agree')}" class="fs14 fff fw">EZ集運通條款</a>-->
    </div>
</div>

<div class="user_main">
    <div style="margin-bottom: 24px;">
        <div class="user_art_title clearfix">
            <div class="ellipsis"><span style="cursor: pointer;" onclick="window.location.href = '{:url('notice/index')}'">通知公告</span>  <img src="__CDN__/assets/img/pc/help_arr.png" class="ml8 mr8"><span class="color_red"><?=$info['title']?></span></div>
        </div>
        <div class="article_div">
            <h2 class="title" style="text-align:center;"><?=$info['title']?></h2>
            <div class="content">
                <?=$info['content']?>
            </div>
        </div>
        <div class="navigation">
            <?php
                if(!empty($up_info)){
            ?>
            <a href="<?=url('notice/details',array('id'=>$up_info['id']))?>">上一篇：<?=$up_info['title']?></a>
            <?php
                }
            ?>
            <?php
                if(!empty($down_info)){
            ?>
            <a style="float:right" href="<?=url('notice/details',array('id'=>$down_info['id']))?>">下一篇：<?=$down_info['title']?></a>
            <?php
                }
            ?>
        </div>
    </div>
</div>
{include file="common/footer" /}
<style>
    html,
    body {
        margin: 0;
        padding: 0;
        height: 100%;
        background-color: #edefef;
    }

    .user_main {
        padding: 24px;
        margin: 24px auto;
        border-radius: 4px;
        max-width: 1200px;
        min-height: 898px;
        background-color: #fff;
    }

    .content {
        padding: 20px 0;
        color: #666;
        font-size: 14px;
        line-height: 24px;
    }

    .article_div .title {
        font-size: 20px;
        line-height: 30px;
    }

    .navigation {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }

    .navigation a {
        color: #666;
        text-decoration: none;
    }

    .navigation a:hover {
        color: #EF436D;
    }

    .user_art_title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .color_red {
        color: #EF436D;
    }

    .ellipsis {
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
</style>