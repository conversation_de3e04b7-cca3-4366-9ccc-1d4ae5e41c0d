{include file="common/meta" /}
{load href="__CDN__/assets/css/element-ui-index.css,__CDN__/assets/css/login.css" /}
{load
href="__CDN__/assets/js/vue.js,
__CDN__/assets/js/axios.min.js,
__CDN__/assets/js/lodash.min.js,
__CDN__/assets/js/element-ui-index.js"
/}

<div id="register-container" class="container" style="position: relative;">
    <img src="__CDN__/assets/img/pc/register_bg.png" alt="" style="width: 100%; height: 100%;">
    <div class="user-section login-section register-section" style="position: absolute;top: 15%; left: 50%; transform: translateX(-50%);">
        <div class="login-logo" style="text-align:center;margin-top:32px;margin-bottom:8px;">
            <img src="__CDN__/assets/img/pc/ez_logo.png" alt="{:__('EZtransport')}" style="height:56px;">
        </div>
        <div class="login-title"
             style="text-align:center;font-size:24px;color:#333333;margin-bottom:24px;">{{lineStatus ? '綁定手機號' : ' 註冊'  }} 
        </div>
        <div class="login-main">
            <form name="form1" class="form-vertical" method="POST" action="">
                <input type="hidden" name="invite_user_id" value="0"/>
                <input type="hidden" name="url" value="{$url|htmlentities}"/>
                {:token()}
                
                <!-- 手机号输入框 -->
                <div class="form-group">
                    <div class="ez-form-label"><span class="required">*</span>{:__('Mobile phone number')}</div>
                    <el-input v-model="form.mobile" placeholder="請輸入手機號碼" name="account" maxlength="10"
                             @input="onMobileInput" class="ez-phone-input">
                        <el-select v-model="countryCode" slot="prepend" placeholder="+886">
                            <el-option label="+886" value="+886"></el-option>
                        </el-select>
                    </el-input>
                </div>
                
                <!-- 验证码输入框 -->
                <div class="form-group">
                    <div class="ez-form-label"><span class="required">*</span>{:__('Captcha')}</div>
                    <div class="ez-verification-wrapper">
                        <el-input v-model="form.captcha" placeholder="請輸入簡訊驗證碼" name="captcha" maxlength="6"></el-input>
                        <button type="button" :disabled="isCaptchaDisabled" @click="sendVerificationCode" 
                                  class="ez-send-code-btn">
                            {{ captchaText }}
                        </button>
                    </div>
                </div>

                <!-- 协议同意复选框 -->
                <div class="ez-agreement-wrapper">
                    <el-checkbox v-model="form.checked" class="ez-checkbox">
                        <span class="fs12">我已閱讀並同意</span>
                        <a href="/index/login/member" class="ez-link-text fs12">《{:__('Member terms')}》</a>
                        <a href="/index/login/user" class="ez-link-text fs12">《{:__('Personal data security strategy')}》</a>
                    </el-checkbox>
                </div>

                <!-- 注册按钮 -->
                <div class="ez-btn-wrapper">
                    <!-- <button type="button" class="ez-register-btn" @click="submitReg">{:__('Register successful')}</button> -->
                    <button type="button" class="ez-register-btn" @click="submitReg">{{lineStatus ? '認證' : '完成註冊'}}</button>
                </div>
                
                <!-- LINE登录按钮 -->
                <div v-if="!lineStatus" class="ez-btn-wrapper">
                    <button type="button" class="ez-line-btn" @click="submitLineLogin">{:__('Line login')}</button>
                </div>
                <div v-if="!lineStatus" class="ez-login-link">
                    <span>{:__('Already have an account')}</span>
<!--                    <a href="{:url('login/login')}?url={$url|urlencode|htmlentities}" class="ez-link-text">{:__('To login')}</a>-->
                    <a href="{:url('login/login')}" style="text-decoration: underline" class="ez-link-text">{:__('To login')}</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>

var line ='<?=$lineid?>';
console.log(line, 'line');

    const app = new Vue({
        el: '#register-container',
        data() {
            return {
                captchaText: "發送驗證碼",
                isCaptchaDisabled: false,
                countdown: 180, // 设置为180秒
                countryCode: '+886',
                form: {
                    mobile: '',
                    captcha: '',
                    password: '',
                    checked: false,
                },
                lineStatus: false,
            };
        },
        mounted() {

        },
        methods: {
            submitReg: _.debounce(async function() {
                try {
                    if(!this.form.mobile) {
                        this.$message.error('請輸入手機號碼！');
                        return;
                    }
                    if(!this.form.captcha) {
                        this.$message.error('請輸入驗證碼！');
                        return;
                    }
                    if(!this.form.checked) {
                        this.$message.error('請同意會員條款和個人資料安全保障策略！');
                        return;
                    }
                    
                    const data = {
                        mobile: this.form.mobile.trim(),
                        code: this.form.captcha.trim(),
                        lineid: line
                    }
                    console.log('參數信息', data)
                    let res = await axios.post('/index/login/register', data,{
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    console.log(res, 'res');
                    if (res.data.code === 1) {
                        this.$message.success(res.data.msg);
                        setTimeout(() => {
                            window.location.href = res.data.url; // 注册成功后跳转到登录页面
                        }, 1000);
                    } else {
                        this.$message.error(res.data.msg || '注册失败，请稍后再试！');
                    }
                } catch (err) {
                    this.$message.error('注册失败，请稍后再试！');
                }
            }, 800),
            onMobileInput(e) {
                // 只允许输入数字，且最大长度为10
                let val = this.form.mobile.replace(/[^\d]/g, '').slice(0, 10);
                this.form.mobile = val;
            },
            sendVerificationCode: _.debounce(function() {
                if (!this.form.mobile) {
                    this.$message.error('請輸入手機號碼！');
                    return;
                }

                if (!/^09\d{8}$/.test(this.form.mobile)) {
                    this.$message.error('手機號碼格式不正確');
                    return;
                }
                
                try {
                    // 发送验证码逻辑
                    axios.post('/index/login/getCode', {
                        mobile: this.form.mobile,
                        type: 0 // 注册类型
                    }, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }).then(res => {
                        if (res.data.code === 1) {
                            this.$message.success('驗證碼已發送！');
                            this.startCountdown();
                        }else {
                            this.$message.error(res.data.msg || '驗證碼發送失敗，請稍後再試！');
                        }
                    }).catch(err => {
                        this.$message.error('驗證碼發送失敗，請稍後再試！');
                    });

                } catch (err) {
                    this.$message.error('驗證碼發送失敗，請稍後再試！');
                }
            }, 800),
            startCountdown() {
                this.isCaptchaDisabled = true;
                this.captchaText = `重發驗證碼(${this.countdown}s)`;
                
                const timer = setInterval(() => {
                    this.countdown--;
                    this.captchaText = `重發驗證碼(${this.countdown}s)`;
                    
                    if (this.countdown <= 0) {
                        clearInterval(timer);
                        this.isCaptchaDisabled = false;
                        this.captchaText = '發送驗證碼';
                        this.countdown = 180; // 重置为180秒
                    }
                }, 1000);
            },
            submitLineLogin: _.debounce(function () {
                axios.get('/index/third_party/line_user_login', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                }
                ).then(res => {
                    window.location.href = res.data.url || '';
                }).catch(err => {
                    this.$message.error('line登錄失敗，請稍後再試！');
                })
            }, 800),
        }
    });
</script>