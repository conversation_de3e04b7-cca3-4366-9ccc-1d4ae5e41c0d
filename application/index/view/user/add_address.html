
{include file="common/resources" /}
<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box">
    <div class="left_d" data-parent-nav="transport/recipient">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <!-- 页面标题和返回按钮 -->
        <div class="page-header">
            <a href="javascript:history.back();" class="back-btn"><i class="el-icon-arrow-left"></i> 返回</a>
            <h1 class="page-title">{{isEdit ? '修改收件人' : '新增收件人'}}</h1>
        </div>
        
        <!-- 表单内容 -->
        <div class="form-container">
            <el-form :model="formData" ref="addressForm" :rules="rules" label-position="left" :label-width="labelWidth" class="address-form">
                <div class="form-row">
                    <el-form-item label="所在區域" prop="region" class="form-item-left required-field">
                        <el-cascader
                            v-model="formData.region"
                            :options="areaList"
                            placeholder="請選擇"
                            class="full-width"
                            >
                        </el-cascader>
                    </el-form-item>
                    
                    <el-form-item label="詳細地址" prop="address" class="form-item-right required-field">
                        <el-input v-model="formData.address" placeholder="請輸入詳細地址 如**街**號"></el-input>
                    </el-form-item>
                </div>
                
                <div class="form-row">
                    <el-form-item label="姓名" prop="name" class="form-item-left required-field">
                        <el-input v-model="formData.name" placeholder="請輸入收件人姓名"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="手機號碼" prop="mobile" class="form-item-right required-field">
                        <el-input v-model="formData.mobile" placeholder="請輸入手機號碼"></el-input>
                    </el-form-item>
                </div>
                
                <div class="form-row">
                    <el-form-item label="身份證號" prop="idCard" class="form-item-left required-field">
                        <el-input v-model="formData.idCard" placeholder="請輸入收件人身份證號"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="郵政編碼" prop="postalCode" class="form-item-right required-field">
                        <el-input v-model="formData.postalCode" placeholder="請輸入郵政編碼"></el-input>
                    </el-form-item>
                </div>
                
                <div class="form-row">
                    <el-form-item label="固定號碼" class="form-item-left">
                        <el-input v-model="formData.fixedPhone" placeholder="請輸入"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="設定標籤" class="form-item-right label-options">
                        <el-radio-group v-model="formData.label">
                            <el-radio
                            v-for="item in identityList" :key="item.label" :label="item.label">{{ item.name }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
                
                <div class="form-footer">
                    <div class="switch-wrapper form-item-left">
                        <el-switch
                            v-model="formData.isDef"
                            active-color="#FF4081"
                            inactive-color="#DCDFE6">
                        </el-switch>
                        <span class="switch-label">預設寄送地址</span>
                    </div>
                    
                    <div class="form-buttons form-item-right" style="margin-right: 260px;">
                        <el-button @click="cancel">取消</el-button>
                        <el-button type="primary" @click="submitForm('addressForm')" :loading="submitting">提 交</el-button>
                    </div>
                </div>
            </el-form>
        </div>
    </div> 
</div>

<script>
    let row = JSON.parse('<?=json_encode($row, JSON_UNESCAPED_UNICODE)?>');
    console.log(row, 'row');
    
    
    var app = new Vue({
        el: '.transport-box',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            formData: {
                region: '',
                address: '',
                name: '',
                mobile: '',
                idCard: '',
                postalCode: '',
                fixedPhone: '',
                label: '',  // 默认选择同事
                labelId: '',    // 标签id
                isDef: false
            },
            labelWidth: '90px',
            areaList: [],
            identityList: [],   // 设定标签
            rules: {
                region: [{ required: true, message: '請選擇區域', trigger: 'change' }],
                address: [{ required: true, message: '請輸入詳細地址', trigger: 'blur' }],
                name: [{ required: true, message: '請輸入收件人姓名', trigger: 'blur' }],
                mobile: [
                    { required: true, message: '請輸入手機號碼', trigger: 'blur' },
                    { pattern: /^09\d{8}$/, message: '手機號碼必須為09開頭的10位數字', trigger: 'blur' }
                ],
                idCard: [
                    { required: true, message: '請輸入收件人身份證號', trigger: 'blur' },
                    { pattern: /^[A-Z][0-9]{9}$/, message: '身份證號必須為1位大寫字母+9位數字', trigger: 'blur' }
                ],
                postalCode: [
                    { required: true, message: '請輸入郵政編碼', trigger: 'blur' },
                    { pattern: /^\d{5,6}$/, message: '郵遞區號只能是5位或6位數字', trigger: 'blur' }
                ],
            },
            submitting: false,
            isEdit: false,      // 是否为编辑模式
            addressId: null,    // 当前编辑的地址ID
        },
        mounted() {
            this.getRegionList();
            // 自动填充row数据（编辑模式）
            if (row && row.id) {
                this.isEdit = true;
                this.addressId = row.id;
                this.formData = {
                    region: [row.city, row.district],
                    address: row.detail,
                    name: row.name,
                    mobile: row.mobile,
                    idCard: row.card,
                    postalCode: row.postal,
                    fixedPhone: row.number || '',
                    label: '', // 下面会处理
                    labelId: row.label,
                    isDef: row.is_def == 1
                };
                // 标签需要在identityList加载后匹配
                this.$nextTick(() => {
                    if (this.identityList.length > 0) {
                        const labelItem = this.identityList.find(item => item.id == row.label);
                        if (labelItem) {
                            this.formData.label = labelItem.label;
                        }
                    }
                });
            }
        },
        methods: {
            getRegionList() {
                axios.get('/index/user/addAddress').then(res=> {
                    if(res.data.code === '0') {
                        this.areaList = (res.data?.area_list || []).map(city => ({
                            value: city.id,
                            label: city.name,
                            children: city.list.map(district => ({
                                value: district.id,
                                label: district.name
                            }))
                        }));
                        this.identityList = (res.data?.label_list || []).map((item, index) => ({
                            label: index + 1,  // 保持原始id用于提交
                            name: item.name,
                            id: item.id
                        }));
                        // 编辑模式下，identityList加载后再匹配label
                        if (this.isEdit && this.formData.labelId && this.identityList.length > 0) {
                            const labelItem = this.identityList.find(item => item.id == this.formData.labelId);
                            if (labelItem) {
                                this.formData.label = labelItem.label;
                            }
                        } else if (this.identityList.length > 0 && !this.isEdit) {
                            this.formData.label = 2;  // 默认选择同事
                        }
                    } else {
                        this.$message.error(res.data.msg);
                    }
                }).catch(err => {
                    this.$message.error('網絡錯誤');
                });
            },
            submitForm: _.debounce(function(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.submitting = true;
                        if (!Array.isArray(this.formData.region) || this.formData.region.length < 2) {
                            this.$message.error('請選擇完整的地區信息');
                            this.submitting = false;
                            return;
                        }
                        const selectedLabel = this.identityList.find(item => item.label === this.formData.label);
                        const identityLabel = selectedLabel ? selectedLabel.id : null;
                        let params = {
                            type: 0,
                            isdef: this.formData.isDef ? 1 : 0,
                            city: this.formData.region[0],
                            dist: this.formData.region[1],
                            detail: this.formData.address,
                            name: this.formData.name,
                            mobile: this.formData.mobile,
                            card: this.formData.idCard,
                            number: this.formData.fixedPhone,
                            postal: this.formData.postalCode,
                            label: identityLabel
                        };

                        console.log(params,'params');
                        
                        // 编辑时带上id
                        if (this.isEdit && this.addressId) {
                            params.id = this.addressId;
                        }
                        axios.post('/index/user/addAddresses', params).then(response => {
                            this.submitting = false;
                            if (Number(response.data.code) === 0) {
                                this.$message({
                                    message: response.data.msg,
                                    type: 'success'
                                });
                                setTimeout(() => {
                                    // 在跳转前存储需要高亮的导航项
                                    sessionStorage.setItem('activeNav', 'transport/recipient');
                                    window.location.href = '/index/transport/recipient';
                                }, 600);
                            } else {
                                this.$message.error(response.data.msg || '操作失敗');
                            }
                        }).catch(error => {
                            this.submitting = false;
                            this.$message.error('系統錯誤，請稍後再試');
                            console.error(error);
                        });
                    } else {
                        return false;
                    }
                });
            }, 600),
            cancel() {
                history.back();
            },
        }
    });
</script>

<style>
    .page-header {
        position: relative;
        text-align: center;
        padding: 20px 0;
        margin-bottom: 20px;
    }
    
    .back-btn {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        text-decoration: none;
        font-size: 14px;
    }
    
    .page-title {
        font-size: 18px;
        color: #FF4081;
        margin: 0 auto;
        font-weight: normal;
        display: inline-block;
    }
    
    .form-container {
        padding: 0 15px;
    }
    
    .address-form {
        background: #fff;
        padding: 15px;
    }
    
    .form-row {
        display: flex;
        margin-bottom: 15px;
    }
    
    .form-item-left {
        flex: 1;
        margin-right: 10px;
    }
    
    .form-item-right {
        flex: 1;
        margin-right: 10px;
    }
    
    .full-width {
        width: 100%;
    }
    
    .required-mark {
        color: #FF4081;
        margin-right: 1px;
        position: relative;
        top: 2px;
    }
    
    .el-form-item {
        margin-bottom: 20px;
    }
    
    .el-form-item__label {
        color: #333 !important;
        font-size: 14px !important;
        line-height: 36px !important;
        padding-right: 5px !important;
    }

    /* 使用Element UI的必填样式显示红色星号 */
    .required-field .el-form-item__label:before {
        content: "* " !important;
        color: #FF4081 !important;
        margin-right: 2px;
    }

    .el-form-item__label:before {
        content: "" !important;
    }
    
    .el-form-item__content {
        line-height: 36px !important;
    }
    
    .el-input, .el-select, .el-cascader {
        width: 100% !important;
        max-width: 420px;
    }

    .el-input__inner, .el-select .el-input__inner {
        height: 36px;
        line-height: 36px;
        width: 100%;
    }
    
    .label-options .el-radio-group {
        display: flex;
        flex-wrap: wrap;
        padding-top: 7px;
    }
    
    .label-options .el-radio {
        margin-right: 15px;
        margin-bottom: 10px;
        font-size: 14px;
    }
    
    /* 调整底部布局 */
    .form-footer {
        display: flex;
        flex-direction: row;
        margin-top: 30px;
        width: 100%;
    }
    
    .switch-wrapper {
        display: flex;
        align-items: center;
        width: 50%;
        padding-right: 10px;
        box-sizing: border-box;
    }
    
    .switch-label {
        margin-left: 10px;
        font-size: 14px;
        color: #333;
    }
    
    .form-buttons {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        width: 50%;
        padding-left: 10px;
        box-sizing: border-box;
    }
    
    .form-buttons .el-button {
        min-width: 100px;
        height: 40px;
        padding: 0 20px;
    }
    
    .el-button--primary {
        background-color: #FF4081;
        border-color: #FF4081;
    }
    
    .el-button--primary:hover,
    .el-button--primary:focus {
        background-color: #f33677;
        border-color: #f33677;
    }

    /* 添加响应式布局 */
    @media screen and (max-width: 1200px) {
        .form-row {
            flex-direction: column;
        }
        
        .form-item-left, .form-item-right {
            flex: 1;
            width: 100%;
            margin-right: 0;
            margin-left: 0;
        }
        
        .el-input, .el-select, .el-cascader {
            max-width: 100%;
        }
        
        .form-footer {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .switch-wrapper {
            margin-bottom: 20px;
            width: 100%;
            padding-right: 0;
        }
        
        .form-buttons {
            width: 100%;
            padding-left: 0;
            justify-content: space-between;
        }
        
        .form-buttons .el-button {
            flex: 1;
        }
    }

    @media screen and (max-width: 768px) {
        .page-header {
            padding: 15px 0;
        }
        
        .el-form-item__label {
            font-size: 12px !important;
        }
        
        .form-container {
            padding: 0 10px;
        }
        
        .address-form {
            padding: 10px;
        }
    }
</style>