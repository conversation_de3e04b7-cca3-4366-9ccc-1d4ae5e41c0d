{include file="common/resources" /}

<div class="transport-box" id="app" style="margin: 0; padding: 0; border: none;">
    {include file="common/bottom_nav" /}
    <div class="right-container" style="margin: 0; padding: 0;">
        <div class="nav">
            <img src="__CDN__/assets/img/pc/nav_logo.png" alt="" style="height: 50%; cursor: pointer;" onclick="window.location.href='{:url('/index/login/login')}'">
            <div class="nav-right" onclick="window.location.href='/index/notice/index'">
                <div class="login-message">
                    <img class="pointer" src="__CDN__/assets/img/pc/new_index/parcel19.svg" alt="通知公告">
                    {if $notice_num}
                    <span class="login-message-num">{$notice_num}</span>
                    {/if}
                    <a href="#" class="terms-link pointer">通知公告</a>
                </div>
            </div>
        </div>
        <div class="tool-bg">
            <img src="__CDN__/assets/img/pc/login/tool_bg.png" alt=""
                style="width: 100%; height: 100%; filter: brightness(50%); position: relative;">
            <span>能寄嗎？</span>
        </div>
        <div class="send-content">
            <div class="search-container">
                <div class="search-input-container">
                    <input type="text" class="send-search-input" v-model="KeyWords" placeholder="輸入商品關鍵詞或黏貼商品標題"
                        @keyup.enter="searchProducts">
                    <button class="search-btn" @click="searchProducts">
                        <i class="el-icon-search fs16" style="color: #EF436D;"></i>
                    </button>
                </div>
                <div class="search-result" v-if="showResults" ref="searchResults">
                    <div v-if="products.length && products.length > 0" class="search-result-title">
                        選擇一個最相似的商品:
                    </div>
                    <div class="search-result-container custom-scrollbar" style="padding-right: 4px;" ref="scrollContainer">
                        <div class="product-item" v-for="(product, index) in products" :key="index"
                            @click="selectProduct(product)" :class="{ 'active': selectedProduct === product }">
                            <div class="product-image">
                                <img :src="getImageProxyUrl(product.imageUrl)|| product.image" alt="商品图片">
                            </div>
                            <span class="product-title">{{ product.subject || product.title }}</span>
                        </div>
                        <div class="loading-more" v-if="isLoading"
                            style="width: 100%;display: flex; flex-direction: column; align-items: center; justify-content: center;">
                            <div><i class="el-icon-loading ml8 fs24 c999"></i></div>
                            <div style="margin-left: 10px; color: #999999;">加載中</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-detail" v-if="selectedProduct">
                    <div v-if="goodsParams &&goodsParams.chineseName" class="product-detail-title">
                        【{{ goodsParams.chineseName}}】
                    </div>
                    <div class="divider"></div>
                    <div v-if="goodsParams.judge" class="product-question">
                        請問{{goodsParams.judge}}?
                    </div>
                    <div v-if="goodsParams.judge" class="question-action">
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="battery" :value="true" v-model="selectedAnswer">
                                <span class="radio-label">是</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="battery" :value="false" checked v-model="selectedAnswer">
                                <span class="radio-label">否</span>
                            </label>
                        </div>
                        <button class="submit-btn" @click="submitAnswer">提交</button>
                    </div>

                    <div v-if="goodsParams.judge" class="divider"></div>

                    <div class="shipping-options">
                        <div class="shipping-title">運輸方式</div>
                        <div class="shipping-method">
                            <div class="shipping-method-item">
                                <div class="shipping-left">
                                    <div class="shipping-icon">
                                        <img src="__CDN__/assets/img/pc/transport01.svg" alt="">
                                    </div>
                                    <div class="shipping-name">海快</div>
                                </div>
                                <div class="shipping-right">
                                    <span>
                                        <i :class="!transportStatus.seaExpress ? 'el-icon-circle-check' : 'el-icon-circle-close'"
                                            :style="{color: !transportStatus.seaExpress ? '#2BA471' : '#EF436D'}"></i>
                                    </span>
                                    <span class="shipping-status"
                                        :style="{color: !transportStatus.seaExpress ? '#2BA471' : '#EF436D'}">
                                        {{!transportStatus.seaExpress ? '允許' : '禁運' }}
                                    </span>
                                </div>
                            </div>
                            <div class="shipping-method-item">
                                <div class="shipping-left">
                                    <div class="shipping-icon">
                                        <img src="__CDN__/assets/img/pc/transport03.svg" alt="">
                                    </div>
                                    <div class="shipping-name">空運</div>
                                </div>
                                <div class="shipping-right">
                                    <span>
                                        <i :class="!transportStatus.airFreight ? 'el-icon-circle-check' : 'el-icon-circle-close'"
                                            :style="{color: !transportStatus.airFreight ? '#2BA471' : '#EF436D'}"></i>
                                    </span>
                                    <span class="shipping-status"
                                        :style="{color: !transportStatus.airFreight ? '#2BA471' : '#EF436D'}">
                                        {{!transportStatus.airFreight ? '允許' : '禁運' }}
                                    </span>
                                </div>
                            </div>
                            <div class="shipping-method-item">
                                <div class="shipping-left">
                                    <div class="shipping-icon">
                                        <img src="__CDN__/assets/img/pc/transport02.svg" alt="">
                                    </div>
                                    <div class="shipping-name">海運</div>
                                </div>
                                <div class="shipping-right">
                                    <span>
                                        <i :class="!transportStatus.seaFreight ? 'el-icon-circle-check' : 'el-icon-circle-close'"
                                            :style="{color: !transportStatus.seaFreight ? '#2BA471' : '#EF436D'}"></i>
                                    </span>
                                    <span class="shipping-status"
                                        :style="{color: !transportStatus.seaFreight ? '#2BA471' : '#EF436D'}">
                                        {{!transportStatus.seaFreight ? '允許' : '禁運' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="tax-info" v-if="false">
                            <div class="tax-title">全包稅限制</div>
                            <div class="tax-content orange pl12 fs13">此商品不包稅</div>
                            <div class="tax-content orange pl12 fs13">此類型商品單筆集運5公斤以上不列入全包稅範圍</div>
                        </div>

                        <div class="import-info">
                            <div class="import-title">進口限制</div>
                            <div class="import-content pl12">該商品無進口限制</div>
                            <div class="import-alert pl12">若含有WIFI、藍牙、遙控器商品屬台灣NCC管制品，建議個人用途單次航班理論商品數量不要超過2個</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{include file="common/footer" /}
<script>
    const app = new Vue({
        el: '#app',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            KeyWords: '',
            secondId: null,     // 二级类目ID
            thirdId: null,      // 三级类目ID
            showResults: false,
            selectedProduct: null,
            products: [],       // 商品列表
            goodsParams: {},    // 商品参数
            submitted: false,   // 是否已点击提交
            currentPage: 1,
            pageSize: 10,
            isLoading: false,
            hasMoreData: true,
            selectedAnswer: false, // 默认选择否 (false=否, true=是)
            transportStatus: {
                seaExpress: false, // 海快状态，false=允许，true=禁运
                airFreight: false, // 空运状态，false=允许，true=禁运 
                seaFreight: false  // 海运状态，false=允许，true=禁运
            },
            initialTransportStatus: {
                seaExpress: false, // 海快初始状态
                airFreight: false, // 空运初始状态
                seaFreight: false  // 海运初始状态
            },
            transportIcon: [
                '__CDN__/assets/img/pc/transport01.svg',
                '__CDN__/assets/img/pc/transport02.svg',
                '__CDN__/assets/img/pc/transport03.svg', 
            ],
        },
        computed: {

        },
        methods: {
            async searchProducts() {
                if (this.KeyWords.trim()) {
                    try {
                        // 重置分页和产品列表
                        this.currentPage = 1;
                        this.hasMoreData = true;
                        this.selectedProduct = null; // 清除已选中的产品，隐藏产品详情
                        this.showResults = true;
                        this.products = []; // 清空产品列表，避免显示旧数据
                        this.isLoading = true; // 设置加载状态
                        
                        let params = {
                            kw: this.KeyWords,
                            p:this.currentPage,
                            s:this.pageSize
                        }
                        // 获取第一页数据
                        let res = await axios.post(`keywordQuery`,params);
                        console.log(res,'res');
                        if (res.data && Number(res.data.code) === 0) {
                            this.products = res.data.data || [];
                            // 如果返回的数据少于请求的每页数量，表示没有更多数据了
                            this.hasMoreData = this.products.length >= this.pageSize;
                        } else {
                            this.$message.error(res.data?.msg || '获取商品数据失败');
                            this.products = [];
                            this.hasMoreData = false;
                        }
                    } catch (error) {
                        console.error('搜索商品出错:', error);
                        this.$message.error('搜索商品出错');
                        this.products = [];
                        this.hasMoreData = false;
                    } finally {
                        this.isLoading = false;
                        this.$nextTick(() => {
                            this.addScrollListener();
                        });
                    }
                }
            },
            async loadMoreProducts() {
                if (!this.hasMoreData || this.isLoading) return;
                
                try {
                    this.isLoading = true;
                    this.currentPage++;
                    
                    let params = {
                        kw: this.KeyWords,
                        p: this.currentPage,
                        s: this.pageSize
                    }
                    
                    let res = await axios.post(`keywordQuery`, params);
                    if (res.data && Number(res.data.code) === 0 && res.data.data && res.data.data.length) {
                        // 将新数据添加到现有列表末尾
                        this.products = [...this.products, ...res.data.data];
                        // 检查是否还有更多数据
                        this.hasMoreData = res.data.data.length >= this.pageSize;
                    } else {
                        this.hasMoreData = false;
                    }
                } catch (error) {
                    console.error('加载更多商品出错:', error);
                    this.$message.error('加载更多商品出错');
                } finally {
                    this.isLoading = false;
                }
            },
            selectProduct(product) {
                this.selectedProduct = product;
                this.secondId = product.secondCategoryId;
                this.thirdId = product.thirdCategoryId;
                this.showResults = false; // 隐藏搜索结果
                this.removeScrollListener(); // 移除滚动监听
                this.submitted = false; // 重置提交状态
                this.selectedAnswer = false; // 重置为默认选择"否"

                const categoryId = this.thirdId || this.secondId;

                if(categoryId) {
                    axios.post('embargo', {id: categoryId}).then(res => {
                        if(res.data && Number(res.data.code) === 0) {
                            console.log('商品参数', res.data.data);
                            this.goodsParams = res.data.data;
                            
                            // 根据返回结果设置运输方式的初始状态
                            // 海快状态
                            this.initialTransportStatus.seaExpress = this.goodsParams.hk === '1';
                            // 空运状态
                            this.initialTransportStatus.airFreight = this.goodsParams.ky === '1,2';
                            // 海运状态
                            this.initialTransportStatus.seaFreight = this.goodsParams.hy === '1';
                            
                            // 复制初始状态到当前状态
                            this.transportStatus = {...this.initialTransportStatus};
                            
                            console.log('运输方式初始状态：', this.initialTransportStatus);
                        } else {
                            this.$message.error(res.data?.msg || '获取商品参数失败');
                        }  
                    }).catch(err => {
                        console.error('获取商品参数出错:', err);
                        this.$message.error('获取商品参数出错');
                    });
                }
            },
            submitAnswer() {
                // 根据选择更新提交状态
                if (this.selectedAnswer === true) {
                    // 如果选择"是"，表示商品有问题，显示为禁运
                    this.submitted = true;
                    
                    // 所有运输方式都设为禁运
                    this.transportStatus = {
                        seaExpress: true,
                        airFreight: true,
                        seaFreight: true
                    };
                    
                    this.$message({
                        message: '根据商品情况，该商品不允许运输',
                        type: 'warning'
                    });
                } else {
                    // 如果选择"否"，恢复到初始状态
                    this.submitted = false;
                    
                    // 恢复到API返回的初始状态
                    this.transportStatus = {...this.initialTransportStatus};
                    
                    this.$message({
                        message: '根据商品参数显示运输限制',
                        type: 'success'
                    });
                }
                
                console.log('选择答案:', this.selectedAnswer ? '是' : '否');
                console.log('提交状态:', this.submitted);
                console.log('运输方式状态:', this.transportStatus);
            },
            getCategory(product) {
                // 从商品数据中提取分类信息
                if (product.thirdCategoryId) {
                    return '商品分類';
                }
                return '未知分類';
            },
            checkScroll() {
                const container = this.$refs.scrollContainer;
                if (!container) return;
                
                const scrollTop = container.scrollTop;
                const scrollHeight = container.scrollHeight;
                const clientHeight = container.clientHeight;
                
                // 当滚动到底部时加载更多数据
                if (scrollHeight - scrollTop - clientHeight <= 50 && this.hasMoreData && !this.isLoading) {
                    this.loadMoreProducts();
                }
            },
            addScrollListener() {
                if (this.$refs.scrollContainer) {
                    this.$refs.scrollContainer.addEventListener('scroll', this.checkScroll);
                }
            },
            removeScrollListener() {
                if (this.$refs.scrollContainer) {
                    this.$refs.scrollContainer.removeEventListener('scroll', this.checkScroll);
                }
            },
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '';
                // 使用图片代理
                return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
            },
        },
        mounted() {
            // 初次挂载时不需要添加监听，只有在搜索结果显示时才添加
        },
        updated() {
            // 当DOM更新时检查是否需要添加滚动监听
            if (this.showResults && this.$refs.scrollContainer) {
                this.addScrollListener();
            }
        },
        beforeDestroy() {
            // 组件销毁前移除监听器
            this.removeScrollListener();
        }
    })
</script>

