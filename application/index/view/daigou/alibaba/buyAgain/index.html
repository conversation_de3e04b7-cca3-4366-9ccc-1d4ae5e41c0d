{include file="common/resources" /}
{include file="common/daigouresourses" /}

<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="buyAgain">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="max-w-screen-2xl mx-auto bg-white">
            <!-- 顶部标题 -->
            <div class="font-bold text-xl px-8 pt-8 pb-4">再次購買</div>
            <!-- 商品表格 -->
            <div class="bg-white mx-8 rounded border">
                <!-- 表头 -->
                <div class="flex items-center bg-pink-50 text-gray-700 font-bold text-sm px-6 py-3 rounded-t">
                    <el-switch v-model="showImg" class="mr-4" active-text="展示貨品圖" inactive-text=""></el-switch>
                    <div class="w-1/4">货品信息</div>
                    <div class="w-1/4">规格</div>
                    <div class="w-1/12 text-center">上次購買價格 （元）</div>
                    <div class="w-1/12 text-center">當前價格 (元)</div>
                    <div class="w-1/12 text-center">采購數</div>
                    <div class="w-1/12 text-center">小計 （人民幣）</div>
                </div>
                <!-- 公司分组 -->
                <div class="px-6 py-2 bg-gray-50 border-b text-pink-700 font-bold flex items-center">
                    {{ sellerInfo }}
                    <el-tooltip content="公司信息" placement="top">
                        <i class="el-icon-info ml-2"></i>
                    </el-tooltip>
                </div>
                <!-- 商品行 -->
                <div v-for="(item, idx) in productItems" :key="item.subItemID" class="flex items-center border-b px-6 py-4">
                    <div style="margin-left:40px;"></div>
    
                    <div class="w-1/4 flex items-center">
                        <img v-if="showImg" :src="getImageProxyUrl(item.productImgUrl[1] || item.productImgUrl[0])" class="w-14 h-14 object-cover rounded border mr-3" />
                        <span class="text-pink-600 font-bold truncate">{{item.name}}</span>
                    </div>
                    <div style="margin-left:90px;"></div>
    
                    <div class="w-1/4 text-gray-600 text-sm">{{item.cargoNumber}}</div>
                    <div class="w-1/12 text-center">{{item.price}}</div>
                    <div class="w-1/12 text-center">{{item.price}}</div>
                    <div class="w-1/12 flex justify-center items-center">
                        <el-button size="mini" @click="changeQty(idx, -1)" :disabled="item.qty<=0">-</el-button>
                        <span class="mx-2 w-6 text-center">{{item.qty}}</span>
                        <el-button size="mini" @click="changeQty(idx, 1)">+</el-button>
                    </div>
                    <div class="w-1/12 text-right text-pink-600 font-bold">{{(item.qty*item.price).toFixed(2)}}</div>
                </div>
                <!-- 展开未购买过的价格 -->
                <!-- <div class="text-center text-pink-500 py-4 cursor-pointer select-none" @click="showMore = !showMore">
                    <span>展开未购买过的规格</span>
                    <i :class="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                </div> -->
            </div>
            <!-- 底部结算栏 -->
            <div class="bg-pink-50 border-t p-6 flex justify-around items-center z-50 mb-2" style="position: absolute;bottom: 0;width: 90%;left:5%">
                <div class="max-w-screen-xl mx-auto flex items-center justify-between px-8">
                    <div class="text-gray-600 text-sm flex items-center space-x-6">
                        <span style="margin-left: 50px;">结算明细</span>
                        <span style="margin-left: 50px;">已选货品：<span class="text-pink-600 font-bold">{{selectedKinds}}</span> 种</span>
                        <span style="margin-left: 50px;">数量：<span class="text-pink-600 font-bold">{{selectedQty}}</span> 件</span>
                        <span style="margin-left: 50px;">货品金额总计（不包含运费）：<span class="text-pink-600 font-bold text-lg">￥{{totalAmount.toFixed(2)}}</span></span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <el-button style="margin-left: 50px;" size="large" @click="addToCart">加采购单</el-button>
                        <el-button style="margin-left: 50px;" type="danger" size="large" @click="submitOrder">再次購買</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const app = new Vue({
        el: '#buyAgain',
        mixins: [bottomNavMixin],
        data: {
            list: <?php echo json_encode($list); ?>, 
            productInfo: <?php echo json_encode($data); ?>, 
            showImg: true,
            showMore: false,
            showDialog: false,
            orderData: '',
            productItems: [],
            sellerInfo: ''
        },
        mounted(){
            this.productInfo = this.productInfo.result.result
            console.log(this.list,this.productInfo,78)
            this.initData();
        },
        computed: {
            selectedKinds() {
                return this.productItems.filter(i=>i.qty>0).length;
            },
            selectedQty() {
                return this.productItems.reduce((sum,i)=>sum+Number(i.qty),0);
            },
            totalAmount() {
                return this.productItems.reduce((sum,i)=>sum+i.qty*i.price,0);
            }
        },
        methods: {
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '/assets/img/daigou/product.png';
                if (originalUrl.includes('alicdn.com') || originalUrl.includes('alibaba.com')) {
                    return 'imageProxy?url=' + encodeURIComponent(originalUrl);
                }
                return originalUrl;
            },
            initData() {
                if (this.list && this.list.productItems) {
                    try {
                        const productItemsData = JSON.parse(this.list.productItems);
                        this.productItems = productItemsData.data.map(function(item) {
                            return {
                                cargoNumber: item.cargoNumber,
                                itemAmount: item.itemAmount,
                                name: item.name,
                                price: item.price,
                                productImgUrl: item.productImgUrl,
                                quantity: item.quantity,
                                subItemID: item.subItemID,
                                productCargoNumber: item.productCargoNumber,
                                specId: item.specId,
                                gmtCreate: item.gmtCreate,
                                gmtPayExpireTime: item.gmtPayExpireTime,
                                qty: item.quantity || 0
                            };
                        });
                    } catch (e) {
                        console.error('解析商品数据失败:', e);
                        this.productItems = [];
                    }
                }
                
                if (this.list && this.list.sellerLoginId) {
                    this.sellerInfo = this.list.sellerLoginId;
                }
            },
            changeQty(idx, delta) {
                const item = this.productItems[idx];
                if(item.qty+delta>=0) item.qty+=delta;
            },
            async addToCart() {
                const listdata = this.productItems.filter(function(i) { return i.qty>0; });
                if (listdata.length === 0) {
                    this.$message && this.$message.warning('请先选择商品数量');
                    return;
                }
                let selected = {};
                selected = {
                    totalNum: this.selectedQty,
                    totalPrice: this.totalAmount,
                    shop: this.productInfo.sellerNick || (this.productInfo.seller && this.productInfo.seller.wangwangNick) || '',
                    subject: this.productInfo.subject,
                    sellerOpenId: this.productInfo.sellerOpenId,
                    offer: this.productInfo.offerId,
                    img: this.productInfo.productImage && this.productInfo.productImage.images ? this.productInfo.productImage.images[0] : '',
                    spec: listdata.map(item => {
                        // 通过specId从productSkuInfos中获取对应的skuAttributes
                        const matchingSku = this.productInfo.productSkuInfos.find(sku => sku.specId === item.specId);
                        return {
                            specId: item.specId,
                            cargoNumber: item.cargoNumber,
                            skuAttributes: matchingSku ? matchingSku.skuAttributes : [],
                            price: item.price,
                            num: item.qty,
                            skuImageUrl: item.productImgUrl && item.productImgUrl.length > 0 ? item.productImgUrl[0] : ''
                        };
                    })
                };

                // 提交到后端
                console.log('结算参数', selected);
                let res = await axios.post(`aliAddCart`, selected);
                if(res.data.code == '0'){
                    this.$message.success('添加成功');
                }else{
                    this.$message.error(res.data.msg || '添加失败');
                }
            },
            async submitOrder() {
                const listdata = this.productItems.filter(function(i) { return i.qty>0; });
                if (listdata.length === 0) {
                    this.$message && this.$message.warning('请先选择商品数量');
                    return;
                }
                let selected = [];
                selected.push({
                    shop: this.productInfo.sellerNick || (this.productInfo.seller && this.productInfo.seller.wangwangNick) || '',
                    subject: this.productInfo.subject,
                    id:0,
                    sellerId: this.productInfo.sellerOpenId,
                    offer: this.productInfo.offerId,
                    images: this.productInfo.productImage && this.productInfo.productImage.images ? this.productInfo.productImage.images[0] : '',
                    spec: listdata.map(item => {
                        // 通过specId从productSkuInfos中获取对应的skuAttributes
                        const matchingSku = this.productInfo.productSkuInfos.find(sku => sku.specId === item.specId);
                        return {
                            specId: item.specId,
                            cargoNumber: item.cargoNumber,
                            skuAttributes: matchingSku ? matchingSku.skuAttributes : [],
                            price: item.price,
                            num: item.qty,
                            skuImageUrl: item.productImgUrl && item.productImgUrl.length > 0 ? item.productImgUrl[0] : ''
                        };
                    })
                });
                // 提交到后端
                console.log('结算参数', selected);
                let res = await axios.post('aliSubmitOrder', {data:selected});
                this.orderData = JSON.stringify(selected, null, 2);

               // 创建表单并提交数据
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'aliSubmitOrder';

                // 创建隐藏的input字段来传递数据
                const dataInput = document.createElement('input');
                dataInput.type = 'hidden';
                dataInput.name = 'data';
                dataInput.value = JSON.stringify(selected);
                form.appendChild(dataInput);

                // 将表单添加到页面并提交
                document.body.appendChild(form);
                form.submit();
            }
        }
    });
</script>