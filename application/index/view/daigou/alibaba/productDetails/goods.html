<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../css/element-ui-index.css">
    <link href="../css/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/common.css">
    <script src="../js/vue.js"></script>
    <!-- 引入组件库 -->
    <script src="../js/element-ui-index.js"></script>

</head>

<body>
    <!-- 流程步骤条 -->
    <div class="max-w-screen-2xl mx-auto mt-6 flex items-center px-2">

        <div class="text-xl font-bold mr-4">1688代採購</div>
    </div>

    <div class="max-w-screen-2xl mx-auto mt-6 flex items-center px-2">

        <div class="flex items-center flex-1">
            <span class="text-pink-500 font-bold flex items-center"><svg class="w-5 h-5 mr-1" fill="none"
                    stroke="#ec4899" stroke-width="2" viewBox="0 0 24 24">
                    <path d="M9 12h6M12 9v6" stroke-linecap="round" stroke-linejoin="round" />
                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="#ec4899" />
                </svg>创建代购订单</span>
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
        </div>
        <div class="flex items-center flex-1 justify-center">
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
            <span class="text-gray-400 flex items-center"><svg class="w-5 h-5 mr-1" fill="none" stroke="#aaa"
                    stroke-width="2" viewBox="0 0 24 24">
                    <path d="M6 6h15M6 12h15M6 18h15M3 6h.01M3 12h.01M3 18h.01" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>付款到EZ集运通</span>
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
        </div>
        <div class="flex items-center flex-1 justify-end">
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
            <span class="text-gray-400 flex items-center"><svg class="w-5 h-5 mr-1" fill="none" stroke="#aaa"
                    stroke-width="2" viewBox="0 0 24 24">
                    <rect x="3" y="7" width="18" height="13" rx="2" stroke="#aaa" />
                    <path d="M16 3v4M8 3v4" stroke-linecap="round" />
                </svg>采购成功</span>
        </div>
    </div>
    <!-- 商品详情页 -->
    <div id="app" class="flex max-w-screen-2xl mx-auto mt-8 bg-white rounded-lg  p-8">
        <div class="w-full">
            <!-- 顶部商品主图和信息 -->
            <div class="flex">
                <!-- 左侧图片区 -->
                <div class="w-1/4 flex flex-col items-center">
                    <img src="../img/1688/product.png" alt="商品主图"
                        class="w-56 h-56 rounded-lg shadow mb-4 object-cover" />
                    <div class="bg-orange-400 text-white px-4 py-2 rounded-lg text-lg font-bold mb-2">这款小夜灯绝了</div>
                    <!-- <div class="bg-white px-3 py-1 rounded shadow text-gray-700 text-sm">夜起亮灯 不影响室友</div> -->
                </div>
                <!-- 右侧信息区 -->
                <div class="w-3/4 pl-10">
                    <!-- 标题 -->
                    <div class="text-xl font-bold mb-2 mt-2">创意小夜灯充电按按磁吸灯LED充电电池款卧室床头灯USB学生宿舍灯</div>
                    <!-- 选项卡 -->
                    <div class="flex items-center border-b mb-2">
                        <div class="px-6 py-2 text-pink-600 font-bold border-b-2 border-pink-500 cursor-pointer">批发
                        </div>
                        <div class="px-6 py-2 text-gray-400 cursor-pointer">代发</div>
                    </div>
                    <!-- 会员价提示 -->
                    <div class="text-xs text-pink-500 mb-2">已享受PLUS会员价</div>
                    <!-- 价格阶梯 -->
                    <div class="flex items-end space-x-8 mb-2">
                        <div class="text-pink-600 text-2xl font-bold">￥11.21 <span
                                class="text-base font-normal text-gray-500">≥1件</span></div>
                        <div class="text-pink-600 text-xl font-bold">￥6.86 <span
                                class="text-base font-normal text-gray-500">≥500件</span></div>
                        <div class="text-pink-600 text-lg font-bold">￥4.68 <span
                                class="text-base font-normal text-gray-500">≥1000件</span></div>
                    </div>
                    <!-- 参数 -->
                    <div class="mb-2 text-gray-700 text-sm">光源功率：2W</div>
                    <!-- 规格表格 -->
                    <div class="mt-4">
                        <div class="font-semibold mb-2">增选颜色：</div>
                        <table class="w-full text-sm border rounded overflow-hidden">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="py-2 px-2 text-left font-normal text-gray-500">图片</th>
                                    <th class="py-2 px-2 text-left font-normal text-gray-500">名称</th>
                                    <th class="py-2 px-2 text-center font-normal text-gray-500">价格</th>
                                    <th class="py-2 px-2 text-center font-normal text-gray-500">库存</th>
                                    <th class="py-2 px-2 text-center font-normal text-gray-500">数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b">
                                    <td class="py-2 px-2"><img src="../img/1688/product.png"
                                            class="w-10 h-10 object-cover rounded" /></td>
                                    <td class="py-2 px-2">充电款常光无极调光</td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">6.18</td>
                                    <td class="py-2 px-2 text-center text-gray-600">5201</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" disabled>-</button>
                                        <input type="text" value="0" class="w-10 h-7 border rounded text-center mx-1"
                                            readonly />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500">+</button>
                                    </td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-2 px-2"><img src="../img/1688/product.png"
                                            class="w-10 h-10 object-cover rounded" /></td>
                                    <td class="py-2 px-2">【特惠款】充电款白光两档亮度</td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">6.18</td>
                                    <td class="py-2 px-2 text-center text-gray-600">5201</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" disabled>-</button>
                                        <input type="text" value="0" class="w-10 h-7 border rounded text-center mx-1"
                                            readonly />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500">+</button>
                                    </td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-2 px-2"><img src="../img/1688/product.png"
                                            class="w-10 h-10 object-cover rounded" /></td>
                                    <td class="py-2 px-2">新款小号呼吸充电款常光无极调光</td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">6.18</td>
                                    <td class="py-2 px-2 text-center text-gray-600">5201</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" disabled>-</button>
                                        <input type="text" value="0" class="w-10 h-7 border rounded text-center mx-1"
                                            readonly />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500">+</button>
                                    </td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-2 px-2"><img src="../img/1688/product.png"
                                            class="w-10 h-10 object-cover rounded" /></td>
                                    <td class="py-2 px-2">新款小号呼吸【特惠款】充电款白光两档亮亮</td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">6.18</td>
                                    <td class="py-2 px-2 text-center text-gray-600">5201</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" disabled>-</button>
                                        <input type="text" value="0" class="w-10 h-7 border rounded text-center mx-1"
                                            readonly />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500">+</button>
                                    </td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-2 px-2"><img src="../img/1688/product.png"
                                            class="w-10 h-10 object-cover rounded" /></td>
                                    <td class="py-2 px-2">充电感应放白光（人来即亮人走即灭）</td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">6.18</td>
                                    <td class="py-2 px-2 text-center text-gray-600">5201</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" disabled>-</button>
                                        <input type="text" value="0" class="w-10 h-7 border rounded text-center mx-1"
                                            readonly />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500">+</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 px-2"><img src="../img/1688/product.png"
                                            class="w-10 h-10 object-cover rounded" /></td>
                                    <td class="py-2 px-2">新款大号呼吸1200毫安</td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">6.18</td>
                                    <td class="py-2 px-2 text-center text-gray-600">5201</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" disabled>-</button>
                                        <input type="text" value="0" class="w-10 h-7 border rounded text-center mx-1"
                                            readonly />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500">+</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- 底部操作栏 -->
            <div class="flex justify-center mt-8">
                <button class="bg-pink-500 hover:bg-pink-600 text-white px-10 py-2 rounded font-bold mx-2"
                    @click="showCartDialog = true">加采购单</button>
                <button class="bg-pink-500 hover:bg-pink-600 text-white px-10 py-2 rounded font-bold mx-2"
                    @click="showOrderDialog = true">立即订购</button>
                <button
                    class="bg-white border border-pink-500 text-pink-500 px-10 py-2 rounded font-bold mx-2">收藏</button>
            </div>
        </div>
        <!-- 订购弹框 -->
        <div v-if="showOrderDialog" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div class="bg-white rounded-lg shadow-xl w-[700px] max-w-full p-8 relative">
                <button class="absolute right-4 top-4 text-2xl text-gray-400 hover:text-gray-600"
                    @click="showOrderDialog = false">×</button>
                <div class="text-center text-pink-500 text-2xl font-bold mb-8">確認訂購</div>
                <!-- 收货地址 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">收货地址</div>
                    <div class="flex-1 text-gray-700 flex items-center">
                        <i class="el-icon-location text-pink-500 mr-2"></i>仓库：深圳仓
                        <a href="#" class="ml-4 text-pink-400 text-sm hover:underline">更改收货地址</a>
                    </div>
                </div>
                <!-- 确认库存 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">确认库存</div>
                    <div class="flex-1">
                        <div class="text-gray-400 text-sm mb-1">请複製以下消息询问卖家库存是否充足？</div>
                        <div class="bg-gray-50 rounded p-3 text-gray-700 text-sm flex items-start relative">
                            <div class="flex-1">
                                我要订购以下商品，麻烦确认下是否都有货<br>
                                七小七核桃燕麦奶儿童坚果乳植物蛋白饮料学生健康营养早餐奶饮品核桃燕麦乳*8袋 x1
                            </div>
                            <a href="#" class="text-pink-400 text-sm ml-4 absolute right-4 top-3 hover:underline">複製</a>
                        </div>
                        <div class="mt-2 flex items-center">
                            <input type="checkbox" id="stockChecked" v-model="stockChecked"
                                class="mr-2 w-4 h-4 accent-pink-500">
                            <label for="stockChecked" class="text-blue-600 text-base select-none">賣家已確認庫存充足</label>
                        </div>
                    </div>
                </div>
                <!-- 卖家留言 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">卖家留言</div>
                    <div class="flex-1">
                        <input type="text" class="w-full border rounded px-3 py-2 text-sm"
                            placeholder="如果需要，您可以填写给卖家的留言" v-model="buyerMsg">
                        <div class="flex items-center mt-2 space-x-6">
                            <label class="flex items-center text-sm"><input type="radio" name="msgType" checked
                                    class="mr-1">以简體字发送</label>
                            <label class="flex items-center text-sm"><input type="radio" name="msgType"
                                    class="mr-1">以簡體字发送</label>
                        </div>
                    </div>
                </div>
                <!-- 协议勾选 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24"></div>
                    <div class="flex-1 flex items-center">
                        <input type="checkbox" id="agree" v-model="agree" class="mr-2 w-4 h-4 accent-pink-500">
                        <label for="agree" class="text-pink-500 text-sm select-none">我已阅读并同意 <a href="#"
                                class="underline">《淘宝代购服务（免国际运费包裹）用户协议》</a></label>
                    </div>
                </div>
                <!-- 按钮 -->
                <div class="flex justify-end space-x-4 mt-6">
                    <button class="px-8 py-2 rounded bg-gray-100 text-gray-600 font-bold"
                        @click="showOrderDialog = false">取消</button>
                    <button class="px-8 py-2 rounded bg-pink-500 text-white font-bold" :disabled="!agree">订購</button>
                </div>
            </div>
        </div>

        <!-- 已添加至采购车弹框 -->
        <div v-if="showCartDialog" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div class="bg-white rounded-lg shadow-xl w-[340px] max-w-full p-8 relative text-center">
                <svg class="mx-auto mb-2" width="32" height="32" fill="none" viewBox="0 0 32 32">
                    <circle cx="16" cy="16" r="16" fill="#F9A8D4" />
                    <path d="M10 17l4 4 8-8" stroke="#fff" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
                <div class="text-pink-500 text-lg font-bold mb-2 flex items-center justify-center"><span
                        class="ml-2">已添加至采购車</span></div>
                <div class="text-gray-700 mb-6">当前采购车共1种货品</div>
                <div class="flex justify-center space-x-4">
                    <button class="px-6 py-2 rounded bg-gray-100 text-gray-600 font-bold"
                        @click="showCartDialog = false">继续购物</button>
                    <button class="px-6 py-2 rounded bg-pink-500 text-white font-bold">去结算</button>
                </div>
            </div>
        </div>
    </div>



    <script>
        const app = new Vue({
            el: '#app',
            data: {
                showCartDialog: false,
                showOrderDialog: false,
                orderData: '',
                specs: [
                  { name: '核桃燕麦乳*8袋', stock: 201, price: 31.90 },
                  { name: '核桃燕麦乳*16袋', stock: 99, price: 59.90 }
                ],
                selectedSpecIdx: 0,
                count: 1,
                stockChecked: true,
                buyerMsg: '',
                invoiceType: '普通发票',
                agree: false,
                productImages: [
                  '../img/product.png',
                  '../img/product1.png',
                  '../img/product2.png',
                  '../img/product1.png',
                  '../img/product2.png',
                  '../img/product.png',
                  '../img/product1.png',
                ],
                activeImage: 0,
                thumbStart: 0,
                thumbShow: 4
            },
            computed: {
                unitPrice() {
                  return this.specs[this.selectedSpecIdx].price;
                }
            },
            methods: {
                changeCount(delta) {
                  const newCount = this.count + delta;
                  if (newCount >= 1 && newCount <= this.specs[this.selectedSpecIdx].stock) {
                    this.count = newCount;
                  }
                },
                thumbScrollLeft() {
                  if (this.thumbStart > 0) this.thumbStart--;
                },
                thumbScrollRight() {
                  if (this.thumbStart + this.thumbShow < this.productImages.length) this.thumbStart++;
                },
                submitOrder() {
                    console.log('下单数据:', this.specs[this.selectedSpecIdx], this.count);
                }
            }
        })
    </script>
</body>

</html>