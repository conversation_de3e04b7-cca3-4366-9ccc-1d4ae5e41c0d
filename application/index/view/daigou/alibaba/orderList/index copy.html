{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
  {include file="common/notice" /}
</div>

<div class="transport-box" id="OrderList">
  {include file="common/bottom_nav" /}
  <div class="left_d">
    {include file="common/left_page" /}
  </div>
  <div class="right-container relative">
    <!-- 订单列表 -->
    <div id="app" class="max-w-screen-2xl mx-auto bg-gray-50">
      <!-- 顶部导航 -->
      <div class="bg-white border-b flex items-center px-8 h-16">
        <div class="font-bold text-xl mr-8">代購訂單</div>
        <div class="flex space-x-6 text-base">
          <a href="#" @click.prevent="activeStatus = 'all'"
            :class="[activeStatus === 'all' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">全部</a>
          <a href="#" @click.prevent="activeStatus = 'pending'"
            :class="[activeStatus === 'pending' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">待付款</a>
          <a href="#" @click.prevent="activeStatus = 'purchased'"
            :class="[activeStatus === 'purchased' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已采購</a>
          <a href="#" @click.prevent="activeStatus = 'shipped'"
            :class="[activeStatus === 'shipped' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已出貨</a>
          <a href="#" @click.prevent="activeStatus = 'received'"
            :class="[activeStatus === 'received' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已收貨</a>
          <a href="#" @click.prevent="activeStatus = 'afterSale'"
            :class="[activeStatus === 'afterSale' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">售後中</a>
          <a href="#" @click.prevent="activeStatus = 'refunded'"
            :class="[activeStatus === 'refunded' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已退款</a>
          <a href="#" @click.prevent="activeStatus = 'cancelled'"
            :class="[activeStatus === 'cancelled' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已取消</a>
        </div>
      </div>
      <!-- 筛选栏 -->
      <div class="bg-white px-8 py-4  flex items-center space-x-4">
        <el-date-picker v-model="date" type="daterange" range-separator="-" start-placeholder="开始时间"
          end-placeholder="结束时间" size="small"></el-date-picker>
        <el-input v-model="keyword" placeholder="请输入关键字" size="small" class="w-56"></el-input>
        <el-button size="small" icon="el-icon-download"
          style="background-color:#ef436d;borde:none;color:#fff" @click="searchOrders">搜索</el-button>
        <el-button size="small" icon="el-icon-download"
          style="background-color:#22b573;borde:none;color:#fff" @click="exportOrders">导出</el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
      </div>
      <div class="bg-white px-8 py-4  flex items-center space-x-4">
        <el-checkbox v-model="selectAll" label="全选" class="mr-2" @change="handleSelectAll"></el-checkbox>
        <el-button size="small" @click="batchPayment">批量付款({{selectedOrders.length}})</el-button>
        <el-button size="small" @click="updateUrgePayment">更新催付({{selectedOrders.length}})</el-button>
        <el-button size="small" @click="batchCancel">批量取消({{selectedOrders.length}})</el-button>
      </div>
      <!-- 订单卡片列表 -->
      <div class="px-8 py-6 space-y-6">
        <div v-for="order in filteredOrders">
          <div v-if="order.status === 'cancelled'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <span class="text-gray-500 font-bold">已取消</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <span class="text-pink-500">淘宝</span>
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 订单内容 -->
            <div class="flex px-6 py-4">
              <img :src="order.productImg" class="w-20 h-20 object-cover rounded border mr-4" />
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                      <span v-if="order.freeShipping"
                        class="ml-2 px-2 py-0.5 bg-yellow-50 text-yellow-600 text-xs rounded">免国际运费</span>
                    </div>
                    <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                    <div class="mt-2">
                      <el-button size="mini" type="primary" plain>申请售后</el-button>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-500 text-sm">代购金额：</div>
                    <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                    <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="text-sm text-gray-500">送至：{{order.address}}</div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2">再次购买</a>
                <a href="#" class="text-pink-500 underline">订单详情</a>
              </div>
            </div>
          </div>
          <div v-else-if="order.status === 'pending'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <el-checkbox v-model="selectedOrders" :value="order.id" class="mr-2"></el-checkbox>
                <span class="text-yellow-500 font-bold">待付款</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <span class="text-pink-500">淘宝</span>
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 订单内容 -->
            <div class="flex px-6 py-4">
              <img :src="order.productImg" class="w-20 h-20 object-cover rounded border mr-4" />
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                    <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-500 text-sm">代购金额：</div>
                    <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                    <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                    <div class="text-xs text-gray-400 line-through mt-1">原价：37.60</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">送至：{{order.address}}</span>
                <span class="ml-4">订单总额：<span class="text-pink-600 font-bold">¥{{order.amount}}*4.5364= NTS
                    134</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2">再次购买</a>
                <a href="#" class="text-pink-500 underline">订单详情</a>
              </div>
            </div>
          </div>
          <div v-else-if="order.status === 'shipped'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <span class="bg-pink-50 text-pink-500 px-2 py-0.5 rounded font-medium">已发货</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <img src="../img/taobao-icon.png" class="h-4" alt="淘宝" />
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <div class="p-6">
              <div class="flex">
                <img :src="order.productImg" class="w-20 h-20 object-cover border rounded" />
                <div class="flex-1 ml-4">
                  <div class="flex justify-between">
                    <div>
                      <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                      <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                      <div class="mt-2">
                        <el-button size="mini">申请售后</el-button>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-gray-500 text-sm">代购金额：</div>
                      <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                      <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mt-4 text-sm text-gray-500">
                <div class="flex items-center aligns-center">
                  <i class="el-icon-location mr-1"></i>
                  送至：{{order.address}}
                  <div v-if="order.logistics" @mouseover="showlogistics">
                    <a href="#" class="text-pink-500">{{order.logistics.company}} {{order.logistics.number}}</a>
                    <span class="ml-2 text-yellow-500">{{order.logistics.status}}</span>
                  </div>
                </div>
                <div v-if="order.invoice" class="mt-2 text-gray-400">
                  發票將在訂單完成後發送至 {{order.invoice.to}}
                  <a href="#" class="text-green-500 ml-2">修改发票信息</a>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-2 border-t">
              <a href="#" class="text-pink-500 underline mr-2">再次购买</a>
              <a href="#" class="text-pink-500 underline">订单详情</a>
            </div>
          </div>
          <!-- 可继续添加其它状态的订单卡片 -->
          <div v-else-if="order.status === 'purchased'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <span class="text-green-500 font-bold">已付款</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <span class="text-pink-500">淘宝</span>
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 订单内容 -->
            <div class="flex px-6 py-4">
              <img :src="order.productImg" class="w-20 h-20 object-cover rounded border mr-4" />
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                      <span v-if="order.freeShipping"
                        class="ml-2 px-2 py-0.5 bg-yellow-50 text-yellow-600 text-xs rounded">免国际运费</span>
                    </div>
                    <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                    <div class="text-gray-500 text-sm mt-1">数量：{{order.quantity}}</div>
                    <div class="mt-2">
                      <el-button size="mini" type="primary" plain>申请售后</el-button>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-500 text-sm">代购金额：</div>
                    <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                    <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="text-sm text-gray-500">送至：{{order.address}}</div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2">再次购买</a>
                <a href="#" class="text-pink-500 underline">订单详情</a>
              </div>
            </div>
          </div>
          <div v-else-if="order.status === 'received'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <span class="text-blue-500 font-bold">已完成</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <span class="text-pink-500">淘宝</span>
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 订单内容 -->
            <div class="flex px-6 py-4">
              <img :src="order.productImg" class="w-20 h-20 object-cover rounded border mr-4" />
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                      <span v-if="order.freeShipping"
                        class="ml-2 px-2 py-0.5 bg-yellow-50 text-yellow-600 text-xs rounded">免国际运费</span>
                    </div>
                    <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                    <div class="text-gray-500 text-sm mt-1">数量：{{order.quantity}}</div>
                    <div class="mt-2">
                      <el-button size="mini" type="primary" plain>申请售后</el-button>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-500 text-sm">代购金额：</div>
                    <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                    <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="text-sm text-gray-500">送至：{{order.address}}</div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2">再次购买</a>
                <a href="#" class="text-pink-500 underline">订单详情</a>
              </div>
            </div>
          </div>
          <div v-else-if="order.status === 'refunded'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <span class="text-red-500 font-bold">已退款</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <span class="text-pink-500">淘宝</span>
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 订单内容 -->
            <div class="flex px-6 py-4">
              <img :src="order.productImg" class="w-20 h-20 object-cover rounded border mr-4" />
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                      <span v-if="order.freeShipping"
                        class="ml-2 px-2 py-0.5 bg-yellow-50 text-yellow-600 text-xs rounded">免国际运费</span>
                    </div>
                    <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                    <div class="text-gray-500 text-sm mt-1">数量：{{order.quantity}}</div>
                    <div class="mt-2">
                      <el-button size="mini" type="primary" plain>申请售后</el-button>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-500 text-sm">代购金额：</div>
                    <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                    <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="text-sm text-gray-500">送至：{{order.address}}</div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2">再次购买</a>
                <a href="#" class="text-pink-500 underline">订单详情</a>
              </div>
            </div>
          </div>
          <!-- 可继续添加其它状态的订单卡片 -->
          <div v-else-if="order.status === 'afterSale'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <span class="text-orange-500 font-bold">售后中</span>
                <span>{{order.createTime}}</span>
                <span>订单号：{{order.id}}</span>
                <span class="text-pink-500">淘宝</span>
                <span>淘宝订单号：{{order.tbOrderId}}</span>
                <span>{{order.shopName}}</span>
                <a href="#" class="text-pink-500 underline">查看他的所有订单</a>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 订单内容 -->
            <div class="flex px-6 py-4">
              <img :src="order.productImg" class="w-20 h-20 object-cover rounded border mr-4" />
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <a href="#" class="text-pink-600 hover:text-pink-700 font-medium">{{order.productName}}</a>
                      <span v-if="order.freeShipping"
                        class="ml-2 px-2 py-0.5 bg-yellow-50 text-yellow-600 text-xs rounded">免国际运费</span>
                    </div>
                    <div class="text-gray-500 text-sm mt-1">{{order.productSpec}}</div>
                    <div class="text-gray-500 text-sm mt-1">数量：{{order.quantity}}</div>
                    <div class="mt-2">
                      <el-button size="mini" type="warning" plain>售后处理中</el-button>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-gray-500 text-sm">代购金额：</div>
                    <div class="text-pink-600 font-bold text-lg">¥{{order.amount}}</div>
                    <div class="text-xs text-gray-400">（含运费：{{order.shipping}}元）</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="text-sm text-gray-500">送至：{{order.address}}</div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2">查看售后</a>
                <a href="#" class="text-pink-500 underline">订单详情</a>
              </div>
            </div>
          </div>
          <!-- 可继续添加其它状态的订单卡片 -->
        </div>
      </div>
    </div>
  </div>
</div>

<script>

  const app = new Vue({
    el: '#OrderList',
    mixins: [bottomNavMixin], // 引入底部导航栏功能
    data: {
      date: '',
      keyword: '',
      orderList: <?php echo json_encode($list); ?>,
      selectedOrders: [],
      selectAll: false,
      activeStatus: 'all',
      orders: []
    },
    mounted() {
      console.log(this.orderList, 'orderList');
      this.processOrderData();
    },
    computed: {
      filteredOrders() {
        if (this.activeStatus === 'all') {
          return this.orders;
        }
        return this.orders.filter(order => order.status === this.activeStatus);
      }
    },
    watch: {
      selectedOrders: {
        handler(newVal) {
          // 同步全选状态
          const filteredOrderIds = this.filteredOrders.map(order => order.id);
          this.selectAll = filteredOrderIds.length > 0 && 
                          filteredOrderIds.every(id => newVal.includes(id));
        },
        deep: true
      }
    },
    methods: {
      processOrderData() {
        // 状态映射：0-5分别对应待付款、已付款、已出货、已完成、已取消、已退款
        const statusMap = {
          0: 'pending',    // 待付款
          1: 'purchased',  // 已付款
          2: 'shipped',    // 已出货
          3: 'received',   // 已完成
          4: 'cancelled',  // 已取消
          5: 'refunded'    // 已退款
        };

        this.orders = this.orderList.map(order => {
          // 处理订单列表中的每个订单
          const orderItems = order.list || [];
          
          return orderItems.map(item => {
            // 解析商品信息
            let productData = [];
            try {
              const productItems = JSON.parse(item.productItems);
              productData = productItems.data || [];
            } catch (e) {
              console.error('解析商品信息失败:', e);
            }

            // 处理每个商品项
            return productData.map(product => {
              const status = statusMap[item.status] || 'pending';
              const createTime = new Date(item.createtime * 1000).toLocaleString('zh-CN');
              
              // 检查是否有退款信息，如果有则标记为售后中
              let finalStatus = status;
              if (parseFloat(item.refund) > 0 && status !== 'refunded') {
                finalStatus = 'afterSale';
              }
              
              return {
                id: item.id,
                tbOrderId: item.orderId,
                shopName: item.sellerLoginId,
                status: finalStatus,
                createTime: createTime,
                productName: product.name,
                productSpec: product.cargoNumber,
                productImg: product.productImgUrl && product.productImgUrl[0] ? product.productImgUrl[0] : '',
                amount: parseFloat(product.itemAmount).toFixed(2),
                shipping: parseFloat(item.shippingFee).toFixed(2),
                address: `收货地址ID: ${item.addressId}`,
                freeShipping: parseFloat(item.shippingFee) === 0,
                quantity: product.quantity,
                price: product.price,
                refund: parseFloat(item.refund).toFixed(2),
                logistics: item.waybill ? {
                  company: item.wbname || '未知物流',
                  number: item.waybill,
                  status: '运输中'
                } : null,
                invoice: null, // 发票信息可以根据需要添加
                originalPrice: (parseFloat(product.price) * 1.2).toFixed(2), // 原价计算
                warehouse: item.wh || '未知仓库'
              };
            });
          }).flat(); // 扁平化数组
        }).flat(); // 再次扁平化

        console.log('处理后的订单数据:', this.orders);
      },
      showlogistics(){
        console.log(123,123)
      },
      // 全选/取消全选
      handleSelectAll() {
        if (this.selectAll) {
          this.selectedOrders = this.filteredOrders.map(order => order.id);
        } else {
          this.selectedOrders = [];
        }
      },
      // 批量付款
      batchPayment() {
        if (this.selectedOrders.length === 0) {
          this.$message.warning('请先选择要付款的订单');
          return;
        }
        console.log('批量付款订单:', this.selectedOrders);
        // 这里可以添加实际的付款逻辑
      },
      // 更新催付
      updateUrgePayment() {
        if (this.selectedOrders.length === 0) {
          this.$message.warning('请先选择要催付的订单');
          return;
        }
        console.log('更新催付订单:', this.selectedOrders);
        // 这里可以添加实际的催付逻辑
      },
      // 批量取消
      batchCancel() {
        if (this.selectedOrders.length === 0) {
          this.$message.warning('请先选择要取消的订单');
          return;
        }
        console.log('批量取消订单:', this.selectedOrders);
        // 这里可以添加实际的取消逻辑
      },
      // 搜索订单
      searchOrders() {
        console.log('搜索条件:', {
          date: this.date,
          keyword: this.keyword
        });
        // 这里可以添加实际的搜索逻辑
      },
      // 导出订单
      exportOrders() {
        console.log('导出订单数据');
        // 这里可以添加实际的导出逻辑
      },
      // 重置搜索条件
      resetSearch() {
        this.date = '';
        this.keyword = '';
        console.log('重置搜索条件');
      }
    }
  });
</script>