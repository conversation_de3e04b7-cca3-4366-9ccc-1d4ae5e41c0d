{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
  {include file="common/notice" /}
</div>

<div class="transport-box" id="OrderList">
  {include file="common/bottom_nav" /}
  <div class="left_d">
    {include file="common/left_page" /}
  </div>
  <div class="right-container relative">
    <!-- 订单列表 -->
    <div id="app" class="max-w-screen-2xl mx-auto bg-gray-50">
      <!-- 顶部导航 -->
      <div class="bg-white border-b flex items-center px-8 h-16">
        <div class="font-bold text-xl mr-8">代購訂單</div>
        <div class="flex space-x-6 text-base">
          <a href="#" @click.prevent="activeStatus = 'all'"
            :class="[activeStatus === 'all' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">全部</a>
          <a href="#" @click.prevent="activeStatus = 'pending'"
            :class="[activeStatus === 'pending' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">待付款</a>
          <a href="#" @click.prevent="activeStatus = 'purchased'"
            :class="[activeStatus === 'purchased' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已采購</a>
          <a href="#" @click.prevent="activeStatus = 'shipped'"
            :class="[activeStatus === 'shipped' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已出貨</a>
          <a href="#" @click.prevent="activeStatus = 'received'"
            :class="[activeStatus === 'received' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已收貨</a>
          <!-- <a href="#" @click.prevent="activeStatus = 'afterSale'"
            :class="[activeStatus === 'afterSale' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">售後中</a> -->
          <a href="#" @click.prevent="activeStatus = 'refunded'"
            :class="[activeStatus === 'refunded' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已退款</a>
          <a href="#" @click.prevent="activeStatus = 'cancelled'"
            :class="[activeStatus === 'cancelled' ? 'text-pink-500 font-bold bg-pink border-pink-500' : 'pt-2 text-gray-600 hover:text-pink-500', 'pb-2']">已取消</a>
        </div>
      </div>
      <!-- 筛选栏 -->
      <div class="bg-white px-8 py-4  flex items-center space-x-4">
        <el-date-picker v-model="date" type="daterange" range-separator="-" start-placeholder="开始时间"
          end-placeholder="结束时间" size="small"></el-date-picker>
        <el-input v-model="keyword" placeholder="请输入关键字" size="small" class="w-56"></el-input>
        <el-button size="small" icon="el-icon-download"
          style="background-color:#ef436d;borde:none;color:#fff" @click="searchOrders">搜索</el-button>
        <el-button size="small" icon="el-icon-download"
          style="background-color:#22b573;borde:none;color:#fff" @click="exportOrders">导出</el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
      </div>
      <div class="bg-white px-8 py-4  flex items-center space-x-4">
        <el-checkbox v-model="selectAll" label="全选" class="mr-2" @change="handleSelectAll"></el-checkbox>
        <el-button size="small" @click="batchPayment">批量付款({{selectedOrders.length}})</el-button>
        <!-- <el-button size="small" @click="updateUrgePayment">更新催付({{selectedOrders.length}})</el-button> -->
        <el-button size="small" @click="batchCancel">批量取消({{selectedOrders.length}})</el-button>
        <el-button size="small" type="info" @click="clearAllSelections">清除选择</el-button>
      </div>
      <!-- 收货地址 -->
      <div class="bg-white rounded  p-6 mb-4" v-if="activeStatus === 'pending' || activeStatus === 'all'">
        <div class="flex items-center mb-2">
          <span class="font-bold text-lg">收貨地址</span>
        </div>
        <div class="text-gray-700 flex items-center">
          <i class="el-icon-location text-pink-500 mr-2"></i>
          <span v-if="isValidAddress(selectedAddress)">送至：{{formatAddress(selectedAddress)}}</span>
          <span v-else class="text-red-500">请选择收货地址</span>
          <el-button size="mini" class="ml-auto" @click="showAddressModal = true">選擇地址</el-button>
        </div>
      </div>
      <!-- 订单卡片列表 -->
      <div class="px-8 py-6 space-y-6">
        <div v-for="order in filteredOrders" :key="order.id">
          <div v-if="order.status === 'cancelled'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <!-- <el-checkbox :value="order.id" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox> -->
                <span class="text-gray-500 font-bold">已取消</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderNo}}</span>
                <span class="text-pink-500">淘宝</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                
              </div>
            </div>
            
          </div>
          <div v-else-if="order.status === 'pending'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <el-checkbox :value="selectedOrders.includes(order.id)" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox>
                <span class="text-yellow-500 font-bold">待付款</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderId}}</span>
                <span class="text-pink-500">淘宝</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                <a class="text-pink-500 underline mr-2" style="cursor:pointer" @click="godetail(order)">订单详情</a>
              </div>
            </div>
          </div>
          <div v-else-if="order.status === 'shipped'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <!-- <el-checkbox :value="order.id" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox> -->
                <span class="bg-pink-50 text-pink-500 px-2 py-0.5 rounded font-medium">已发货</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderNo}}</span>
                <img src="../img/taobao-icon.png" class="h-4" alt="淘宝" />
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
                <div class="ml-4" @mouseenter="showdetail(order)" style="color:#ef436d;cursor:pointer">物流信息</div>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                
              </div>
            </div>
           
          </div>
          <!-- 可继续添加其它状态的订单卡片 -->
          <div v-else-if="order.status === 'purchased'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <!-- <el-checkbox :value="order.id" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox> -->
                <span class="text-green-500 font-bold">已付款</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderNo}}</span>
                <span class="text-pink-500">淘宝</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                
              </div>
            </div>
            
          </div>
          <div v-else-if="order.status === 'received'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <!-- <el-checkbox :value="order.id" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox> -->
                <span class="text-blue-500 font-bold">已完成</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderNo}}</span>
                <span class="text-pink-500">淘宝</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                
              </div>
            </div>
            
          </div>
          <div v-else-if="order.status === 'refunded'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <!-- <el-checkbox :value="order.id" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox> -->
                <span class="text-red-500 font-bold">已退款</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderNo}}</span>
                <span class="text-pink-500">淘宝</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                
              </div>
            </div>
            
          </div>
          <div v-else-if="order.status === 'afterSale'" class="bg-white rounded border overflow-hidden">
            <!-- 订单头部 -->
            <div class="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
              <div class="flex items-center space-x-4 text-sm">
                <!-- <el-checkbox :value="order.id" :checked="selectedOrders.includes(order.id)" @change="handleOrderSelect(order.id)" class="mr-2"></el-checkbox> -->
                <span class="text-orange-500 font-bold">售后中</span>
                <span>{{order.createTime}}</span>
                <span>平台流水号：{{order.orderNo}}</span>
                <span>阿里订单号：{{order.orderNo}}</span>
                <span class="text-pink-500">淘宝</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button type="text" size="mini" icon="el-icon-edit"></el-button>
                <el-button type="text" size="mini" icon="el-icon-delete"></el-button>
              </div>
            </div>
            <!-- 商品项 -->
            <div class="px-6 py-4 border-b">
              <!-- 商品列表 -->
              <div v-if="order.productItems" class="space-y-3">
                <div v-for="(item, index) in parseProductItems(order.productItems)" :key="index" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                  <!-- 商品图片 -->
                  <div class="flex-shrink-0">
                    <img v-if="item.productImgUrl && item.productImgUrl.length > 0" 
                         :src="getImageProxyUrl(item.productImgUrl[0])" 
                         :alt="item.name"
                         class="w-16 h-16 object-cover rounded border"
                        >
                    <div v-else class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                      <i class="el-icon-picture-outline text-gray-400"></i>
                    </div>
                  </div>
                  
                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">{{item.name}}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>规格：{{item.cargoNumber}}</span>
                      <span class="ml-2">数量：{{item.quantity}}</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span>单价：¥{{item.price}}</span>
                      <span class="ml-2">小计：¥{{item.itemAmount}}</span>
                    </div>
                  </div>
                  
                  <!-- 商品价格 -->
                  <div class="flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-pink-600">¥{{item.itemAmount}}</div>
                    <div class="text-xs text-gray-400">¥{{item.price}} × {{item.quantity}}</div>
                  </div>
                </div>
              </div>
              
              <!-- 订单信息 -->
              <div class="flex items-start justify-between mt-4">
                <div class="flex-1">
                  <div class="text-gray-500 text-sm">订单号：{{order.orderNo}}</div>
                  <div class="text-gray-500 text-sm mt-1">买家：{{order.buyerLoginId}}</div>
                  <div class="text-gray-500 text-sm mt-1">卖家：{{order.sellerLoginId}}</div>
                  <div v-if="order.wh" class="text-gray-500 text-sm mt-1">仓库：{{order.wh}}</div>
                </div>
                <div class="text-right">
                  <div class="text-gray-500 text-sm">商品总额：¥{{order.sumProductPayment}}</div>
                  <div class="text-gray-500 text-sm">运费：¥{{order.shippingFee}}</div>
                  <div class="text-gray-500 text-sm">优惠券：-¥{{order.couponFee}}</div>
                  <div class="text-pink-600 font-bold text-lg mt-1">¥{{order.totalAmount}}</div>
                </div>
              </div>
            </div>
            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-3 flex items-center justify-between border-t">
              <div class="flex items-center text-sm">
                <span class="text-gray-500">订单总额：¥{{order.orderTotalAmount}}</span>
                <span class="ml-4">汇率：{{ehg}}</span>
                <span class="ml-4 ">台币：NTS <span class="text-pink-500"> {{(parseFloat(order.orderTotalAmount) * parseFloat(ehg)).toFixed(2)}}</span></span>
              </div>
              <div class="flex items-center space-x-2">
                <a href="#" class="text-pink-500 underline mr-2" @click="cancelOrder(order)">取消订单</a>
                <a href="#" class="text-pink-500 underline mr-2" @click="buyagain(order)">再次购买</a>
                
              </div>
            </div>
            
          </div>
          <!-- 地址选择弹框 -->
          <div v-if="showAddressModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div class="bg-white rounded-xl shadow-xl p-8 w-full max-w-2xl relative">
                <div class="text-center text-pink-500 text-2xl font-bold mb-6">選擇收貨地址</div>
                
                <!-- 地址列表 -->
                <div class="max-h-96 overflow-y-auto">
                    <div v-for="(address, index) in addlist" :key="index" 
                        class="border rounded-lg p-4 mb-3 cursor-pointer hover:bg-gray-50 transition-colors"
                        :class="{'border-pink-500 bg-pink-50': selectedAddress.addressId === address.addressId}"
                        @click="selectAddress(address)">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="font-bold text-gray-800">{{address.addressCodeText}}</span>
                                    <span v-if="address.isDefault" class="ml-2 px-2 py-1 bg-pink-500 text-white text-xs rounded">默认</span>
                                </div>
                                <div class="text-gray-600 text-sm mb-1">{{address.address}}</div>
                                <div class="text-gray-500 text-xs">
                                    联系人：{{address.fullName}} | 电话：{{address.mobilePhone
          }}
                                </div>
                            </div>
                            <div class="ml-4">
                                <el-radio v-model="selectedAddress.addressId" :label="address.addressId" class="text-pink-500"></el-radio>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4 mt-6 pt-4 border-t">
                    <button class="px-6 py-2 rounded border text-gray-500 bg-white hover:bg-gray-100"
                        @click="showAddressModal = false">取消</button>
                    <button class="px-6 py-2 rounded bg-pink-500 text-white font-bold hover:bg-pink-600"
                        @click="confirmAddress">確定</button>
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 物流信息显影 -->
  <div v-if="showLogisticsInfo" class="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 w-96 max-h-96 overflow-hidden" style="left:40% !important">
    <div class="bg-pink-500 text-white px-4 py-2 flex items-center justify-between">
      <div class="font-bold">物流信息</div>
      <button @click="closeLogisticsInfo" class="text-white hover:text-gray-200">
        <i class="el-icon-close"></i>
      </button>
    </div>
    
    <div class="p-4 max-h-80 overflow-y-auto">
      <!-- 物流基本信息 -->
      <div v-if="logisticsInfo" class="mb-4 p-3 bg-gray-50 rounded text-sm">
        <div class="grid grid-cols-1 gap-2">
          <div>
            <span class="text-gray-600">运单号：</span>
            <span class="font-medium">{{logisticsInfo.logisticsBillNo || '暂无'}}</span>
          </div>
          <div>
            <span class="text-gray-600">物流公司：</span>
            <span class="font-medium">{{logisticsInfo.logisticsCompany || '暂无'}}</span>
          </div>
          <div>
            <span class="text-gray-600">订单号：</span>
            <span class="font-medium">{{logisticsInfo.orderId || '暂无'}}</span>
          </div>
        </div>
      </div>
      
      <!-- 物流轨迹 -->
      <div v-if="logisticsInfo && logisticsInfo.logisticsSteps" class="space-y-3">
        <div class="text-sm font-bold text-gray-800 mb-3">物流轨迹</div>
        <div class="relative">
          <!-- 时间线 -->
          <div class="absolute left-2 top-0 bottom-0 w-0.5 bg-gray-200"></div>
          
          <div v-for="(step, index) in logisticsInfo.logisticsSteps" :key="index" class="relative flex items-start mb-3">
            <!-- 时间线节点 -->
            <div class="flex-shrink-0 w-4 h-4 rounded-full border-2 border-pink-500 bg-white flex items-center justify-center mr-3 relative z-10">
              <div class="w-2 h-2 rounded-full bg-pink-500"></div>
            </div>
            
            <!-- 物流信息 -->
            <div class="flex-1 bg-white p-3 rounded border text-xs">
              <div class="flex items-center justify-between mb-1">
                <div class="text-gray-500">{{formatLogisticsTime(step.acceptTime)}}</div>
                <div v-if="index === 0" class="text-xs bg-pink-500 text-white px-1 py-0.5 rounded">最新</div>
              </div>
              <div class="text-gray-700 text-xs leading-relaxed">{{step.remark}}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无物流信息 -->
      <div v-else class="text-center py-4">
        <i class="el-icon-truck text-2xl text-gray-300 mb-2"></i>
        <div class="text-gray-500 text-sm">暂无物流信息</div>
      </div>
    </div>
  </div>
</div>

<script>

  const app = new Vue({
    el: '#OrderList',
    mixins: [bottomNavMixin], // 引入底部导航栏功能
    data: {
      date: '',
      keyword: '',
      orderList: <?php echo json_encode($list); ?>,
      addlist: <?php echo json_encode($addr); ?>,
      ehg: <?php echo json_encode($ehg); ?>,
      selectedOrders: [],
      selectAll: false,
      activeStatus: 'all',
      orders: [],
      checked: true,
      showAddressModal:false,
      goodsDetail:{},
      selectedAddress: {}, // 新增选中的地址
      showLogisticsInfo: false, // 新增物流信息弹框显示状态
      logisticsInfo: {logisticsCompany:''} // 新增物流信息对象
    },
    mounted() {
      console.log(this.orderList, 'orderList');
      this.processOrderData();
      this.updateSelectAllStatus();
      console.log('初始化完成，selectedOrders:', this.selectedOrders,this.ehg);
      // 默认选中第一个地址
      if (this.addlist && this.addlist.length > 0) {
          this.selectedAddress = this.addlist[0];
      }
    },
    computed: {
      filteredOrders() {
        if (this.activeStatus === 'all') {
          return this.orders;
        }
        return this.orders.filter(order => order.status === this.activeStatus);
      }
    },
    watch: {
      selectedOrders: {
        handler(newVal) {
          this.updateSelectAllStatus();
        },
        deep: true
      },
      filteredOrders: {
        handler() {
          this.updateSelectAllStatus();
        },
        deep: true
      },
      activeStatus: {
        handler(newStatus) {
          console.log('订单状态切换:', newStatus);
          // 状态切换时清理无效选择
          this.cleanupInvalidSelections();
          // 更新全选状态
          this.updateSelectAllStatus();
        }
      }
    },
    methods: {
      getImageProxyUrl(originalUrl) {
          if (!originalUrl) return '';
          // 使用图片代理
          return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
      },
      // 更新全选状态
      updateSelectAllStatus() {
          const filteredOrderIds = this.filteredOrders.map(order => order.id);
        const selectedFilteredIds = this.selectedOrders.filter(id => filteredOrderIds.includes(id));
        
          // 只有当筛选后的订单都存在且都被选中时，才显示为全选状态
          this.selectAll = filteredOrderIds.length > 0 && 
                        selectedFilteredIds.length === filteredOrderIds.length;
      },
      
      // 处理单个订单选择
      handleOrderSelect(orderId) {
        const index = this.selectedOrders.indexOf(orderId);
        if (index > -1) {
          // 如果已选中，则取消选中
          this.selectedOrders.splice(index, 1);
        } else {
          // 如果未选中，则选中
          this.selectedOrders.push(orderId);
        }
        console.log('订单选择状态更新:', orderId, '当前选中:', this.selectedOrders);
      },
      
      // 全选/取消全选
      handleSelectAll() {
        const filteredIds = this.filteredOrders.map(order => order.id);
        
        if (this.selectAll) {
          // 当前是全选状态，执行取消全选
          this.selectedOrders = this.selectedOrders.filter(id => !filteredIds.includes(id));
        } else {
          // 当前不是全选状态，执行全选
          const newSelectedIds = filteredIds.filter(id => !this.selectedOrders.includes(id));
          this.selectedOrders = [...this.selectedOrders, ...newSelectedIds];
        }
        
        console.log('全选操作后:', this.selectedOrders);
      },
      
      // 清除所有选择
      clearAllSelections() {
        this.selectedOrders = [];
        this.selectAll = false;
        console.log('清除所有选择');
      },
      
      // 获取当前筛选下已选中的订单
      getSelectedFilteredOrders() {
        const filteredIds = this.filteredOrders.map(order => order.id);
        return this.selectedOrders.filter(id => filteredIds.includes(id));
      },
      
      // 获取当前筛选下已选中的订单数量
      getSelectedFilteredCount() {
        return this.getSelectedFilteredOrders().length;
      },
      
      processOrderData() {
        // 状态映射：0-5分别对应待付款、已付款、已出货、已完成、已取消、已退款
        const statusMap = {
          0: 'pending',    // 待付款
          1: 'purchased',  // 已付款
          2: 'shipped',    // 已出货
          3: 'received',   // 已完成
          4: 'refunded',  // 已退款
          5: 'cancelled'    // 已取消
        };

        this.orders = [];
        
        console.log('原始订单数据:', this.orderList);
        
        this.orderList.forEach(order => {
          const status = statusMap[order.status] || 'pending';
          const createTime = new Date(order.createtime * 1000).toLocaleString('zh-CN');
          
          // 检查是否有退款信息，如果有则标记为售后中
          let finalStatus = status;
          if (parseFloat(order.refund || '0') > 0 && status !== 'refunded') {
            finalStatus = 'afterSale';
          }

          const orderItem = {
            id: order.id, // 直接使用订单ID
            orderNo: order.no,
            orderId: order.orderId,
            status: finalStatus,
            createTime: createTime,
            totalAmount: parseFloat(order.totalAmount || '0').toFixed(2),
            shippingFee: parseFloat(order.shippingFee || '0').toFixed(2),
            refund: parseFloat(order.refund || '0').toFixed(2),
            addressId: order.addressId,
            buyerLoginId: order.buyerLoginId,
            sellerLoginId: order.sellerLoginId,
            sellerOpenId: order.sellerOpenId,
            waybill: order.waybill,
            wbname: order.wbname,
            wh: order.wh,
            couponFee: parseFloat(order.couponFee || '0').toFixed(2),
            sumProductPayment: parseFloat(order.sumProductPayment || '0').toFixed(2),
            orderTotalAmount: parseFloat(order.totalAmount || '0').toFixed(2),
            productItems: order.productItems
          };
          this.orders.push(orderItem);
        });

        console.log('处理后的订单数据:', this.orders);
        console.log('订单ID列表:', this.orders.map(o => o.id));
      },
      showlogistics(){
        console.log(123,123)
      },
      // 批量操作前验证
      validateBatchOperation() {
        if (this.selectedOrders.length === 0) {
          this.$message.warning('请先选择要操作的订单');
          return false;
        }
        
        // 验证选择状态
        const validation = this.validateSelectionState();
        if (!validation.isValid) {
          console.warn('选择状态验证失败:', validation.issues);
          this.$message.error('选择状态存在问题，请重新选择订单');
          return false;
        }
        
        return true;
      },
      
      batchPayment() {
          if (!this.validateBatchOperation()) {
            return;
          }
          console.log('批量付款订单ID:', this.selectedOrders);

          // 发送请求到后端
          this.$confirm('确认要批量付款选中的订单吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const data = this.selectedOrders.map(order => ({ index: order }));
            // 这里添加实际的付款逻辑
            axios.post('aliBatchOrder', {"data":data,addr:this.selectedAddress.addressId})
              .then(res => {
                console.log(res, 123);
                if(res.data.code == 0){
                  this.$message.success('批量付款请求已发送');
                  window.location.href = `daigouAliOrderConfirm?no=${res.data.id}`;
                }else{
                  this.$message.error(res.data.msg);
                }
               
              })
              .catch(error => {
                console.error('请求失败:', error);
                this.$message.error('批量付款失败');
              });
          }).catch(() => {
            this.$message.info('已取消批量付款');
          });
      },
      // 批量取消
      async batchCancel() {
        if (!this.validateBatchOperation()) {
          return;
        }
        const selectedOrderIds = this.getSelectedOrderIds();
        
        console.log('批量取消订单ID:', selectedOrderIds);
        // 发送请求到后端
        const data = selectedOrderIds.map(order => ({ index: order }));
        let res = await axios.post(`aliBatchCancel`, {"data":data});
        if(res.data.code == '0'){
          this.$message.success('批量取消请求已发送');
          window.location.href = 'aliOrderList';
        }else{
          this.$message.error('批量取消失败');
        }
        console.log(res,123)
        // 这里添加实际的取消逻辑
      
      },
      // 搜索订单
      searchOrders() {
        console.log('搜索条件:', {
          date: this.date,
          keyword: this.keyword
        });
        
        // 清理无效的选择（如果订单被筛选掉）
        this.cleanupInvalidSelections();
        
        // 这里可以添加实际的搜索逻辑
      },
      // 导出订单
      exportOrders() {
        console.log('导出订单数据');
        // 这里可以添加实际的导出逻辑
      },
      // 重置搜索条件
      resetSearch() {
        this.date = '';
        this.keyword = '';
        console.log('重置搜索条件');
        
        // 清理无效的选择
        this.cleanupInvalidSelections();
      },
      
      // 清理无效的选择
      cleanupInvalidSelections() {
        const allOrderIds = this.orders.map(order => order.id);
        const validSelections = this.selectedOrders.filter(id => allOrderIds.includes(id));
        
        if (validSelections.length !== this.selectedOrders.length) {
          console.log('清理无效选择:', {
            before: this.selectedOrders,
            after: validSelections
          });
          this.selectedOrders = validSelections;
        }
      },
      godetail(i){
        window.location.href = `daigouAliOrderConfirm?no=${i.orderNo}`;
      },
      // 验证选择状态完整性
      validateSelectionState() {
        const issues = [];
        
        // 检查是否有重复的订单ID
        const duplicateIds = this.selectedOrders.filter((id, index) => this.selectedOrders.indexOf(id) !== index);
        if (duplicateIds.length > 0) {
          issues.push(`发现重复的订单ID: ${duplicateIds.join(', ')}`);
        }
        
        // 检查是否有不存在的订单ID
        const allOrderIds = this.orders.map(order => order.id);
        const invalidIds = this.selectedOrders.filter(id => !allOrderIds.includes(id));
        if (invalidIds.length > 0) {
          issues.push(`发现无效的订单ID: ${invalidIds.join(', ')}`);
        }
        
        // 检查全选状态是否正确
        const filteredIds = this.filteredOrders.map(order => order.id);
        const selectedFilteredIds = this.selectedOrders.filter(id => filteredIds.includes(id));
        const expectedSelectAll = filteredIds.length > 0 && selectedFilteredIds.length === filteredIds.length;
        
        if (this.selectAll !== expectedSelectAll) {
          issues.push(`全选状态不正确: 期望 ${expectedSelectAll}, 实际 ${this.selectAll}`);
        }
        
        return {
          isValid: issues.length === 0,
          issues: issues
        };
      },
      // 解析商品信息
      parseProductItems(productItemsStr) {
        try {
          if (!productItemsStr) {
            return [];
          }
          
          // 如果是字符串，尝试解析为JSON
          if (typeof productItemsStr === 'string') {
            const parsed = JSON.parse(productItemsStr);
            return parsed.data || parsed || [];
          }
          
          // 如果已经是对象，直接返回
          if (typeof productItemsStr === 'object') {
            return productItemsStr.data || productItemsStr || [];
          }
          
          return [];
        } catch (error) {
          console.error('解析商品信息失败:', error);
          return [];
        }
      },
      // 选择地址
      selectAddress(address) {
          this.selectedAddress = address;
      },
      // 确认地址选择
      confirmAddress() {
          this.showAddressModal = false;
      },
      // 格式化地址显示
      formatAddress(address) {
          if (!address) return '';
          return `${address.addressCodeText || ''} ${address.address || ''}`.trim();
      },
      // 检查地址是否有效
      isValidAddress(address) {
          return address && address.addressId && address.address;
      },
      async cancelOrder(order) {
          let params = [order.orderId]
          console.log('params:', params);
          let res = await axios.post('daigouAliOrderCancel',params);
          console.log('取消:', res.data);
          
          if (res.data.code == '0') {
              this.showCancelDialog = true;
              let res1 = axios.get(`daigouAliOrderConfirm?no=${res.data.no}`);
              setTimeout(() => {
                  this.showCancelDialog = false;
                  // 直接从数组中移除取消的订单
                  window.location.href = `daigouAliOrderConfirm?no=${res.data.no}`;
              }, 1200);

          } else {
              this.$message.error(res.data.msg || '订单创建失败');
          }
          
      },
      // 根据选中的订单ID获取对应的orderId
      getSelectedOrderIds() {
          return this.selectedOrders.map(selectedId => {
              // 在orders数组中查找对应的订单
              const order = this.orders.find(o => o.id === selectedId);
              return order ? order.id : null;
          }).filter(id => id !== null); // 过滤掉无效的orderId
      },
      buyagain(res){
        console.log(res,123)
        window.location.href = `aliBuyAgain?no=${res.orderNo}`;
      },
      // 开启物流弹框
      async showdetail(res){
        let res1 = await axios.post('aliLogistics',{no:res.orderId});
        this.logisticsInfo = res1.data.data.logisticsTrace[0]
        this.logisticsInfo.logisticsCompany = res1.data.data.wbname
        // this.logisticsInfo = {
        //   logisticsBillNo: "78917447152709",
        //   logisticsCompany: "中通快递",
        //   orderId: res.orderId,
        //   logisticsSteps: [
        //     {"acceptTime":"2025-06-20 20:25:03","remark":"【金华市】 快件已由义乌苏溪龚聪聪（18072354072）揽收（物流问题无需找商家，请联系专属电话0579-82405681为您解决）"},
        //     {"acceptTime":"2025-06-20 20:25:30","remark":"【金华市】 快件已发往 义乌转运中心（物流问题无需找商家，请联系专属电话0579-82405681为您解决）"},
        //     {"acceptTime":"2025-06-20 20:29:55","remark":"【金华市】 快件已到达 义乌苏溪（物流问题无需找商家，请联系专属电话0579-82405681为您解决）"},
        //     {"acceptTime":"2025-06-20 21:15:54","remark":"【金华市】 快件已到达 义乌转运中心（物流问题无需找商家，请联系专属电话95311为您解决）"},
        //     {"acceptTime":"2025-06-20 21:19:19","remark":"【金华市】 快件已发往 深圳转运中心（物流问题无需找商家，请联系专属电话95311为您解决）"},
        //     {"acceptTime":"2025-06-20 21:20:14","remark":"【金华市】预计【6月21日】到达【深圳市】，两地距离921KM，预计将在【21日下午】为您更新快件状态，请您放心。"},
        //     {"acceptTime":"2025-06-21 14:00:43","remark":"【深圳市】 快件已到达 深圳转运中心（物流问题无需找商家，请联系专属电话95311为您解决）"},
        //     {"acceptTime":"2025-06-21 18:52:25","remark":"【深圳市】 快件已发往 深圳松岗（物流问题无需找商家，请联系专属电话95311为您解决）"},
        //     {"acceptTime":"2025-06-21 22:10:56","remark":"【深圳市】 快件已到达 深圳松岗（物流问题无需找商家，请联系专属电话0755-36561583为您解决）"},
        //     {"acceptTime":"2025-06-21 22:11:13","remark":"【深圳市】 深圳松岗 的业务员【黄晓峰（勿找商家，有事呼叫我），18688718006】正在为您派件（95720为中通快递员外呼专属号码，请放心接听，如有问题可联系网点：0755-36561583，投诉电话：0755-36561579）"},
        //     {"acceptTime":"2025-06-23 12:55:43","remark":"【深圳市】 快件已由【黄晓峰（勿找商家，有事呼叫我），18688718006】送达【前台】签收。 签收网点电话： 0755-36561583，投诉电话：0755-36561579。感谢使用中通快递，期待再次为您服务！"}
        //   ]
        // };
        this.showLogisticsInfo = true;
      },
      // 关闭物流弹框
      close(){
        this.showLogisticsInfo = false;
      },
      // 关闭物流信息弹框
      closeLogisticsInfo() {
        this.showLogisticsInfo = false;
        this.logisticsInfo = {};
      },
      // 格式化物流时间
      formatLogisticsTime(time) {
        if (!time) return '';
        const date = new Date(time);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      async godetail(i){
        let res = await axios.post(`aliOrderDetails`,i.id);
        if (res.data.code == '0') {
            window.location.href = `daigouAliOrderConfirm?no=${res.data.no}`;
        } else {
            this.$message.error(res.data.msg || '订单创建失败');
        }
          
      }
    }
  });
</script>