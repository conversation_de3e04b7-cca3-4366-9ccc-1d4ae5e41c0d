{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="shopCart">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <!-- 购物车 -->
        <div id="app" class="bg-white max-w-screen-2xl mx-auto p-8">
            <!-- 顶部Tab -->
            <div class="flex items-center border-b mb-4">
                <div class="px-6 py-2 text-pink-500 font-bold border-b-2 border-pink-500 cursor-pointer">现货（{{totalShops}}）</div>
                <!-- <div class="px-6 py-2 text-gray-400 cursor-pointer">分销</div> -->
            </div>
            <!-- 操作栏 -->
            <div class="flex items-center mb-4">
                <input type="checkbox" class="mr-2" :checked="isAllSelected" @change="toggleAll">
                <span class="mr-4">全选</span>
                <!-- <button class="border px-3 py-1 rounded mr-2" @click="collectSelected">移入收藏</button> -->
                <button class="border px-3 py-1 rounded mr-2" @click="deleteSelected">删除</button>
            </div>
            <!-- 店铺分组 -->
            <div v-for="(shop, sidx) in cartlist" :key="shop.name" class="mb-6 border-b pb-4" >
                <div class="flex items-center mb-2" v-if="shop.spec.data.length > 0">
                    <input type="checkbox" class="mr-2" v-model="shop.checked" @change="toggleShop(sidx)">
                    <span class="font-semibold" v-if="shop.shop.wangwangNick|| shop.shop ">店铺：{{ shop.subject }}</span>
                </div>
                <div v-for="(item, idx) in shop.spec.data" :key="item.id" class="flex items-start border rounded p-4 mb-2">
                    <input type="checkbox" class="mr-2" v-model="item.checked" @change="toggleItem(sidx, idx)">
                    <img :src="getImageProxyUrl(item.skuImageUrl)" alt="商品图片" class="w-16 h-16 object-cover mr-4 border rounded mt-2">
                    <div class="flex-col">
                        <a class="text-pink-500 font-bold mb-1 flex items-center" style="cursor:pointer" @click="godetail(shop.offer)">{{ shop.subject }}</a>
                        <div class="flex items-center ml-4" style="margin-left: 10px;">
                            <div class="font-bold mb-1 flex items-center " >
                                <span class="text-hidden" style="width:130px">{{ item.cargoNumber || item.skuAttributes[0].value}}</span>
                            </div>
                            <button class="border px-2 py-1" style="margin-left: 300px;"
                                @click="changeSkuCount(sidx, idx, -1)">-</button>
                            <span class="mx-2 w-6 text-center">{{ item.num }}</span>
                            <button class="border px-2 py-1" 
                                @click="changeSkuCount(sidx, idx, 1)">+</button>
                                <span class="ml-6 text-gray-600" style="margin-left: 250px;">{{ item.price
                                }}</span>
                            <span class="ml-4 text-pink-500 font-bold" style="margin-left: 250px;font-size:20px;min-width:120px"> ￥{{ (item.price * item.num).toFixed(2) }}</span>
                        </div>
                       
                    </div>
                    <div class="flex flex-col items-end justify-between ml-6 h-full mt-2" style="margin-left: 70px;">
                        <div class="text-gray-500 cursor-pointer" @click="collectSelected(shop)">移入收藏</div>
                        <div class="text-gray-500 cursor-pointer mt-2" @click="deleteItem(shop.id, shop.spec,sidx, idx)">删除</div>
                    </div>
                </div>
            </div>
            <!-- 结算栏 -->
            <div class="bg-pink-50 border-t p-6 flex justify-between items-center z-50" style="">
                <div class="flex items-center space-x-6">
                    <span class="bg-pink-100 text-pink-500 px-3 py-1 rounded text-xs font-bold">已享受PLUS会员价</span>
                    <span>货品数量{{ totalKinds }}/{{ totalShops }}</span>
                    <span>货品种类 {{ totalKinds }}</span>
                    <span>数量总计 {{ totalCount }}件</span>
                    <span>金额总计 <span class="text-pink-500 text-xl font-bold">￥{{ totalPrice }}</span></span>
                </div>
                <button
                    class="bg-pink-500 hover:bg-pink-600 text-white px-12 py-2 rounded font-bold text-lg" @click="handleAddOrder">结算</button>
            </div>
            <div v-if="showCollectDialog"
                class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
                <div class="bg-white rounded-lg shadow-lg p-8 w-80 text-center relative">
                    <div class="text-pink-500 text-2xl mb-2 flex items-center justify-center font-bold">
                        <svg class="w-7 h-7 mr-2" fill="none" stroke="currentColor" stroke-width="2"
                            viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="#fff" />
                            <path d="M8 12l2 2 4-4" stroke="#ec4899" stroke-width="2" fill="none" />
                        </svg>
                        移入成功
                    </div>
                </div>
            </div>
            <!-- 订购弹框 -->
        <div v-if="showOrderDialog" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div class="bg-white rounded-lg shadow-xl w-[700px] max-w-full p-8 relative">
                <button class="absolute right-4 top-4 text-2xl text-gray-400 hover:text-gray-600"
                    @click="showOrderDialog = false">×</button>
                <div class="text-center text-pink-500 text-2xl font-bold mb-8">確認訂購</div>
                <!-- 收货地址 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">收货地址</div>
                    <div class="flex-1 text-gray-700 flex items-center">
                        <i class="el-icon-location text-pink-500 mr-2"></i>仓库：深圳仓
                        <a href="#" class="ml-4 text-pink-400 text-sm hover:underline">更改收货地址</a>
                    </div>
                </div>
                <!-- 确认库存 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">确认库存</div>
                    <div class="flex-1">
                        <div class="text-gray-400 text-sm mb-1">请複製以下消息询问卖家库存是否充足？</div>
                        <div class="bg-gray-50 rounded p-3 text-gray-700 text-sm flex items-start relative">
                            <div class="flex-1">
                                我要订购以下商品，麻烦确认下是否都有货<br>
                                <div v-for="(item, idx) in showproduct" :key="item.sellerId" class="border-b">
                                    {{item.subject}}
                                    <span v-for="(i, idx) in item.spec" :key="item.specId">
                                        {{ i.cargoNumber }} * {{ i.num }} 
                                    </span>
                                </div>
                                
                            </div>
                            <a href="javascript:;" class="text-pink-400 text-sm ml-4 absolute right-4 top-3 hover:underline" @click="copyOrderInfo">複製</a>
                        </div>
                        <div class="mt-2 flex items-center">
                            <input type="checkbox" id="stockChecked" v-model="stockChecked"
                                class="mr-2 w-4 h-4 accent-pink-500">
                            <label for="stockChecked" class="text-blue-600 text-base select-none">賣家已確認庫存充足</label>
                        </div>
                    </div>
                </div>
                <!-- 卖家留言 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">卖家留言</div>
                    <div class="flex-1">
                        <input type="text" class="w-full border rounded px-3 py-2 text-sm"
                            placeholder="如果需要，您可以填写给卖家的留言" v-model="buyerMsg">
                        <div class="flex items-center mt-2 space-x-6">
                            <label class="flex items-center text-sm"><input type="radio" name="msgType" checked
                                    class="mr-1">以简體字发送</label>
                            <label class="flex items-center text-sm"><input type="radio" name="msgType"
                                    class="mr-1">以簡體字发送</label>
                        </div>
                    </div>
                </div>
                <!-- 协议勾选 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24"></div>
                    <div class="flex-1 flex items-center">
                        <input type="checkbox" id="agree" v-model="agree" class="mr-2 w-4 h-4 accent-pink-500">
                        <label for="agree" class="text-pink-500 text-sm select-none">我已阅读并同意 <a href="#"
                                class="underline">《淘宝代购服务（免国际运费包裹）用户协议》</a></label>
                    </div>
                </div>
                <!-- 按钮 -->
                <div class="flex justify-end space-x-4 mt-6">
                    <button class="px-8 py-2 rounded bg-gray-100 text-gray-600 font-bold"
                        @click="showOrderDialog = false">取消</button>
                    <button class="px-8 py-2 rounded bg-pink-500 text-white font-bold" @click="checkout">订購</button>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#shopCart',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            cartlist: <?php echo json_encode($list); ?>, // 后端传入
            showCollectDialog: false,
            agree: false,
            showOrderDialog :false,
            showproduct:[],
            stockChecked:false,
            buyerMsg:''
        },
        computed: {
            totalKinds() {
                // 统计所有已勾选sku的数量
                let count = 0;
                if (this.cartlist && Array.isArray(this.cartlist)) {
                    this.cartlist.forEach(shop => {
                        if (shop && shop.spec && shop.spec.data && Array.isArray(shop.spec.data)) {
                            shop.spec.data.forEach(item => {
                                if (item && item.checked && item.num) {
                                    count += parseInt(item.num) || 0;
                                }
                            });
                        }
                    });
                }
                return count;
            },
            totalShops() {
                // 统计所有sku的数量
                let count = 0;
                if (this.cartlist && Array.isArray(this.cartlist)) {
                    this.cartlist.forEach(shop => {
                        if (shop && shop.spec && shop.spec.data && Array.isArray(shop.spec.data)) {
                            shop.spec.data.forEach(item => {
                                if (item && item.num) {
                                    count += parseInt(item.num) || 0;
                                }
                            });
                        }
                    });
                }
                return count;
            },
            totalCount() {
                // 统计所有已勾选sku的总件数
                let count = 0;
                if (this.cartlist && Array.isArray(this.cartlist)) {
                    this.cartlist.forEach(shop => {
                        if (shop && shop.spec && shop.spec.data && Array.isArray(shop.spec.data)) {
                            shop.spec.data.forEach(item => {
                                if (item && item.checked && item.num) {
                                    count += parseInt(item.num) || 0;
                                }
                            });
                        }
                    });
                }
                return count;
            },
            totalPrice() {
                // 统计所有已勾选sku的总价
                let sum = 0;
                if (this.cartlist && Array.isArray(this.cartlist)) {
                    this.cartlist.forEach(shop => {
                        if (shop && shop.spec && shop.spec.data && Array.isArray(shop.spec.data)) {
                            shop.spec.data.forEach(item => {
                                if (item && item.checked && item.price && item.num) {
                                    sum += parseFloat(item.price) * parseInt(item.num);
                                }
                            });
                        }
                    });
                }
                return sum.toFixed(2);
            },
            isAllSelected() {
                if (!this.cartlist || !Array.isArray(this.cartlist) || this.cartlist.length === 0) {
                    return false;
                }
                return this.cartlist.every(shop => {
                    if (!shop || !shop.checked || !shop.spec || !shop.spec.data || !Array.isArray(shop.spec.data)) {
                        return false;
                    }
                    return shop.spec.data.every(item => item && item.checked);
                });
            }
        },
        mounted() {
            if (this.cartlist && Array.isArray(this.cartlist)) {
                this.cartlist.forEach(item => {
                    // 解析shop
                    if (typeof item.shop === 'string') {
                        try {
                            item.shop = JSON.parse(item.shop);
                        } catch (e) {
                            item.shop = {};
                        }
                    }
                    // 解析spec
                    if (typeof item.spec === 'string') {
                        try {
                            item.spec = JSON.parse(item.spec);
                        } catch (e) {
                            item.spec = { data: [] };
                        }
                    }
                });
                this.cartlist.forEach(shop => {
                    if (shop) {
                        this.$set(shop, 'checked', false);
                        if (shop.spec && shop.spec.data && Array.isArray(shop.spec.data)) {
                            shop.spec.data.forEach(item => {
                                if (item) {
                                    this.$set(item, 'checked', false);
                                }
                            });
                        }
                    }
                });
            }
            console.log( this.cartlist,890890)

        },
        methods: {
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '';
                // 使用图片代理
                return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
            },
            // 全选/取消全选
            toggleAll() {
                const checked = !this.isAllSelected;
                this.cartlist.forEach(shop => {
                    shop.checked = checked;
                    shop.spec.data.forEach(item => {
                        item.checked = checked;
                    });
                });
            },
            // 店铺全选/取消
            toggleShop(sidx) {
                const shop = this.cartlist[sidx];
                // 店铺checkbox变化时，所有商品跟随
                shop.spec.data.forEach(item => {
                    item.checked = shop.checked;
                });
            },
            // 单个商品选中
            toggleItem(sidx, idx) {
                const shop = this.cartlist[sidx];
                // 只要有一个商品被选中，父店铺即为选中
                shop.checked = shop.spec.data.some(item => item.checked);
            },
            // 数量增减
            changeSkuCount(sidx, idx, delta) {
                const item = this.cartlist[sidx].spec.data[idx];
                let num = parseInt(item.num) + delta;
                if (num < 1) num = 1;
                item.num = num;
            },
            // 批量删除
            deleteSelected(){
                let selected = [];
                this.cartlist.forEach(shop => {
                    // 只收集选中的商品
                    let selectedItems = shop.spec.data.filter(item => item.checked);
                    if(selectedItems.length > 0) {
                        selected.push({
                            id: shop.id,
                            specId: selectedItems.map(item => ({
                                specId: item.specId
                            })).reduce((acc, curr) => {
                                acc.push(curr.specId); // 合并成一个数组
                                return acc;
                            }, [])
                        });
                    }
                });
                
                // 如果没有选中的商品，直接返回
                if (selected.length === 0) {
                    this.$message && this.$message.warning('请先选择要删除的商品');
                    return;
                }
                
                // 这里可以用ajax提交selected到后端
                console.log('删除参数', selected);
                axios.post('aliShoppCart', {data:selected}).then(res => {
                    // 假设后端返回成功
                    // 前端移除已选中的商品
                    if(res.data && res.data.code == 0){
                        // 处理每个店铺的商品
                        this.cartlist = this.cartlist.map(shop => {
                            // 过滤掉已选中的商品
                            shop.spec.data = shop.spec.data.filter(item => !item.checked);
                            return shop;
                        }).filter(shop => shop.spec.data.length > 0); // 移除空的店铺
                        
                        // 更新店铺的选中状态
                        this.cartlist.forEach(shop => {
                            if (shop.spec.data.length > 0) {
                                // 如果店铺中还有商品，检查是否全选
                                shop.checked = shop.spec.data.every(item => item.checked);
                            }
                        });
                        
                        this.$message && this.$message.success('删除成功');
                    } else {
                        this.$message && this.$message.error(res.data.msg || '删除失败');
                    }
                }).catch(error => {
                    console.error('删除失败:', error);
                    this.$message && this.$message.error('删除失败，请稍后重试');
                });
            },
            // 删除商品
            async deleteItem(id,spec,sidx, idx) {
                console.log(id,spec,sidx, idx,123123)

                // 在删除前先保存要删除的商品的specId
                const specIdToDelete = spec.data[idx].specId;
                
                this.cartlist[sidx].spec.data.splice(idx, 1);
                
                // 如果店铺中没有商品了，移除整个店铺
                if (this.cartlist[sidx].spec.data.length === 0) {
                    this.cartlist.splice(sidx, 1);
                }
                
                let res = await axios.post('aliShoppCart', {data:[{id: id,specId:[specIdToDelete]}]});
                this.$message && this.$message.success('删除成功');
            },
            // 移入收藏
            async collectSelected(shop) {
                // 收藏功能
                try {
                    const collectData = {
                        price: shop.spec.data[0].price,
                        images: shop.images,
                        offer: shop.offer,
                        subject: shop.subject
                    };
                    
                    console.log('收藏参数:', collectData);
                    // 发送收藏请求到后端
                    const response = await axios.post('aliAddcollect', collectData);
                    
                    if (response.data && response.data.code == '1' && response.data.msg === '商品收藏成功') {
                        console.log(1111)

                        this.$message && this.$message.success('收藏成功');
                        this.showCollectDialog = true;
                        setTimeout(() => {
                            this.showCollectDialog = false;
                        }, 1200);
                    } else {
                        console.log(123123)

                        this.$message && this.$message.error(response.data.msg || '收藏失败');
                    }
                } catch (error) {
                    console.error('收藏失败:', error);
                    this.$message && this.$message.error('收藏失败，请稍后重试');
                }
               
            },
            // 结算
            async checkout() {
                if(!this.agree){
                    this.$message.error('请勾选并阅读《淘宝代购服务（免国际运费包裹）用户协议》');
                    return
                }
                let selected = [];
                this.cartlist.forEach(shop => {
                    // 只收集选中的商品
                    let selectedItems = shop.spec.data.filter(item => item.checked);
                    if(selectedItems.length > 0) {
                        selected.push({
                            shop: shop.shop.wangwangNick,
                            subject: shop.subject,
                            sellerId: shop.sellerId,
                            id: shop.id,
                            offer: shop.offer,
                            images: shop.images,
                            spec: selectedItems.map(item => ({
                                specId: item.specId,
                                cargoNumber: item.cargoNumber,
                                skuAttributes: item.skuAttributes,
                                price: item.price,
                                num: item.num,
                                skuImageUrl: item.skuImageUrl
                            }))
                        });
                    }
                });
                console.log('结算参数', selected);
                let res = await axios.post('aliSubmitOrder', {data:selected});

               // 创建表单并提交数据
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'aliSubmitOrder';

                // 创建隐藏的input字段来传递数据
                const dataInput = document.createElement('input');
                dataInput.type = 'hidden';
                dataInput.name = 'data';
                dataInput.value = JSON.stringify(selected);
                form.appendChild(dataInput);

                // 将表单添加到页面并提交
                document.body.appendChild(form);
                form.submit();

            },
            handleAddOrder(){
                // 判断是否有选中的商品
                let hasSelected = false;
                this.cartlist.forEach(shop => {
                    if (shop.spec.data.some(item => item.checked)) {
                        hasSelected = true;
                    }
                });
                if (!hasSelected) {
                    this.$message && this.$message.warning('请先选择商品');
                    return;
                }
                let selected = [];
                this.cartlist.forEach(shop => {
                    // 只收集选中的商品
                    let selectedItems = shop.spec.data.filter(item => item.checked);
                    if(selectedItems.length > 0) {
                        this.showproduct.push({
                            shop: shop.shop.wangwangNick,
                            subject: shop.subject,
                            sellerId: shop.sellerId,
                            id: shop.id,
                            offer: shop.offer,
                            images: shop.images,
                            spec: selectedItems.map(item => ({
                                specId: item.specId,
                                cargoNumber: item.cargoNumber,
                                skuAttributes: item.skuAttributes,
                                price: item.price,
                                num: item.num,
                                skuImageUrl: item.skuImageUrl
                            }))
                        });
                    }
                });
                console.log('结算参数', this.showproduct);

                this.showOrderDialog = true;
            },
            copyOrderInfo() {
                let text = '';
                this.showproduct.forEach(item => {
                    text += (item.subject ? item.subject : '') + '\n';
                    if (item.spec && Array.isArray(item.spec)) {
                        item.spec.forEach(i => {
                            text += `${i.cargoNumber} * ${i.num}\n`;
                        });
                    }
                });
                // 复制到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.$message && this.$message.success('复制成功');
                    }, () => {
                        this.$message && this.$message.error('复制失败，请手动复制');
                    });
                } else {
                    // 兼容老浏览器
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    document.body.appendChild(textarea);
                    textarea.select();
                    try {
                        document.execCommand('copy');
                        this.$message && this.$message.success('复制成功');
                    } catch (err) {
                        this.$message && this.$message.error('复制失败，请手动复制');
                    }
                    document.body.removeChild(textarea);
                }
            },
            godetail(id){
                window.location.href = `aliGoods?offer=${id}`;
            }
        }
    })
</script>