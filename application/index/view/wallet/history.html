{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="app">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <div class="wallet-page relative">
            <div class="title">儲值記錄</div>
            <el-table
                    :data="paginatedData"
                    class="custom-header history-table"
                    style="width: 100%">
                <el-table-column
                        label="訂單編號"
                        prop="order_no"
                        width="300">
                </el-table-column>
                <el-table-column
                        label="儲值金額"
                        prop="tb_money"
                        width="260">
                </el-table-column>
                <el-table-column
                        label="支付方式"
                        prop="pay_type"
                        width="260">
                    <template slot-scope="scope">
                        <el-tag
                                :type="scope.row.pay_type == 1 ? 'warning' : 'danger'"
                                disable-transitions>{{scope.row.pay_type == 1 ? '銀行' : '超商'}}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        label="創建時間"
                        width="260">
                    <template slot-scope="scope">
                        {{formatDate(scope.row.createtime,1)}}
                    </template>
                </el-table-column>
                <el-table-column
                        label="狀態"
                        prop="status"
                        width="260">
                    <template slot-scope="scope">
                        <span :style="{color: scope.row.status === 0 ? '#999999' : '#22B573'}">
                            {{
                                scope.row.pay_status == 0 ? '未支付' :
                                    scope.row.pay_status == 1 ? '部分支付' :
                                        scope.row.pay_status == 2 ? '已支付' : '多支付'
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作"
                        prop="view"><template slot-scope="scope">
<!--                        <span class="red fs14 pointer underline">查看</span>-->
                </template>
                </el-table-column>
            </el-table>
            <div class="footer absolute">
                <el-pagination
                        :current-page="currentPage"
                        :total="historyOrdersList.length"
                        :page-size="100"
                        :page-sizes="[5,10,20]"
                        @current-change="handleCurrentChange"
                        @size-change="handleSizeChange"
                        layout="total, sizes, prev, pager, next, jumper">
                </el-pagination>
            </div>
        </div>
    </div>
</div>
<script>
    const list = JSON.parse('<?=json_encode($rows, JSON_UNESCAPED_UNICODE)?>');
    console.log(list,'list')
    const app = new Vue({
        el: '#app',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            historyOrdersList: list,
            currentPage: 1,
            currentPageSize: 5,
            pageSize: [5, 10, 20, 50],
        },
        computed: {
            paginatedData() {
                const start = (this.currentPage - 1) * this.currentPageSize;
                const end = start + this.currentPageSize;
                return this.historyOrdersList.slice(start, end);
            },
        },
        methods:{
            handleAmount(event) {
                let value = event.target.value;
                // 移除非数字和小数点
                value = value.replace(/[^\d.]/g, '');
                // 处理多个小数点，只保留第一个
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                // 处理以小数点开头的情况，前面加0
                if (value.startsWith('.')) {
                    value = '';
                }
                // 处理前导零（如00123 → 123，但允许0.123）
                if (value.length > 1 && value[0] === '0' && value[1] !== '.') {
                    value = value.replace(/^0+/, '');
                    // 如果替换后为空或以小数点开头，补0
                    if (value === '' || value.startsWith('.')) {
                        value = '0' + value;
                    }
                }
                this.TWDAmount = value;
            },
            formatDate(timestamp,type) {
                return utils.formatDate(timestamp,type);
            },
            // 分頁
            handleCurrentChange(page) {
                this.currentPage = page;
            },
            handleSizeChange(val) {
                this.currentPageSize = val
                this.currentPage = 1
            },
        },
    })
</script>

<style>
    .history-table th.el-table__cell:first-child {
        padding-left: 24px !important;
    }

    .history-table td.el-table__cell:first-child {
        padding-left: 24px !important;
    }
</style>
