{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<input type="hidden" id="listData" value='{:json_encode($list)}'>
<div class="transport-box" id="app">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container" >
        <div class="wallet-page relative">
            <div class="title">賬戶明細</div>
            <div class="form-box">
                <div style="display: flex; gap: 32px">
                    <div style="display: flex; align-items: center">
                        <div class="title-filter">狀態：</div>
                        <el-select placeholder="全部/儲值/支出/退款" v-model="filterStatus" class="select-input">
                            <el-option :value="null" label="全部"></el-option>
                            <el-option :key="index"
                                       :label="item"
                                       :value="item"
                                       v-for="(item,index) in uniqueTypes"></el-option>
                        </el-select>
                    </div>
                    <div style="display: flex; align-items: center">
                        <div class="title-filter">時間：</div>
                        <div>
                            <el-date-picker
                                    end-placeholder="结束日期"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    type="daterange"
                                    v-model="filterDateRange">
                            </el-date-picker>
                        </div>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 16px">
                    <el-button @click="applyFilters"
                               icon="el-icon-search"
                               style="background: #EF436D; height: 30px; "
                               type="danger">搜索
                    </el-button>
                    <el-button @click="resetFilters"
                               icon="el-icon-refresh"
                               style="background: #FFFFFF">重置
                    </el-button>
                </div>
            </div>
            <el-table
                    :data="pageAccountData"
                    class="custom-header detail-table"
                    style="width: 100%">
                <el-table-column
                        class="table-first-row"
                        label="分類"
                        prop="status"
                        width="140">
                    <template slot-scope="scope">
                        <div class="account-icon">
<!--                            <img :src="scope.row.status === 0 ? accountStatusIcon[0]: scope.row.status === 1 ? accountStatusIcon[1] : accountStatusIcon[2]"-->
<!--                                 alt="">-->
                            <img :src="accountStatusIcon"
                                 alt="">
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        label="訂單編號"
                        prop="order_no"
                        width="300">
                </el-table-column>
                <el-table-column
                        label="創建時間"
                        prop="createtime"
                        width="300">
                </el-table-column>
                <el-table-column
                        label="收入"
                        width="180">
                    <template slot-scope="scope">
                       <span :style="{color:scope.row.status === 1 ? '#EF436D' : '#22B573'}">
                           {{ scope.row.money }}
                       </span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="餘額"
                        prop="surplus_money"
                        width="180">
                </el-table-column>
                <el-table-column
                        label="收支類型"
                        prop="type"
                        width="180">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.status === 0 ? 'success' : scope.row.status === 1 ? 'danger' : 'warning'">
                            {{ scope.row.type === 1? '添加服務' :
                                scope.row.type === 2 ? '錢包儲值' :
                                    scope.row.type === 3 ? '集運訂單' : ''
                            }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        label="備註"
                        prop="remarks">
                </el-table-column>
            </el-table>
            <div class="footer absolute">
                <el-pagination
                        :current-page="currentPage"
                        :page-size="100"
                        :page-sizes="[5,10,20]"
                        :total="accountDetailList.length"
                        @current-change="handleCurrentChange"
                        @size-change="handleSizeChange"
                        layout="total, sizes, prev, pager, next, jumper">
                </el-pagination>
            </div>
        </div>
    </div>
</div>
<script>
    // 从隐藏字段获取数据
    var listDataInput = document.getElementById('listData');
    var walletData = [];

    try {
        walletData = JSON.parse(listDataInput.value).data;
        console.log(walletData, '+++++++++++++');
    } catch (e) {
        console.error('Error parsing JSON data:', e);
    }
    const app = new Vue({
        el: '#app',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            filterStatus: null, // 筛选状态
            filterDateRange: null, // 筛选日期范围
            filteredAccountData: [],
            accountStatusIcon: '__CDN__/assets/img/pc/new_index/parcel12.svg',
            accountDetailList: walletData,
            currentPage: 1,
            currentPageSize: 5,
            pageSize: [5, 10, 20, 50],
        },
        created(){
            this.filteredAccountData = [...this.accountDetailList];
        },
        computed:{
            uniqueTypes() {
                return [...new Set(this.accountDetailList.map(item => item.type))];
            },
            pageAccountData() {
                const start = (this.currentPage - 1) * this.currentPageSize;
                const end = start + this.currentPageSize;
                return this.filteredAccountData.slice(start, end);
            },
        },
        methods:{
            // 查询
            applyFilters() {
                this.filteredAccountData = this.accountDetailList.filter(item => {
                    // 类型筛选
                    const typeMatch = this.filterStatus !== null ? item.type === this.filterStatus : true;
                    // 日期筛选
                    let dateMatch = true;
                    if (this.filterDateRange && this.filterDateRange.length === 2) {
                        const startDate = new Date(this.filterDateRange[0]);
                        const endDate = new Date(this.filterDateRange[1]);
                        endDate.setHours(23, 59, 59, 999); // 包含结束日期的最后一刻 (h-m-s-ms)
                        const itemDate = new Date(item.createTime);
                        dateMatch = itemDate >= startDate && itemDate <= endDate;
                        console.log(item.createTime, dateMatch, ************)
                    }
                    return typeMatch && dateMatch;
                });
                this.currentPage = 1;
            },
            resetFilters() {
                this.filterStatus = null;
                this.filterDateRange = null;
                this.filteredAccountData = [...this.accountDetailList];
                this.currentPage = 1;
            },

            // 分頁
            handleCurrentChange(page) {
                this.currentPage = page;
            },
            handleSizeChange(val) {
                this.currentPageSize = val
                this.currentPage = 1
            },
        },
    })
</script>
<style>
    .detail-table th.el-table__cell:first-child {
        padding-left: 24px !important;
    }

    .detail-table td.el-table__cell:first-child {
        padding-left: 24px !important;
    }
</style>
