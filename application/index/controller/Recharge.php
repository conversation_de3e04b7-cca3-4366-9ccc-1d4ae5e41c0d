<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Exception;
use think\Validate;
use think\db;
use think\Cookie;
use think\Hook;

class Recharge extends Frontend
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    protected $layout = '';
    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
        if(!$this->auth->id){
            $this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
        }

        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
    }


    public function index(){

        if($this->request->isPost()){
            /**
             * 钱包充值
             */
            // if($this->auth->is_card == '0'){
            //     $this->error('身份證一次認證未開通');
            // }
            $money = $this->request->post('money');
            $pay_type = $this->request->post('pay_type');     // 目前支付方式，只设置银行
            $bank_id = $this->request->post('bank_id');

            /** 检查银行卡是否通过验证 */

            $bank_row = null;
            if( $pay_type==1 ){
                if( $bank_id ){
                    $bank_row = $this->query_record('user_bank', array('id'=>$bank_id, 'user_id'=>$this->auth->id, 'is_state'=>1));
                    if( !$bank_row ){
                        return json_encode(array('code'=>'1', "msg"=>"你選擇的銀行卡還未通過驗證"), JSON_UNESCAPED_UNICODE);
                    }
                }else{
                    return json_encode(array('code'=>'1', "msg"=>"未檢測到你綁定的銀行卡"), JSON_UNESCAPED_UNICODE);
                }
            }else{
                return json_encode(array('code'=>'1', "msg"=>"目前尚未開通其他支付方式"), JSON_UNESCAPED_UNICODE);
            }

            /**检查 生成订单前是否已经存在一条 银行转账的订单 */
            if( $pay_type==1 ){
                $row = $this->query_record('order_bank_matching', array('user_id'=>$this->auth->id, 'bank_id'=>$bank_id));
                if($row){
                    return json_encode(array("code"=>'1', 'msg'=>'您有一筆等待處理的银行转账訂單'), JSON_UNESCAPED_UNICODE);
                }
            }

            $data = array(
                    'order_no'      =>orderNo(5),
                    'user_id'       =>$this->auth->id,
                    'tb_money'      =>$money,
                    'actual_money'  =>$money - 0,               // 后期 加算 优惠
                    'rec_money'     =>0,
                    'bank_id'       =>$bank_id?$bank_id:0,
                    'bank_num'      =>$bank_row?$bank_row['account_six']:'',
                    'pay_type'      =>$pay_type,
                    'pay_status'    =>0,
                    'order_status'  =>0,
                    'createtime'    =>time(),
                    'updatetime'    =>time(),
                    'completetime'  =>0
            );

            Db::startTrans();
            try{
                $id = $this->getid_insert_record('recharge', $data);
                if( !$id ){
                    Db::rollback();
                    return json_encode(array("code"=>'1', 'msg'=>'創建充值訂單失敗'), JSON_UNESCAPED_UNICODE);
                }
                if( $pay_type == 1 ){
                    $_match = $this->order_bank_matching($bank_id, 'recharge', $id, $bank_row['account_six']);
                    if( $_match ){
                        Db::commit();
                        return json_encode(array("code"=>'0', 'msg'=>'生成订单'), JSON_UNESCAPED_UNICODE);                
                    }
                }
            }catch (Exception $e){
            }
            Db::rollback();
            return json_encode(array("code"=>'1', 'msg'=>'生成充值訂單失敗'), JSON_UNESCAPED_UNICODE);
        }
        // 返回用户绑定的银行卡
        $ubank = Db::name('user_bank')->alias('ub')
                    ->join('bank bk', 'ub.bank_id=bk.id')
                    ->field('ub.*, bk.name')
                    ->where('ub.user_id', $this->auth->id)
                    ->select();

        $this->view->assign('ubank', $ubank);
        return $this->view->fetch('wallet/recharge');
    }




    public function history(){

        // $map = array(
		// 	'user_id' => $this->auth->id,
		// );
        // $table = "recharge";
        // $list = Frontend::getOrderList($map, 5, $table);
        // $this->view->assign('list', $list);


        $rows = Db::name('recharge')->where('user_id', $this->auth->id)->order('id','desc')->limit('200')->select();
        $this->view->assign('rows', $rows);
        return $this->view->fetch('wallet/history');
    }




    public function details(){
        $type = $this->request->get('type');
        $begtime = $this->request->get('bt');
        $endtime = $this->request->get('et');

        $map = array(
			'user_id' => $this->auth->id,
		);
        if( isset($type) ){
            $map['type'] = $type;
        }

        if( isset($begtime) && isset($endtime) ){
            $bt = strtotime($begtime);
            $et = strtotime($endtime);
            $map['createtime'] =  array('between', array($bt, $et)) ;
        }
        $table = "wallet_log";
        $list = Frontend::getOrderList($map, 10, $table);

        $this->view->assign('list', $list);
        return $this->view->fetch('wallet/details');
    }



    public function addbank(){
        if($this->request->isPost()){

            $map = array(
                'user_id'   => $this->auth->id,
                'is_state'  => '1',
                'status'    => 'normal',
            );

            $bank_count = Db::name('user_bank')->where($map)->count();
            if( $bank_count < 3 ){
                $bank_id = $this->request->Post('bank');
                $bank_name = $this->request->Post('name');
                $bank_account = $this->request->Post('lastsix');
                $_map = array(
                    'user_id'       => $this->auth->id,
                    'account_six'   => $bank_account,
                );
                $_info = $this->query_record('user_bank', $_map, '*', array('id'=>'desc'));
                
                if(!empty($_info)){
                    if($_info['is_state'] == '1'){
                        // $this->error('銀行卡已經存在');
                        return json_encode(array('code'=>'1', "msg"=>"銀行卡已經存在"), JSON_UNESCAPED_UNICODE);
                    }
                }

                $rule = [
                    'bank_id'           => 'require',
                    'account_name'      => 'require|length:1,20',
                    'account_six'       => 'number|length:6',
                ];
                $msg = [
                    'bank_id.require'       => 'Bank required',
                    'account_name.require'  => 'Bank card name required',
                    'account_name.length'   => 'Account name cannot exceed 20 characters',
                    'account_six.length'    => 'The last six digits of the bank card must be six',
                ];
                $data = [
                    'bank_id'       => $bank_id,
                    'account_name'  => $bank_name,
                    'account_six'   => $bank_account,
                    'user_id'       => $this->auth->id,
                    'card_img'      => "",
                    'card_img_img'  => "",
                    'is_lock'       => '1',
                    'is_state'      => '1',
                    'createtime'    => time(),
                    'updatetime'    => time(),
                ];
                $validate = new Validate($rule, $msg);
                $result = $validate->check($data);
                if (!$result) {
                    //$this->error(__($validate->getError()));
                    return json_encode(array('code'=>'1', "msg"=>$validate->getError()), JSON_UNESCAPED_UNICODE);
                }else{
                    Db::startTrans();
                    $res = $this->getid_insert_record('user_bank', $data);
                    if($res)
                    {
                        Db::commit();
                        // if($type){
                        //     $this->success(__('Submit successfully'), url('daifu/select_bank'));
                        // }else{
                        //     $this->success(__('Submit successfully'), url('account/index'));
                        // }
                        $_content = $this->auth->username .'('. $this->auth->mobile .')'.'银行卡提交需要审核，请至后台进行审核';
                        return json_encode(array('code'=>'0', "msg"=>$_content), JSON_UNESCAPED_UNICODE);
                    }else{
                        Db::rollback();
                        return json_encode(array('code'=>'1', "msg"=>"添加銀行卡失敗"), JSON_UNESCAPED_UNICODE);
                    }
                }
            }else{
                return json_encode(array('code'=>'1', "msg"=>"银行卡已达上限"), JSON_UNESCAPED_UNICODE);
            }
        }

        $map = array(
            'user_id'   => $this->auth->id,
			'is_state'  => '1',
			'status'    => 'normal',
        );
        $bank_count = Db::name('user_bank')->where($map)->count();
		if( $bank_count >= 3 ){
			// 达到上限         看看后面怎么操作
            return json_encode(array('code'=>'1', "msg"=>"银行卡已达上限"), JSON_UNESCAPED_UNICODE);
            //$this->view->assign('num','1');
		}

        $rows = $this->query_records('bank', array('status'=>'normal'));
        $bank = $this->list_to_tree($rows);

        return json_encode(array("code"=>'0', 'bank'=>$bank, 'bank_count'=>$bank_count, 'is_card'=>1), JSON_UNESCAPED_UNICODE);


        // $this->view->assign('bank',$bank);
        // $this->view->assign('bank_count',$bank_count);
        // $this->view->assign('is_card',$this->auth->is_card_img);
        // return $this->view->fetch();
    }

}
