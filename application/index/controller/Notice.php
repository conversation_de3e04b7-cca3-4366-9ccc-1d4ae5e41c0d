<?php



namespace app\index\controller;

use app\common\controller\Frontend;
use think\Config;
use app\api\controller\LineWebhook;
use think\Db;


/**
 * 工具类
 */
class Notice extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    public function _initialize()
    {
        parent::_initialize();
        // $auth = $this->auth;
		// if(!$this->auth->id){
		// 	$this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		// }
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'), '/');
        }
    }


    public  function index(){
        // 只取平台公告
        $type = $this->request->param('type','1');

        // $name = $this->query_record('dict', array('id'=>$type), 'name');
        $list = $this->query_records('notice', array('type'=>$type), '*', array('id'=>'desc'));

        // $this->view->assign('name', $name);
        $this->view->assign('list', $list);
        return $this->view->fetch('');
    }


    public function details(){
		$id = request()->param('id');
		$map = array(
			'id' => $id,
		);
		$info = Db::name('notice')->field('type,title,content')->where($map)->find();
		/*上一篇*/
		$up_info = Db::name('notice')->field('id,type,title')
                                            ->where('id',$id-1)
                                            ->where('type', $info['type'])
                                            ->limit(1)
                                            ->find();
		/*下一篇*/
		$down_info = Db::name('notice')->field('id,type,title')
                                            ->where('id',$id+1)
                                            ->where('type', $info['type'])
                                            ->limit(1)
                                            ->find();
		
        
        $this->view->assign('info',$info);
		$this->view->assign('up_info',$up_info);
		$this->view->assign('down_info',$down_info);
        return $this->view->fetch('content');
	}






    public function line_test(){
        $line = new LineWebhook();

        $order_data = [
            'create_time' => '2025-07-03 10:36:00',
            'order_no' => 'Y8051237354912687',
            // 'img' => 'https://jiyun.frp.nxun.cn:10021/uploads/1.png',
            'good_name' => '任天堂Switch2 主機switch 2 瑪麗歐賽車世界 主機組2代原廠公司貨 NS2 游戲主機 Q哥電玩',
            'good_price' => '￥3999.9',
            'good_weight' => '2.9KG',
            'order_total' => 'NT$ 18,045',
            'pay_status' => '待付款',
            'redirect_url' => 'https://linecorp.com'
        ];

        $to = "U744ed97aed973c16746f8ff087bbc2d0";
        $line->order($order_data, $to);

        return  "发送完成";
    }


    
}
