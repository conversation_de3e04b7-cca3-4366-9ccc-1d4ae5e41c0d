<?php


namespace app\index\controller;

use app\admin\library\Auth;
use app\common\controller\Frontend;
use app\common\sdk\Alibaba;
use think\Exception;
use think\Db;
use think\Config;
use think\Cookie;
use think\Hook;




class Transport extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];


    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
        if(!$this->auth->id){
			// $this->error('帳戶未登錄，請先登錄',url('index/login/login'));
            $this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		}
        // Hook::add('user_logout_successed', function ($user) use ($auth) {
        //     Cookie::delete('uid');
        //     Cookie::delete('token');
        // });
    }


    public function index()
    {
        if( $this->request->isPost() ){
            $param = $this->request->param();

            $data = $param['data'];                                 // 上传一个ok字段，当ok=1时，不再返回信息提示用户是否要
            Db::startTrans();
            try {
                for($i=0;$i<count($data);$i++){
                    $waybill =      $data[$i]['deliveryId'];
                    $wuliu_id =     $data[$i]['wid'];               //  物流ID
                    $goods_name =   $data[$i]['goodsName'];
                    $goods_type =   $data[$i]['goodsType'];                
                    $goods_count =  $data[$i]['goodsCount'];
                    $remarks =      $data[$i]['remarks'];
                    $wh_id  =       $data[$i]['deliveryType'];
                    
                    $page_row = $this->query_record('package', array('waybill'=>$waybill, 'wuliu_id'=>$wuliu_id));
                    if( !$page_row ){
                        $map = [
                            'user_id'       =>$this->auth->id,
                            'entrust_no'    =>orderNo('1'), 
                            'waybill'       =>$waybill,
                            'wuliu_id'      =>$wuliu_id,
                            'goods_name'    =>$goods_name,
                            'goodstype_id'  =>$goods_type,
                            'num'           =>$goods_count,
                            'transport'     =>0,
                            'wh_id'         =>$wh_id,
                            'uremarks'      =>$remarks,
                            'createtime' => time(),
                            'updatetime' => time(),
                        ];

                        $pid = $this->getid_insert_record('package', $map);
                        if( $pid <= 0 ){
                            Db::rollback();
                            return json_encode(array('code'=>'1', "msg"=>'添加包裹失敗'), JSON_UNESCAPED_UNICODE);
                        }

                        $com = $this->get_field_value('wuliu', array('id'=>$wuliu_id), 'alias');
                        if( strlen($com) <= 0 ){
                            Db::rollback();
                            return json_encode(array('code'=>'1', "msg"=>"快遞編號不正確"), JSON_UNESCAPED_UNICODE);
                        }

                        $kd_row = $this->query_record('kuaidi_query', array('user_id'=>$this->auth->id, 'num'=>$waybill));
                        $err_count = 5;
                        if( $kd_row && $kd_row['ct'] >= $err_count && $param['ok'] == "0" ){
                            Db::rollback();
                            return json_encode(array('code'=>'2', "msg"=>'超過'.$err_count.'次查詢失敗後不再查詢，請確實是否繼續提交'), JSON_UNESCAPED_UNICODE);
                        }

                        // 如果物流是顺丰，需要寄存人电话
                        $phone = "";
                        if( $com == 'shunfeng' ){
                            $contact_row = $this->query_record('contact', array('wh_id'=>$wh_id));
                            if(!$contact_row){
                                Db::rollback();
                                return json_encode(array('code'=>'1', "msg"=>"倉庫還未添加默認聯絡人"), JSON_UNESCAPED_UNICODE);
                            }
                            $phone = $contact_row['mobile'];
                        }

                        // // 根据包裹状态来判断用什么物流查询
                        $wuliu_data = $this->kuaidi_hd_query($this->auth->id,$pid, $com, $waybill, $phone);    // 查不到也不能不让提交
                        if( $param['ok'] == "0" ){
                            if( $wuliu_data['code'] == '1' ){
                                Db::rollback();
                                if( !$kd_row ){
                                    $this->getid_insert_record('kuaidi_query', array('user_id'=>$this->auth->id, 'num'=>$waybill, 'ct'=>1, 'updatetime'=>time(),'createtime'=>time()));
                                }else{
                                    $this->add_field_value('kuaidi_query', array('user_id'=>$this->auth->id, 'num'=>$waybill), 'ct', 1);
                                }
                                return json_encode(array('code'=>'2', "msg"=>$wuliu_data['message'] . '-請確認快遞與運單號是否輸入正確'), JSON_UNESCAPED_UNICODE);
                            }
                            $this->update_record('kuaidi_query', array('com'=>$com, 'num'=>$waybill), array('ct'=>0, 'user_id'=>$this->auth->id));
                        }else{
                            $this->update_record('kuaidi_query', array('user_id'=>$this->auth->id, 'num'=>$waybill), array('pid'=>$pid, 'com'=>$com, 'ct'=>0, 'updatetime'=>time()));
                        }
                    }else{
                        Db::rollback();
                        return json_encode(array('code'=>'1', "msg"=>'運單號碼重複'), JSON_UNESCAPED_UNICODE);
                    }
                }
                Db::commit();
                return json_encode(array("code"=>'0', "msg"=>"添加成功", JSON_UNESCAPED_UNICODE ));
            }catch (Exception $e) {
                Db::rollback();
                return json_encode(array('code'=>'1', "msg"=>$e->getMessage()), JSON_UNESCAPED_UNICODE);
            }
        }
        
        $contactes = Db::name('contact')->alias('c')
                                        ->join('warehouse_transport wt', 'c.wh_id=wt.wh_id')
                                        ->join('warehouse w', 'c.wh_id=w.id')
                                        ->join('dict d', 'wt.type=d.id')
                                        ->field('c.id, c.username, c.mobile, c.title, c.wh_id, w.code, w.title as wtitle, w.addr, wt.type, d.name, d.value')
                                        ->select();
        
        $gt = $this->query_records('goodstype', array('status'=>0));

        $dict = $this->query_records('dict', array('status'=>0));

        $tp_arr = $this->list_to_list($dict, "type", "transport");

        $id_arr = array_column($tp_arr, "id");

        $wh_rows = Db::name('warehouse_transport')->alias('wt')
                                        ->join('warehouse w', 'wt.wh_id=w.id')
                                        ->field('wt.type, w.id, w.title')
                                        ->select();

        $wh_data = $this->list_to_group_byid($tp_arr, $wh_rows, 'type');

        $wuliu_row = $this->query_records('wuliu', array('type'=>0), 'id, name');

        $this->view->assign('rows', $contactes);         // 联络人
        $this->view->assign('kd', $wuliu_row);        // 快递
        $this->view->assign('tp', $wh_data);        // 运输方式+仓库
        $this->view->assign('gt', $gt);             // 货物类型
        return $this->view->fetch();
    }


    /**
     * 我的包裹  
     */
    public function myParcel(){
        if( $this->request->isPost() ){
            $status = $this->request->post('status');
            $rows = Db::name('package')->alias('p')
                                            ->join('addservice a', 'p.addser_id=a.id', 'LEFT')
                                            ->join('wuliu wl', 'p.wuliu_id=wl.id')
                                            ->field('p.*, a.addser_type_id, wl.sname, wl.icon')
                                            ->where('p.user_id', $this->auth->id)
                                            ->where('p.status', $status)
                                            ->select();

            $cvalues = array_column($rows, "id");
            $id_list = implode(',', $cvalues);
            $addservice_detail_rows = $this->query_records('addservice_detail', array('pid'=>array('in', $id_list)));
            $addservice_detail_data = $this->list_to_group($cvalues, $addservice_detail_rows, 'pid');

            $kuaidi_logs = Db::name('kuaidi_query')->alias('kq')
                                            ->join('kuaidi_log kl', 'kq.id=kl.kid')
                                            ->field('kq.pid,kl.*')
                                            ->where('kq.pid', "in", $id_list)
                                            ->order('kl.id desc')
                                            ->select();


            $kd_log = $this->list_key_to_max($kuaidi_logs, $cvalues, 'pid', 'id');
            $rows = $this->list_add_array_record($rows, array('name'=>'pid', 'data'=>$kd_log), 'kd_log');
            $rows = $this->list_add_array_record($rows, array('name'=>'pid', 'data'=>$addservice_detail_data), 'ser_detail');

            return json_encode(array('code'=>'0', 'list'=>$rows), JSON_UNESCAPED_UNICODE);
        }



        $rows = Db::name('package')->alias('p')
                                    ->join('addservice a', 'p.addser_id=a.id', 'LEFT')
                                    ->join('wuliu wl', 'p.wuliu_id=wl.id')
                                    ->field('p.*, a.addser_type_id, wl.sname, wl.icon')
                                    ->where('p.user_id', $this->auth->id)
                                    ->select();

        $cvalues = array_column($rows, "id");
        $id_list = implode(',', $cvalues);

        $kuaidi_logs = Db::name('kuaidi_query')->alias('kq')
                                    ->join('kuaidi_log kl', 'kq.id=kl.kid')
                                    ->field('kq.pid,kl.*')
                                    ->where('kq.pid', "in", $id_list)
                                    ->order('kl.id desc')
                                    ->select();
        
        $addservice_detail_rows = $this->query_records('addservice_detail', array('pid'=>array('in', $id_list)));
        $addservice_detail_data = $this->list_to_group($cvalues, $addservice_detail_rows, 'pid');

        $kd_log = $this->list_key_to_max($kuaidi_logs, $cvalues, 'pid', 'id');
        $rows = $this->list_add_array_record($rows, array('name'=>'pid', 'data'=>$kd_log), 'kd_log');
        $rows = $this->list_add_array_record($rows, array('name'=>'pid', 'data'=>$addservice_detail_data), 'ser_detail');

        $addser = $this->addService();

        $dict = $this->query_records('dict', array('status'=>0));
        $tp_arr = $this->list_to_list($dict, "type", "transport");

        $wh_tp = Db::name('warehouse_transport')->alias('wt')
                                    ->join('dict d', 'wt.type=d.id')
                                    ->field('wt.id, wt.wh_id, wt.type, d.name')
                                    ->select();

        $wh_rows = $this->query_records('warehouse', array('status'=>0));
        $wh_data = $this->list_to_group_byid($wh_rows, $wh_tp, 'wh_id');


        $line_bang = $this->line_bang();
        $line_bot = $this->line_bot();
        if($line_bang && (!$line_bot)){
            $line_name = $this->line_field('line_username');
            $this->view->assign('line_name', $line_name);    
        }
        $this->view->assign('line_bang', $line_bang);
        $this->view->assign('line_bot', $line_bot);
        $this->view->assign('wh', $wh_data);
        $this->view->assign('list', $rows);
        $this->view->assign('addser', $addser);
        $this->view->assign('tp', $tp_arr);        // 运输方式
        return $this->view->fetch('transport/myParcel/index');
    }


    /**
     * 查询包裹         
     */
    public function searchParcel(){
        if($this->request->isPost()){
            $wh = $this->request->post('wh');
            $waybill = $this->request->post('wb');
            $status = $this->request->post('status');

            if( !empty($wh) && !empty($waybill)  ){
                $rows = Db::name('package')->alias("p")
                        ->join('wuliu wl', 'p.wuliu_id=wl.id')
                        ->field('p.*, wl.sname, wl.icon')
                        ->where('p.wh_id', $wh)
                        ->where('p.user_id', $this->auth->id)
                        ->where('p.waybill', $waybill)
                        ->where('p.status', $status)
                        ->select();
            }else if( !empty($wh) && empty($waybill) ){
                $rows = Db::name('package')->alias("p")
                        ->join('wuliu wl', 'p.wuliu_id=wl.id')
                        ->field('p.*, wl.sname, wl.icon')
                        ->where('p.wh_id', $wh)
                        ->where('p.user_id', $this->auth->id)
                        ->where('p.status', $status)
                        ->select();
            }else if( empty($wh) && !empty($waybill) ){
                $rows = Db::name('package')->alias("p")
                        ->join('wuliu wl', 'p.wuliu_id=wl.id')
                        ->field('p.*, wl.sname, wl.icon')
                        ->where('p.user_id', $this->auth->id)
                        ->where('p.waybill', $waybill)
                        ->where('p.status', $status)
                        ->select();
            }


            $cvalues = array_column($rows, "id");
            $id_list = implode(',', $cvalues);
            $kuaidi_logs = Db::name('kuaidi_query')->alias('kq')
                        ->join('kuaidi_log kl', 'kq.id=kl.kid')
                        ->field('kq.pid,kl.*')
                        ->where('kq.pid', "in", $id_list)
                        ->order('kl.id desc')
                        ->select();
            
            $addservice_detail_rows = $this->query_records('addservice_detail', array('pid'=>array('in', $id_list)));
            $addservice_detail_data = $this->list_to_group($cvalues, $addservice_detail_rows, 'pid');

            $kd_log = $this->list_key_to_max($kuaidi_logs, $cvalues, 'pid', 'id');
            $rows = $this->list_add_array_record($rows, array('name'=>'pid', 'data'=>$kd_log), 'kd_log');
            $rows = $this->list_add_array_record($rows, array('name'=>'pid', 'data'=>$addservice_detail_data), 'ser_detail');
              
            return json_encode(array('code'=>'0', 'list'=>$rows), JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 给包裹添加附件服务
     * @return bool|string
     */
    public function addService(){
        if($this->request->isPost()){
            $post_data = $this->request->param();
            $pg_id = $post_data['pid'];
            $user_id = $this->auth->id;
            $row = $this->query_record('package', array('id'=>$pg_id, 'user_id'=>$user_id));
            if($row){
                // 不知道有没有抵扣金  ？？
                try{

                    $user = $this->query_record('user', array('id'=>$this->auth->id));
                    if(!$user['nouse']){
                        if( !($this->auth->verifyPayPwd($this->request->post('pwd'))) )
                        {
                            return json_encode(array('code'=>'1', "msg"=>"密碼錯誤"), JSON_UNESCAPED_UNICODE);
                        }
                    }

                    $sid = $post_data['sid'];
                    $ser_list = implode(',', $sid);
                    $total_money = $post_data['money'];
                    $data = array(
                        'order_no'            =>orderNo('2'), 
                        'pg_id'             =>$pg_id,
                        'addser_type_id'    =>$ser_list,
                        'tb_money'          =>$total_money,
                        'bal_money'         =>0,
                        'actual_money'      =>$total_money,                 //  以后可能会有抵扣金
                        'pay_type'          =>0,
                        'order_status'      =>0,
                        'createtime'        =>time()
                    );

                    $user = $this->query_record('user', array('id'=>$this->auth->id));
                    if( $user['money'] < $total_money ){
                        return json_encode(array("code"=>"1", "msg"=>"余額不足，請先充值"), JSON_UNESCAPED_UNICODE);
                    }

                    Db::startTrans();
                    $data['pay_type'] = 1;
                    $id = $this->getid_insert_record('addservice', $data);
                    if( $id > 0 ){
                        $addser_list = $this->get_field_value('package', array('id'=>$pg_id), 'addser_id');
                        $addser_list = empty($addser_list) ? $id : $addser_list .','. $id;
                        $this->update_record('package', array('id'=>$pg_id), array('addser_id'=>$addser_list));
                        $list = [];
                        for($i=0;$i<count($sid);$i++){
                            $list[] = array("no"=>orderNo('3'),"pid"=>$pg_id,"aid"=>$id,"at_id"=>$sid[$i], 'updatetime'=>time(), 'createtime'=>time());
                        }
                        $this->insert_array_record('addservice_detail', $list);
                        $this->money_log(1, $total_money, $id, 'addservice', "购买包裹服务");
                        Db::commit();
                        return json_encode(array("code"=>"0", "msg"=>"添加服務成功"), JSON_UNESCAPED_UNICODE);    
                    }
                    Db::rollback();
                }catch (Exception $e)
                {
                    Db::rollback();
                    // return json_encode(array("code"=>"1", "msg"=>$e->getMessage()), JSON_UNESCAPED_UNICODE);
                }
            }
            return json_encode(array("code"=>"1", "msg"=>"添加服務失敗"), JSON_UNESCAPED_UNICODE);
        }

        $rows = Db::name('addservice_type')->alias('a')
                            ->join('dict d', 'a.dict_id=d.id')
                            ->field('a.id, a.title, a.amount, a.descr, a.dict_id, d.name')
                            ->order('a.id ASC')
                            ->select();

        $contentes = $this->query_records('addservice_content', array('status'=>0), 'id cid,aid,title ctitle,content');
        $data = $this->list_to_group_byid($rows, $contentes, 'aid');
        return json_encode(array("code"=>'0', 'data'=>$data), JSON_UNESCAPED_UNICODE);
    }



    public function get_service(){

        if($this->request->isPost()){
            // $pid = $this->request->post('pid');
            $post_data = $this->request->param();
            $pid_arr = $post_data['pid'];
            $pid_str = implode(',', $pid_arr);

            $rows = Db::name('addservice_detail')->alias('ad')
                            ->join('addservice_type at', 'ad.at_id=at.id')
                            ->where('ad.pid', "in", $pid_str)
                            ->field('ad.*, at.title, at.amount, at.descr')
                            ->select();

            $log_rows = $this->query_records('addservice_detail_log', array('pid'=>array('in', $pid_str)), 'id, ad_id, content, img, remarks, createtime');
            $list = $this->list_to_group_byid($rows, $log_rows, 'ad_id');
            return json_encode(array("code"=>'0', 'data'=>$list), JSON_UNESCAPED_UNICODE);
        }
    }


    public function get_detail_log(){
        if($this->request->isPost()){
            $ad = $this->request->post('id');
            $rows = $this->query_records('addservice_detail_log', array('ad_id'=>$ad));
            return json_encode(array("code"=>'0', 'data'=>$rows), JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 设置订单备注
     * @return bool|string
     */
    public function setOremarks(){
        if($this->request->isPost()){
            $pg_id = $this->request->post('pid');
            $row = $this->query_record('package', array('user_id'=>$this->auth->id, 'id'=>$pg_id));
            if( $row ){
                $rk = $this->request->post('rk');
                $this->update_record('package', array('id'=>$pg_id), array('oremarks'=>$rk));
                return json_encode(array("code"=>'0', 'msg'=>'設置成功'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array("code"=>'1', 'msg'=>'訂單不存在'), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 用户设置拒收的包裹
     * @return string
     */
    public function refuse(){
        if($this->request->isPost()){
            $pg_id = $this->request->post('pid');
            $row = $this->query_record('package', array('user_id'=>$this->auth->id, 'id'=>$pg_id));
            if( $row ){
                $ref = $this->request->post('rf');
                $this->update_record('package', array('id'=>$pg_id), array('refuse'=>$ref));
                return json_encode(array("code"=>'0', 'msg'=>'設置成功'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array("code"=>'1', 'msg'=>'訂單不存在'), JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 关联运单号
     * @return bool|string
     */
    public  function setWaybill() {
        if($this->request->isPost()){
            $pg_id = $this->request->post('pid');
            $bill = $this->request->post('bill');
            $row = $this->query_record('package', array('id'=>$pg_id,'user_id'=>$this->auth->id));
            if( $row ){
                $this->update_record('package', array('id'=>$pg_id), array('waybill'=>$bill));
                return json_encode(array("code"=>'0', 'msg'=>'設置成功'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array("code"=>'1', 'msg'=>'設置失敗'), JSON_UNESCAPED_UNICODE);
        }
    }



    public function sendon(){
        // if($this->request->isPost()){
        //     $type = $this->request->post('ty');
        //     $pg_id = $this->request->post('pid');
        //     if($type == 1){
        //         $name = $this->request->post('name');
        //         $addr = $this->request->post('addr');
        //         $mobile = $this->request->post('mob');
        //         $rk = $this->request->post('rk');
        //         $row = Db::name('package')->where('id', $pg_id)->where('user_id', $this->auth->id)->find();
        //         if( !$row ){
        //             return json_encode(array("code"=>'1', 'msg'=>'未找到訂單信息'), JSON_UNESCAPED_UNICODE);
        //         }
        //         $data = array( 'order_no'   =>  $row['waybill'],
        //                         'user_id'   =>  $this->auth->id,
        //                         'type'      =>  $type,
        //                         'rname'     =>  $name,
        //                         'raddr'     =>  $addr,
        //                         'rmobile'   =>  $mobile,
        //                         ''

        //         );
        //     }else if($type == 2){



        //     }
        // }


    }


    public function order(){
        $type = $this->request->param('type');
        if($this->request->isPost()){
            $tb_ali = $type? "ad." : "jy." ;
            $no = $this->request->post('no');
            $price = $this->request->post('price');
            $pay_status = $this->request->post('status');
            $time = $this->request->post('time');

            $map = array();
            
            if( !empty($no) ){
                if($type == 0){
                    $map[$tb_ali . 'order_no'] = $no;
                }else if($type == 1){
                    $map['p.' . 'waybill'] = $no;
                }
            }

            if( !empty($price) ){
                
                if($type == 0){
                    $map[$tb_ali . 'tb_money'] = $price;
                }else if($type == 1){
                    $map['at.' . 'amount'] = $price;
                }
            }
            if( !empty($pay_status) ){
                if($type == 0){
                    $map[$tb_ali . 'pay_status'] = $pay_status;
                }else if($type == 1){
                    $map[$tb_ali . 'status'] = $pay_status;
                }

            }
            if( !empty($time) ){
                $time_arr = explode("-", $time);
                $map[$tb_ali . 'createtime'] = array('between', array($time_arr[0], $time_arr[1]));
            }

            if( $type == 0 ){
                $map[$tb_ali . 'user_id'] = $this->auth->id;
                $list = Db::name('jyorder')->alias("jy")
                                ->join("user u", "jy.user_id=u.id")
                                ->where($map)
                                ->field('jy.*, u.username, u.mobile')
                                ->select();

                if(count($list) <= 0){
                    return json_encode(array('code'=>1,'msg'=>'查詢不到任何記錄'), JSON_UNESCAPED_UNICODE);    
                }
                return json_encode(array('code'=>0,'msg'=>'查詢成功','data'=>$list), JSON_UNESCAPED_UNICODE);

            }else if( $type == 1 ){
                $prows = $this->query_records('package', array('user_id'=>$this->auth->id));
                $cvalues = array_column($prows, "id");
                $id_list = implode(',', $cvalues);

                $map[$tb_ali . 'pid'] = array('in', $id_list);
                $list = Db::name('addservice_detail')->alias("ad")
                                        ->join("package p", "ad.pid=p.id")
                                        ->join("addservice a", "ad.aid=a.id")
                                        ->join("addservice_type at", "ad.at_id=at.id")
                                        ->where($map)
                                        ->field("ad.id, ad.pid, p.waybill, p.wbill_name, a.order_no, a.createtime, at.title, at.amount, ad.status")
                                        ->order("at.id DESC")
                                        ->select();

                if(count($list) <= 0){
                    return json_encode(array('code'=>1,'msg'=>'查詢不到任何記錄'), JSON_UNESCAPED_UNICODE);    
                }
                return json_encode(array('code'=>0,'msg'=>'查詢成功','data'=>$list), JSON_UNESCAPED_UNICODE);
            }
        }

        $list = "";
        if( $type == 0 ){
            $list = Db::name('jyorder')->alias("jy")
                                ->join("user u", "jy.user_id=u.id")
                                ->where('jy.user_id', $this->auth->id)
                                ->field('jy.*, u.username, u.mobile')
                                ->select();

        }else if( $type == 1 ) {
            $prows = $this->query_records('package', array('user_id'=>$this->auth->id));
            $cvalues = array_column($prows, "id");
            $id_list = implode(',', $cvalues);

            $list = Db::name('addservice_detail')->alias("ad")
                                    ->join("package p", "ad.pid=p.id")
                                    ->join("addservice a", "ad.aid=a.id")
                                    ->join("addservice_type at", "ad.at_id=at.id")
                                    ->where("ad.pid", "in", $id_list)
                                    ->field("ad.id, ad.pid, p.waybill, p.wbill_name, a.order_no, a.createtime, at.title, at.amount, ad.status")
                                    ->order("at.id DESC")
                                    ->select();
        }

        $this->view->assign('list', $list);
        return $this->view->fetch('transport/order/index');
    }

    
    public function imp(){



        return $this->view->fetch('transport/imp/index');
    }


    public function recipient(){

        if($this->request->isPost()){
            $sch = $this->request->post('sch');
            // $list = Db::name('address')->alias('ad')
            //                                 ->join("city ct", "ad.city=ct.id")
            //                                 ->join("district dt","ad.district=dt.id")
            //                                 ->join("dict dc","ad.label=dc.id")
            //                                 ->where(["ad.user_id"=> $this->auth->id])
            //                                 ->whereOr(['ad.name'=>$sch, 'ad.mobile' =>$sch])
            //                                 ->field("ad.id, ad.name, dc.name label, ad.mobile, ct.name city, dt.name district, ad.detail")
            //                                 ->order("ad.id desc")
            //                                 ->select();
            
            $list = Db::name('address')->alias('ad')
                                            ->join("city ct", "ad.city=ct.id")
                                            ->join("district dt","ad.district=dt.id")
                                            ->join("dict dc","ad.label=dc.id")
                                            ->whereOr(['ad.name'=>$sch, 'ad.mobile' =>$sch])
                                            ->where(["ad.user_id"=> $this->auth->id])
                                            ->field("ad.id, ad.name, dc.name label, ad.mobile, ct.name city, dt.name district, ad.detail")
                                            ->order("ad.id desc")
                                            ->select();

           
            
            if(count($list) <= 0){
                return json_encode(array('code'=>1,'msg'=>'查詢不到任何記錄'), JSON_UNESCAPED_UNICODE);    
            }
            return json_encode(array('code'=>0,'msg'=>'查詢成功','data'=>$list), JSON_UNESCAPED_UNICODE);
        }

        $list = Db::name('address')->alias('ad')
                            ->join("city ct", "ad.city=ct.id")
                            ->join("district dt","ad.district=dt.id")
                            ->join("dict dc","ad.label=dc.id")
                            ->where("ad.user_id", $this->auth->id)
                            ->field("ad.id, ad.name, dc.name label, ad.mobile, ct.name city, dt.name district, ad.detail")
                            ->order("ad.id desc")
                            ->select();

        $this->view->assign('list',$list);
        return $this->view->fetch('transport/recipient/index');
    }


    public function exp(){
        $rows = Db::name('jiyun_map')->where('type', ">", 0)->group('type, weight')->select();
        $rows_list = Db::name('jiyun_map')->where('type', 0)->order('weight ASC')->select();
        $this->view->assign('list_tp', $rows);
        $this->view->assign('list', $rows_list);
        return $this->view->fetch('transport/exp/explain');


        // return $this->view->fetch('transport/exp/index');
    }

    public function exp_content(){
		$id = request()->param('id');
		$map = array(
			'id' => $id,
		);
		$info = Db::name('jiyun_map')->field('type,title,content')->where($map)->find();
		/*上一篇*/
		$up_info = Db::name('jiyun_map')->field('id,type,title')
                                            ->where('id',$id-1)
                                            ->where('type', $info['type'])
                                            ->limit(1)
                                            ->find();
		/*下一篇*/
		$down_info = Db::name('jiyun_map')->field('id,type,title')
                                            ->where('id',$id+1)
                                            ->where('type', $info['type'])
                                            ->limit(1)
                                            ->find();
		
        
        $this->view->assign('info',$info);
		$this->view->assign('up_info',$up_info);
		$this->view->assign('down_info',$down_info);
        return $this->view->fetch('transport/exp/content');
	}


    public function addr(){
        $rows = Db::name('warehouse')->alias('w')
                            ->join('warehouse_transport wt', 'wt.wh_id=w.id')
                            ->join('dict d', 'wt.type=d.id')
                            ->field('w.id,w.code,w.title as wtitle, w.addr, wt.type, d.name, d.value')
                            ->select();

        $this->view->assign('rows', $rows);
        return $this->view->fetch('transport/addr/index');
    }


    public function addApplicant(){
        if($this->request->isPost()){
            $type = $this->request->post('ty');
            $validate = false;
            if( $type == 0 ){
                $username = $this->request->post('uname');
                $mobile = $this->request->post('mb');
                $card = $this->request->post('card');

                // EZWAY  验证个人
                $validate = true;

            }else if( $type == 1 ){
                $code = $this->request->post('code');
                $mobile = $this->request->post('mb');

                // EZWAY  验证公司
                $validate = true;

            }
            if($validate == false){
                return json_encode(array("code"=>'1', 'msg'=>'EZWAY認證失敗'), JSON_UNESCAPED_UNICODE);
            }
            $data = array(
                'type'      =>$type,
                'user_id'   =>$this->auth->id,
                'username'  =>$type==0?$username:'',
                'mobile'    =>$mobile,
                'card'      =>$type==0?$card:'',
                'co_code'   =>$type==0?'':$code,
                'status'    =>$validate?1:0,
                'createtime' =>time(),
                'updatetime' =>time()
            );

            $this->getid_insert_record('applicant', $data);
            return json_encode(array("code"=>'0', 'msg'=>'EZWAY認證成功'), JSON_UNESCAPED_UNICODE);
        }

        $rows = $this->query_records('applicant', array('user_id'=>$this->auth->id));
        return json_encode(array("code"=>'0', 'data'=>$rows), JSON_UNESCAPED_UNICODE);
        
    }

    public function applicant(){
        // if($this->request->isPost()){
        //     $type = $this->request->post('ty');
        //     $validate = false;
        //     if( $type == 0 ){
        //         $username = $this->request->post('uname');
        //         $mobile = $this->request->post('mb');
        //         $card = $this->request->post('card');

        //         // EZWAY  验证个人                      <---------------------------------------
        //         $validate = true;

        //     }else if( $type == 1 ){
        //         $code = $this->request->post('code');
        //         $mobile = $this->request->post('mb');

        //         // EZWAY  验证公司
        //         $validate = true;

        //     }
        //     if($validate == false){
        //         return json_encode(array("code"=>'1', 'msg'=>'EZWAY認證失敗'), JSON_UNESCAPED_UNICODE);
        //     }
        //     $data = array(
        //         'type'      =>$type,
        //         'user_id'   =>$this->auth->id,
        //         'username'  =>$type==0?$username:'',
        //         'mobile'    =>$mobile,
        //         'card'      =>$type==0?$card:'',
        //         'co_code'   =>$type==0?'':$code,
        //         'status'    =>$validate?1:0,
        //         'createtime' =>time(),
        //         'updatetime' =>time()
        //     );
        //     $this->getid_insert_record('applicant', $data);
        //     return json_encode(array("code"=>'0', 'msg'=>'EZWAY認證成功'), JSON_UNESCAPED_UNICODE);
        // }

        $rows = $this->query_records('applicant', array('user_id'=>$this->auth->id));
        $this->view->assign('rows', $rows);
        return $this->view->fetch('transport/applicant/index');
    }



    /**
     * 创建集运订单
     * @return bool|string
     */
    public function corder(){
        if($this->request->isPost()){
            /**
             * 限制用户创建多个订单
             */
            $check_res = $this->query_record('jyorder', array('user_id'=>$this->auth->id,'pay_status'=>array('<=', 1)));
            if($check_res){
                return json_encode(array("code"=>'1', 'msg'=>'你還有未付款的訂單，請先去完成支付'), JSON_UNESCAPED_UNICODE);
            }

            /**
             * 判读选择银行支付前，数据库是否还有一笔银行支付的订单
             */
            $post_data = $this->request->param();
            // if( isset($post_data['type']) && $post_data['type']==1 ){
            //     $row = Db::name('order_bank_matching')
            //             ->where('user_id', $this->auth->id)
            //             ->where('bank_id', $post_data['bank_id'])
            //             ->find();
            //     if($row){
            //         return json_encode(array("code"=>'1', 'msg'=>'您有一筆等待處理的银行转账訂單 (后期设置跳转)'), JSON_UNESCAPED_UNICODE);
            //     }
            // }
            $pg_id = $post_data['pid'];
            // 1. 检查包裹是不是在同一个仓库
            $pg_str = implode(',', $pg_id);
            $my_page = $this->query_records('package', array('user_id'=>$this->auth->id,'id'=>array('in', $pg_str)));

            $wh_col = array_column($my_page, 'wh_id');
            $unique_arr = array_unique($wh_col);
            $count_page = count($unique_arr);
            if( $count_page < 1 ){
                //  现在还不确定返回  json数据， 还是跳转页面   ？？？？？？？？？？？
                return json_encode(array("code"=>'1', 'msg'=>'包裹驗證錯誤'), JSON_UNESCAPED_UNICODE);
            }else if( $count_page > 1 ){
                return json_encode(array("code"=>'1', 'msg'=>'不同倉庫包裹不能壹起生成訂單'), JSON_UNESCAPED_UNICODE);
            }
            // 还有个type  --> 支付方式 1 银行  2 钱包
            // bank    银行后六位     
            $tw_type = $post_data['tw'];            //取货方式  物流或者自取，传入tw_logistics表ID
            $addr_id = $post_data['addr'];          //收货地址ID
            $appl_id = $post_data['applicant'];     //申报人ID
            $count_tp_id = $post_data['tp'];        //计算运输方式ID

            $add_row = $this->query_record('address', array('id'=>$addr_id));
            if( !$add_row ){
                return json_encode(array("code"=>'1', 'msg'=>'收貨地址錯誤'), JSON_UNESCAPED_UNICODE);
            }

            //查询 申报人  是否已经通过  $appl_id，  未通过的不准许下单
            
            if( !$this->query_record('applicant', array('id'=>$appl_id,'status'=>1)) ){
                return json_encode(array("code"=>'1', 'msg'=>'未通過的申報人，不能下單'), JSON_UNESCAPED_UNICODE);
            }

            $count_money = array();
            $tw_row = $this->query_record('tw_logistics', array('id'=>$tw_type));
            
            // if( $tw_row['type'] == 20 ){
            //     // 宅配
            //     if( $tw_row['name'] == "新竹"){
            //         $count_money['delivery'] = count($my_page) * $tw_row['base_money'];        //  派件费基础收费
            //         $count_money['sup_delivery'] = 0;
            //         for($i=0;$i<count($my_page);$i++){
            //             $item = ceil( ($my_page[$i]['length'] * $my_page[$i]['width'] * $my_page[$i]['height'])/$tw_row['count_value'] );
            //             if( $item > $tw_row['limit_value'] ){
            //                 $count_money['sup_delivery'] += ($item - $tw_row['limit_value']) * $tw_row['over_price'];
            //             }
            //         }
            //     }else{
            //         return json_encode(array("code"=>'1', 'msg'=>'目前還未開通其他物流'), JSON_UNESCAPED_UNICODE);
            //     }
                
            // }else if($tw_row['type'] == 21) {
            //     // 超商自取    核对标准         
            //     for($i=0;$i<count($my_page);$i++){
            //         if( ceil($my_page[$i]['scale']) > $tw_row['scale'] ){
            //             return json_encode(array("code"=>'1', 'msg'=>$my_page[$i]['entrust_no'] . '重量超過' . $tw_row['scale'] . '公斤'), JSON_UNESCAPED_UNICODE);
            //         }
            //         $item_arr = array($my_page[$i]['length'], $my_page[$i]['width'], $my_page[$i]['height']);
            //         $value = $tw_row['two'];
            //         $filteredArray = array_filter($item_arr, function($item) use ($value) {
            //             return $item > $value;
            //         });
            //         if(count($filteredArray)>=2){
            //             return json_encode(array("code"=>'1', 'msg'=>$my_page[$i]['entrust_no'] . '兩邊長度超過了' . $tw_row['two']), JSON_UNESCAPED_UNICODE);
            //         }
            //         if( max($item_arr) > $tw_row['single'] ){
            //             return json_encode(array("code"=>'1', 'msg'=>$my_page[$i]['entrust_no'] . '單邊長度大于' . $tw_row['single']), JSON_UNESCAPED_UNICODE);
            //         }
            //         if( array_sum($item_arr) > $tw_row['total'] ){

            //             return json_encode(array("code"=>'1', 'msg'=>$my_page[$i]['entrust_no'] . '長寬高總長大于' . $tw_row['total']), JSON_UNESCAPED_UNICODE);
            //         }
            //     }
            // }

            //是根据ID，  还是用匹配 ？？
            $count_tp_row = $this->query_record('count_transport', array('id'=>$count_tp_id));

            /* 运输费收费方式是两个阶段， 一段是到达台湾前(海运，空运)    一段是到达台湾后（台湾物流）     
            *  下面是第一阶段收费计算     
            */
            $order_scale = 0;
            $order_volume = 0;

            $actual_scale = 0;
            $actual_volume = 0;
            if( $count_tp_row['transport'] == 16 ){

                // 空运， 体积超标的时候才按体积大小计算
                $scale1 = 0;
                $volume1 = 0;
                for($i=0;$i<count($my_page);$i++){
                    $scale1 += $my_page[$i]['scale'];
                    $volume1 += $my_page[$i]['length'] * $my_page[$i]['width'] * $my_page[$i]['height'];
                }
                $actual_scale = $scale1;
                $actual_volume = $volume1;

                $scale1 = ceil($scale1);
                $volume1 = ceil(($volume1 / $count_tp_row['volume']));
                if( $volume1 <= $scale1 * 3 ){
                    $order_scale = $scale1;
                    $count_money['ky_scale_volume'] = $count_tp_row['fr_weight'] + ($scale1-1) * $count_tp_row['fl_weight'];
                }else{
                    $number = ceil($volume1 / 2);
                    $order_volume = $number;
                    $count_money['ky_scale_volume'] = $count_tp_row['fr_weight'] + ($number-1) * $count_tp_row['fl_weight'];
                }

            }else if( $count_tp_row['transport']==15){
                // 海快的处理
                // $scale_price2 = 0;
                // for($i=0;$i<count($my_page);$i++){
                //     // 海快重量大于50公斤拒收
                //     if( ceil($my_page[$i]['scale']) > 50 ){
                //         return json_encode(array("code"=>'1', 'msg'=>'包裹重量大于上限50公斤'), JSON_UNESCAPED_UNICODE);
                //     }
                //     $order_scale += $my_page[$i]['scale'];
                //     $scale_price2 += ceil($my_page[$i]['scale']) *  $count_tp_row['scale_unit'];
                // }

                $scale_arr = array_column($my_page, "scale");
                $actual_scale = array_sum($scale_arr);

                $order_scale = ceil( $actual_scale );
                // 海快--包频繁税和全包税
                if( $order_scale <= $count_tp_row['min_charge_scale']){
                    // 目前---海快5公斤以下按5公斤算
                    $order_scale = $count_tp_row['min_charge_scale'];
                }

                if($count_tp_row['style']==2 || $count_tp_row['style']==3){
                    if( $order_scale <= $count_tp_row['min_charge_scale']){
                        // 目前---海快5公斤以下按5公斤算
                        $count_money['tax'] = $count_tp_row['base_tax'];
                    }else{
                        $count_money['tax'] = $count_tp_row['base_tax'] + ($order_scale-$count_tp_row['min_charge_scale']) * $count_tp_row['tax'];
                    }
                }

                if( $order_scale > 50 ){
                    return json_encode(array("code"=>'1', 'msg'=>'包裹重量大于上限50公斤'), JSON_UNESCAPED_UNICODE);
                }
                
                $count_money['hk_scale'] = ceil($order_scale * $count_tp_row['taxpre']) ;

            }else if($count_tp_row['transport']==17){
                // 海运    计算方式， 重量与材积两种方式同时计算，  哪个大用哪个
                $scale_price3 = 0;
                $volume_price3 = 0;
                for($i=0;$i<count($my_page);$i++){
                    $order_scale += $my_page[$i]['scale'];
                    $volume_price3 += ceil(($my_page[$i]['length'] * $my_page[$i]['width'] * $my_page[$i]['height']) / $count_tp_row['volume']) * $count_tp_row['volume_unit'];
                }

                $actual_scale = $order_scale;
                $actual_volume = $volume_price3;

                // 小于等于5公斤按5公斤算
                if( $order_scale <= 5 ){
                    $order_scale = 5;
                }
                
                $scale_price3 = ceil($order_scale) *  $count_tp_row['scale_unit'];
                $max_num = ($scale_price3 >= $volume_price3)?$scale_price3:$volume_price3;
                /**海运最低收费  mincharge=1400 */
                if($max_num < 1400){
                    $count_money['hy_scale_volume'] = 1400;
                }else{
                    $count_money['hy_scale_volume'] = $max_num;
                }
            }
            
            // 基础运费
            $base_price = 0;
            if($count_tp_row['transport']==15){
                $base_price = $count_money['hk_scale'];
            }else if($count_tp_row['transport']==16){
                $base_price = $count_money['ky_scale_volume'];
            }else if($count_tp_row['transport']==17){
                $base_price = $count_money['hy_scale_volume'];
            }

            /**
             * 保险费用计算
             */
            $insure_data = [];
            // 超时保险
            $insures_cs = $post_data['cs'];       
            if( $insures_cs ){
                $cs_row = $this->query_record('insure', array('alias'=>'CS'));
                $insure_data[] = array(
                    'id'            =>$cs_row['id'],
                    'bj_price'      =>0,
                    'goods_price'   =>0,
                    'ag_price'      =>0,
                    'base_price'    =>$base_price,
                    'total_price'   =>0,
                    'collect_price' =>ceil($base_price *  number_format(floatval($cs_row['cg_proprotion']), 2, '.', ''))
                );
                $count_money['cs_insure'] = ceil($base_price *  number_format(floatval($cs_row['cg_proprotion']), 2, '.', ''));
            }

            // 保价保险
            $insures_bj = $post_data['bj'];
            $bj_row = $this->query_record('insure', array('alias'=>'BJ'));
            $bj_price = $insures_bj;
            $goods_price = 0;                               // 目前商品金额为0
            $ag_price = ceil( $bj_price * number_format(floatval($bj_row['cp_proportion']), 2, '.', '')) + 15;
            // 歉意金额最多只赔3000
            if($ag_price>3000){
                $ag_price = 3000;
            }

            $total_price = $bj_price + $goods_price + $ag_price + $base_price;
            $insure_data[] = array(
                'id'            =>$bj_row['id'],
                'bj_price'      =>$bj_price,
                'goods_price'   =>$goods_price,
                'ag_price'      =>$ag_price,
                'base_price'    =>$base_price,
                'total_price'   =>$total_price,
                'collect_price' =>$bj_price + ceil( $bj_price * number_format(floatval($bj_row['cg_proprotion']), 2, '.', ''))
            );
            $count_money['bj_insure'] = $bj_price + ceil( $bj_price * number_format(floatval($bj_row['cg_proprotion']), 2, '.', ''));

            /**
             *  判断是否 岛外
             */
            $count_money['is_dw'] = ($add_row['type']==1)?300:0;    // 岛外收费 会不会与偏远收费  冲突 ？？？
            /**
             *  是否打木架或者木箱      包装
             */
            $jg_data = $post_data['bz'];
            if( $jg_data ){
                $count_money['bz'] = 0;
                for($i=0;$i<count($my_page);$i++){
                    $volume = round((($my_page[$i]['length'] * $my_page[$i]['width'] * $my_page[$i]['height']) / (100 * 100 * 100)), 2);
                    $volume = ceil($volume * 10) / 10;    
                    if( $jg_data['type'] == 1 ){
                        // 木架
                        $item = 2100 * $volume;
                    }else if($jg_data['type'] == 2){
                        // 木箱
                        $item = 2800 * $volume;
                    }
                    $count_money['bz'] += $item;
                }
            }

            $tb_money = array_sum($count_money);

            /**
             *  优惠券
             */
            // 1. 抵扣金


            // 2. 优惠券


            $actual_money = $tb_money - 0 - 0;
            /**
             * 开始生成订单字段数据
             */
            $pgid_col = array_column($my_page, 'id');
            $unique_id_arr = array_unique($pgid_col);
            $pgid_arr =  array_values($unique_id_arr);
            $pgid_str = implode(',', $pgid_arr);

            $pgno_col = array_column($my_page, 'entrust_no');
            $unique_no_arr = array_unique($pgno_col);
            $pgno_arr =  array_values($unique_no_arr);
            $pgno_str = implode(',', $pgno_arr);

            $map = array(
                    'pid'           =>$pgid_str,
                    'pno'           =>$pgno_str,
                    'order_no'      =>orderNo('10'),
                    'user_id'       =>$this->auth->id,
                    'num'           =>count($my_page),
                    'scale'         =>$actual_scale,            // 实际重量
                    'volume'        =>$actual_volume,           // 实际材积
                    'base_price'    =>$base_price,
                    'count_tp'      =>$count_tp_id,
                    'disp'          =>empty($count_money['delivery'])?0:$count_money['delivery'], 
                    'super_disp'    =>empty($count_money['sup_delivery'])?0:$count_money['sup_delivery'],
                    'insured'       =>(isset($count_money['cs_insure'])?$count_money['cs_insure']:0) + $count_money['bj_insure'],
                    'tb_money'      =>$tb_money,
                    'bal_money'     =>0,            // 抵扣金
                    'user_coupon_id' =>0,           // 用户优惠券
                    'actual_money'  =>$actual_money,
                    'outer_island'  =>$count_money['is_dw'],
                    'pay_type'      =>2,                            
                    // 'pay_type'      =>$post_data['type'],        // 支付方式 1银行 2钱包
                    // 'bank_num'      =>$post_data['bank'],        // 银行 后6位
                    // 'bank_id'      =>$post_data['bank_id'],        // 银行 后6位
                    'address_id'    =>$addr_id,
                    'applicant_id'  =>$appl_id,
                    'take_type'     =>$tw_row['type'],              //   这里传的应该是    表tw_logistics 的ID的    ？？？？？
                    'wh_id'         =>$post_data['wh_id'],
                    'transport'     =>$post_data['transport'],
                    'charge_json'   =>json_encode($count_money, JSON_UNESCAPED_UNICODE),
                    'tax'           =>empty($count_money['tax'])?0:$count_money['tax'],
                    'is_paster'     =>$post_data['paster'],
                    'createtime'    =>time(),
                    'updatetime'    =>time()
            );
            //创建  Db事务
            Db::startTrans();
            try{
                $order_id = $this->getid_insert_record('jyorder', $map);
                if($order_id <= 0){
                    Db::rollback();
                    return json_encode(array("code"=>'1', 'msg'=>'創建訂單失敗'), JSON_UNESCAPED_UNICODE);
                }
                
                if(!$this->update_record('package', array('id'=>array('in', $pg_str)), array('transport'=>$post_data['transport'], 'order_id'=>$order_id))){
                    Db::rollback();
                    return json_encode(array("code"=>'1', 'msg'=>'添加包裹與訂單關聯失敗'), JSON_UNESCAPED_UNICODE);
                }

                // //  什么地方选择银行支付，什么地方使用
                // if( $post_data['type'] == 1 ){
                //     $_match = $this->order_bank_matching($post_data['bank_id'], 'jyorder', $order_id, $post_data['bank']);
                //     if( !$_match ){
                //         Db::rollback();
                //         return json_encode(array("code"=>'1', 'msg'=>'order_bank_matching 错误'), JSON_UNESCAPED_UNICODE);
                //     }
                // }
                // 最后生成集运订单成功后， 创建订单关联的 保险记录 
                if( count($insure_data) > 0 ){
                    $insure_map = [];
                    for($i=0;$i<count($insure_data);$i++){
                        $insure_map[] = array('jy_order'=>$order_id, 
                                                'insure_id'         =>$insure_data[$i]['id'],
                                                'no'                =>orderNo('4'), 
                                                'user_id'           =>$this->auth->id,
                                                'bj_price'          =>$insure_data[$i]['bj_price'],
                                                'goods_price'       =>$insure_data[$i]['goods_price'],
                                                'ag_price'          =>$insure_data[$i]['ag_price'],
                                                'base_price'        =>$insure_data[$i]['base_price'],
                                                'total_price'       =>$insure_data[$i]['total_price'],
                                                'collect_price'     =>$insure_data[$i]['collect_price'],
                                                'createtime'        =>time(), 
                                                'updatetime'        =>time());
                    }
                    if( $this->insert_array_record('order_insure', $insure_map) > 0 ){
                        Db::commit();
                        return json_encode(array("code"=>'0', 'msg'=>'創建訂單成功', 'data'=>$order_id), JSON_UNESCAPED_UNICODE);
                    }
                }else{
                    Db::commit();
                    return json_encode(array("code"=>'0', 'msg'=>'創建訂單成功'), JSON_UNESCAPED_UNICODE);
                }
                Db::rollback();
            }catch (Exception $e){
                Db::rollback();
            }
            return json_encode(array("code"=>'1', 'msg'=>'生成訂單失敗'), JSON_UNESCAPED_UNICODE);
        }


        $pid_str = $this->request->get('pid');
        $rows = Db::name('package')->alias('p')
                                ->join('warehouse w', 'p.wh_id=w.id')
                                ->field('p.*, w.code, w.title, w.addr')
                                ->where('p.status', 2)
                                ->where('p.id', "in", $pid_str)
                                ->select();

        $tw_list = $this->query_records('tw_logistics', array('type'=>'takeway'));

        $addr_rows = Db::name('address')->alias('ar')
                                ->join('city c', 'ar.city=c.id')
                                ->join('district d', 'ar.district=d.id')
                                ->field('ar.*, c.name cname, d.name dname')
                                ->where('ar.user_id', $this->auth->id)
                                ->where('ar.status', 0)
                                ->select();

        $al_rows = $this->query_records('applicant', array('user_id'=>$this->auth->id));

        $count_tp =  Db::name('count_transport')->alias('ct')
                                ->join('dict d', 'ct.transport=d.id')
                                ->field('ct.*, d.name')
                                ->select();

        $insure_rows = $this->query_records('insure',array());

        $wid = $this->request->get('wid');
        $wt_list = Db::name('warehouse_transport')->alias('wt')
                                ->join('dict d', 'wt.type=d.id')
                                ->where('wt.wh_id', $wid)
                                ->field('wt.type, d.name')
                                ->where('wt.status', 0)
                                ->select();

        return json_encode(array("code"=>'0', 'wt_list'=>array('id'=>$wid, 'list'=>$wt_list), 'pg_list'=>$rows, 'tw_list'=>$tw_list, 'insure_list'=>$insure_rows, 'addr_list'=>$addr_rows, 'count_tp_list'=>$count_tp, 'applicant_list'=>$al_rows), JSON_UNESCAPED_UNICODE);
        // return json_encode($insure_rows, JSON_UNESCAPED_UNICODE);
        // $this->view->assign('pg_list', $pg_list);                   //   包裹列表
        // $this->view->assign('tw_list', $tw_list);                   //   台湾物流       
        // $this->view->assign('insure_list', $insure_rows);           //   保险列表
        // $this->view->assign('addr_list', $addr_rows);               //   地址列表
        // $this->view->assign('count_tp_list', $count_tp);            //   运输方式列表
        // $this->view->assign('applicant_list', $al_rows);            //   申报人列表
        // return $this->view->fetch('transport/corder/index');

    }





    public function order_info(){

        if($this->request->isPost()){
            $id = $this->request->post('id');
            $order_row = Db::name('jyorder')->alias('jy')
                            ->join('dict d', 'jy.transport=d.id')
                            ->join('warehouse wh', 'jy.wh_id=wh.id')
                            ->field('jy.*,d.name,wh.title')
                            ->where('jy.id', $id)
                            ->where('jy.user_id', $this->auth->id)
                            ->find();

            if( !$order_row ){
                return json_encode(array('code'=>'1', "msg"=>"獲取訂單信息失敗"), JSON_UNESCAPED_UNICODE);
            }            
            $page_rows = Db::name('package')->alias('p')
                            ->join('wuliu wl', 'p.wuliu_id=wl.id')
                            ->field('p.*, wl.sname, wl.icon')
                            ->where('p.id', 'in', $order_row['pid'])
                            ->select();

            $order_insure = $this->query_records('order_insure', array('jy_order'=>$id));
            $ct = $this->query_record('count_transport', array('id'=>$order_row['count_tp']));
            $addr_row = $this->query_record('address', array('id'=>$order_row['address_id']));
            $appl_row = $this->query_record('applicant', array('id'=>$order_row['applicant_id']));
            $sys_bank = $this->query_record('sys_bank', array('is_relation'=>1, 'status'=>'normal'));
            return json_encode(array("code"=>'0', 'oi' =>$order_insure, 'ct'=>$ct, 'order'=>$order_row, 'list'=>$page_rows, 'addr'=>$addr_row, 'appl'=>$appl_row, 'bank'=>$sys_bank), JSON_UNESCAPED_UNICODE);
        }

    }


    /**
     * 包裹待出货
     * @return bool|string
     */
    public function order_package_status(){

        if($this->request->isPost()){
            $status = $this->request->post('status');
            if( $status != '3' && $status != '4' ){
                return json_encode(array('code'=>'1', "msg"=>"未選擇正確狀態"), JSON_UNESCAPED_UNICODE);
            }

            $wh = $this->request->post('wh', 1);
            $rows = $this->query_records('package', array('user_id'=>$this->auth->id, 'status'=>$status, 'order_id'=>array(">", 0), 'wh_id'=>$wh, 'is_del'=>0), 'order_id');
            $cvalues = array_column($rows, "order_id");
            $orderidArr = array_unique($cvalues);
            if( count($orderidArr) <= 0 ){
                return json_encode(array('code'=>'1', "msg"=>"沒有符合需求的訂單"), JSON_UNESCAPED_UNICODE);
            }

            $no = $this->request->post('no');
            if( !empty($no) ){
                $jymap = array('order_no'=>$no);
            }else{
                $jymap = array('id'=>array("in", implode(',', $orderidArr)));
            }

            $order_list = $this->query_records('jyorder', $jymap);
                    // $pag_list = $this->query_records('package', array('order_id'=>array("in", implode(',', $orderidArr))));

            $pag_list = Db::name('package')->alias('p')
                                            ->join('addservice a', 'p.addser_id=a.id', 'LEFT')
                                            ->join('wuliu wl', 'p.wuliu_id=wl.id')
                                            ->field('p.*, a.addser_type_id, wl.sname, wl.icon')
                                            ->where('p.order_id', 'in', implode(',', $orderidArr))
                                            ->select();

            $avalues = array_column($order_list, "address_id");
            $addr_list = Db::name('address')->alias('ar')
                                ->join('city c', 'ar.city=c.id')
                                ->join('district d', 'ar.district=d.id')
                                ->field('ar.*, c.name cname, d.name dname')
                                ->where('ar.id',"in", implode(',', $avalues))
                                ->select();
            $transport = $this->query_records('count_transport', array());

            $list = [];
            for($i=0;$i<count($order_list);$i++){

                $group = $this->list_get_group($pag_list, 'order_id', $order_list[$i]['id']);
                $order_list[$i]['page'] = $group;
                $addr = $this->list_get_line($addr_list, 'id', $order_list[$i]['address_id']);
                $order_list[$i]['addr'] = $addr;
                $tp = $this->list_get_line($transport, 'id', $order_list[$i]['count_tp']);
                $order_list[$i]['transport'] = $tp;
                $list[] = $order_list[$i];

            }

            return json_encode(array('code'=>'0', "msg"=>"成功", 'list'=>$list), JSON_UNESCAPED_UNICODE);
        }

    }


    /**
     * 当集运订单选择支付的时候  跳转的地方
     * @return void
     */
    public function order_pay(){
        //  ??????????????
        // 银行bank_num 与 bank_id(user_bank表id)  ,  还有  order_bank_matching 表上插入数据
        if($this->request->isPost()){
            $id = $this->request->post('id');
            $order_map = array(
                'id'            =>$id,
                'user_id'       =>$this->auth->id,
                'order_status'  =>0
            );

            $order_row = $this->query_record('jyorder', $order_map);
            if( !$order_row ){
                return json_encode(array('code'=>'1', "msg"=>"訂單信息匹配錯誤"), JSON_UNESCAPED_UNICODE);
            }
            if( $order_row['pay_type'] == 1 ){
                $sys_bank = $this->query_record('sys_bank', array('is_relation'=>1,'status'=>'normal'));
                return json_encode(array('code'=>'1', "msg"=>"請完成銀行轉賬，收款賬戶：" . $sys_bank['account_name'] . '-' . $sys_bank['account_num']), JSON_UNESCAPED_UNICODE);
            }

            $nouse = $this->get_field_value('user', array('id'=>$this->auth->id), 'nouse');
            if(!$nouse){
                if( !($this->auth->verifyPayPwd($this->request->post('pwd'))) )
                {
                    return json_encode(array('code'=>'1', "msg"=>"密碼錯誤"), JSON_UNESCAPED_UNICODE);
                }
            }

            Db::startTrans();
            try{
                if( $order_row['pay_type'] == 2 ){
                    $user = $this->query_record('user', array('id'=>$this->auth->id));
                    if( $user['money'] < $order_row['actual_money'] ){
                        Db::rollback();
                        return json_encode(array('code'=>'1', "msg"=>"錢包余額不足，請先充值！"), JSON_UNESCAPED_UNICODE);
                    }
                }
                $_res = $this->money_log(3, $order_row['actual_money'], $order_row['id'], 'jyorder');
                if( $_res ){
                    $this->orderLog('钱包支付成功' . $order_row['actual_money'],$order_row['id'], 'jyorder');
                    if( $this->order_processing('jyorder', $order_row['id'], 2, 1) ){
                        $this->update_record('package', array('order_id'=>$order_row['id']), array('status'=>3));
                        Db::commit();
                        return json_encode(array('code'=>'0', "msg"=>"訂單支付成功"), JSON_UNESCAPED_UNICODE);
                    }
                }
                
            }catch (Exception $e){
                // echo $e->getMessage();  
            }
            Db::rollback();
            return json_encode(array('code'=>'1', "msg"=>"訂單支付過程出現異常"), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 包裹物流-----大陆部分     
     * @return void
     */
    public function package_logistics(){

        $id = $this->request->get('pid');
        $row = $this->query_record('package', array('id'=>$id));
        $wuliu_row = $this->query_record('wuliu', array('id'=>$row['wuliu_id']));
        $com = $wuliu_row['alias'];
        if( strlen($com) <= 0 ){
            return json_encode(array('code'=>'1', "msg"=>"快遞編號爲空無法查詢"), JSON_UNESCAPED_UNICODE);
        }

        $phone = "";
        if( $com == 'shunfeng' ){
            $contact_row = $this->query_record('contact', array('wh_id'=>$row['wh_id']));
            $phone = $contact_row['mobile'];
        }

        // 根据包裹状态来判断用什么物流查询
        if( $row['status'] <=3 ){
            $data = $this->kuaidi_hd_query($this->auth->id, $id, $com, $row['waybill'], $phone);
            return json_encode($data, JSON_UNESCAPED_UNICODE);
        }

        return json_encode(array('code'=>'1', "msg"=>"當前系統忙碌中"), JSON_UNESCAPED_UNICODE);

    }


    


    /**
     * 用户确认收货改变状态
     * @return void
     */
    public function order_complete(){
        if($this->request->isPost()){
            $no = $this->request->post('no');
            $row = $this->query_record('jyorder', array('order_no'=>$no));
            if($row['order_status'] != 2){
                return json_encode(array('code'=>'1', "msg"=>"當前狀態不支持"), JSON_UNESCAPED_UNICODE);
            }

            $this->update_record('jyorder', array('order_no'=>$no), array('order_status'=>3));
            // $this->update_record('package', array('id'=>array("in", $row['pid'])), array('status'=>5));
            return json_encode(array('code'=>'0', "msg"=>"完成"), JSON_UNESCAPED_UNICODE);
        }
    }



    /**
     * 删除集运订单
     * @return void
     */
    public function order_delete(){
        if($this->request->isPost()){
            $no = $this->request->post('no');   
            $row = $this->query_record('jyorder', array('order_no'=>$no));
            if( $row['pay_status'] > 0 && $row['order_status'] < 3){
                return json_encode(array('code'=>'1', "msg"=>"當前狀態不支持"), JSON_UNESCAPED_UNICODE);
            }

            $this->update_record('jyorder', array('order_no'=>$no), array('is_del'=>1));
            $this->update_record('package', array('id'=>array("in", $row['pid'])), array('is_del'=>1));
            return json_encode(array('code'=>'0', "msg"=>"删除成功"), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 台湾物流查询信息
     */
    public function tw_logistics(){
        // if($this->request->isPost()){
        //     $name = $this->request->post('name');   
        //     $bill = $this->request->post('bill');   

        //     $return_msg = null;
        //     switch ($name) {
        //         case 21:
        //             $return_msg = $this->tw_logistics_tcat($name, $bill);           // 黑猫
        //             break;
        //         case 22:
        //             $return_msg = $this->tw_logistics_hct($name, $bill);            // 新竹
        //             break;
        //         case 23:
        //             $return_msg = $this->tw_logistics_ktj($name, $bill);            // 大荣
        //             break;
        //         default:
        //             $return_msg = array('code'=>1, 'msg'=>"未支持的查詢類型") ;
        //             break;
        //     }
        

        //     return $return_msg;
        // }


        $name = $this->request->param('name');   
        $bill = $this->request->param('bill');   

        $return_msg = null;
        switch ($name) {
            case 'tcat':
                $return_msg = $this->tw_logistics_tcat($name, $bill);           // 黑猫
                break;
            case 'hct':
                $return_msg = $this->tw_logistics_hct($bill);            // 新竹
                break;
            case 'ktj':
                $return_msg = $this->tw_logistics_ktj($bill);            // 大荣
                break;
            default:
                $return_msg = array('code'=>1, 'msg'=>"未支持的查詢類型") ;
                break;
        }
        return $return_msg;
    }



    private function tw_logistics_tcat($name, $bill){

        $res = $this->tw_tcat_query($name,$bill);
        $js_res = json_decode($res, true);
        if( isset($js_res['result']) ){
            // return array('code'=>'1', 'msg'=>$js_res['message']);
            return json_encode(array('code'=>'1', 'msg'=>$js_res['message']), JSON_UNESCAPED_UNICODE);
        }

        
        $js_data = $js_res['data'];
        if( count($js_data) < 0 ){
            // return array('code'=>'1', 'message'=>'未查詢到任務信息');
            return json_encode(array('code'=>'1', 'message'=>'未查詢到任務信息'), JSON_UNESCAPED_UNICODE);
        }
       
        $data = [];
        $js_data = array_reverse($js_data);
        for($i=0;$i<count($js_data);$i++){
            $data[] = array( 
                            'date'      =>$js_data[$i]['time'], 
                            'addr'      =>$js_data[$i]['areaName'], 
                            'status'    =>$js_data[$i]['status'],
                            'context'   =>$js_data[$i]['context']); 
        }
        // return $array('code'=>0, 'msg'=>'查詢成功', 'data'=>$data);
        return json_encode(array('code'=>0, 'msg'=>'查詢成功', 'data'=>$data), JSON_UNESCAPED_UNICODE);
    }


    private function tw_logistics_hct($bill){

        $res = $this->tw_hct_query($bill);
        $js_res = json_decode($res, true);
        if( count($js_res) <= 0 ){
            // return array('code'=>1, 'msg'=>'未查詢到任務信息');
            return json_encode(array('code'=>1, 'msg'=>'未查詢到任務信息'), JSON_UNESCAPED_UNICODE);
        }        

        $data = [];
        for($i=0; $i<count($js_res); $i++){
            $item = array(
                'date'      =>$js_res[$i]['date'],
                'addr'      =>$js_res[$i]['addr'],
                'status'    =>$js_res[$i]['status'],
                'context'   =>'',
            );
            $data[] = $item;
        }

        // return array('code'=>0, 'msg'=>'查詢成功', 'data'=>$data);
        return json_encode(array('code'=>0, 'msg'=>'查詢成功', 'data'=>$data), JSON_UNESCAPED_UNICODE);
    }


    private function tw_logistics_ktj($bill){

        $res = $this->tw_ktj_query($bill);
        $js_res = json_decode($res, true);
        $result = $js_res['Result'];
        if(count($result) <= 0){
            return json_encode(array('code'=>1, 'msg'=>'未查詢到任務信息'), JSON_UNESCAPED_UNICODE);
        }
        $trac = $result[0]['CargoTracing'];

        $data = [];
        $ktj = Config::get('site.ktj');
        for($i=0; $i<count($trac); $i++){
            $item = array(
                'date'      =>timeToTimestring($trac[$i]['Date'] . $trac[$i]['Time']),
                'addr'      =>$trac[$i]['Station'],
                'status'    =>$trac[$i]['Status'],
                'context'   =>$ktj[$trac[$i]['StatusCode']],
            );
            $data[] = $item;
        }

        // return array('code'=>0, 'msg'=>'查詢成功', 'data'=>$data);
        return json_encode(array('code'=>0, 'msg'=>'查詢成功', 'data'=>$data), JSON_UNESCAPED_UNICODE);
    }


    public function logistics(){
        return $this->view->fetch('transport/myParcel/logistics');
    }


    public function changeOrderAddr(){
        if($this->request->isPost()){
            $id = $this->request->post('id'); 
            $address = $this->request->post('addr');
            $map = array(
                'address_id'    =>$address,
            );

            $jsorder = $this->query_record('jyorder', array('id'=>$id));
            if( empty($jsorder['waybill']) ){
                $update_res = $this->update_record('jyorder', array('id'=>$id), $map);
                if($update_res){
                    return json_encode(array('code'=>'0', "msg"=>"修改成功"), JSON_UNESCAPED_UNICODE);
                }
                return json_encode(array('code'=>'1', "msg"=>"修改失败"), JSON_UNESCAPED_UNICODE);
            }

            $map['change_addr'] = $jsorder['change_addr'] + 1;
            $user = $this->query_record('user', array('id'=>$this->auth->id));
            if(!$user['nouse']){
                if( !($this->auth->verifyPayPwd($this->request->post('pwd'))) )
                {
                    return json_encode(array('code'=>'1', "msg"=>"密碼錯誤"), JSON_UNESCAPED_UNICODE);
                }
            }


            Db::startTrans();
            try{
                // 检测金额够不够
                if( $user['money'] < 200 ){
                    Db::commit();
                    return json_encode(array('code'=>'1', "msg"=>"余額不足，請先充值"), JSON_UNESCAPED_UNICODE);
                }
                // 生成一个额外支付 的订单
                $data = array(
                    'order_id'          =>$jsorder['id'],
                    'order_no'          =>orderNo('102'), 
                    'type'              =>1,
                    'tb_money'          =>200,
                    'actual_money'      =>200,
                    'pay_type'          =>1,
                    'pay_status'        =>1,                 //  以后可能会有抵扣金
                    'order_status'      =>1,
                    'createtime'        =>time()
                );

                $exid = $this->getid_insert_record('jyorder_extra_pay', $data);
                if( $exid > 0 ){

                    $this->money_log(1, 200, $id, 'jyorder', "购买订单服务");
                    // 修改数据
                    $update_res = $this->update_record('jyorder', array('id'=>$id), $map);
                    if($update_res){
                        Db::commit();
                        return json_encode(array('code'=>'0', "msg"=>"修改成功"), JSON_UNESCAPED_UNICODE);
                    }
                }
            }catch (Exception $e){
                $e->getMessage();  
            }
            Db::rollback();
            return json_encode(array('code'=>'1', "msg"=>"修改失败"), JSON_UNESCAPED_UNICODE);
        }
    }

































}
