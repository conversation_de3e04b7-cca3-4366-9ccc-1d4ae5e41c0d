<?php



namespace app\index\controller;

use addons\wechat\model\WechatCaptcha;
use app\common\controller\Frontend;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Attachment;
use think\Config;
use think\Cookie;
use think\Hook;
use think\Session;
use think\Validate;
use think\Db;
use app\common\sdk\Alibaba;
use app\common\sdk\KuaiDiHd;
use think\Exception;
use DateTime;


/**
 * 工具类
 */
class Daigou extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = ['aliShoppCart'];
    protected $noNeedRight = ['*'];


    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
		if(!$this->auth->id){
			$this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		}
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'), '/');
        }
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
     
        $this->view->assign('ehg', 4.585);
    }


    // public function index(){
    //     return $this->view->fetch();
    // }

    

//=============================================================================================
//=============================================================================================
//=============================================================================================

    /**
     * 以下部分为对接部分
     */


    /**
     * 用户输入链接获取1688商品详情
     * @return bool|string
     */
    public function aliSearch(){
        if($this->request->isPost()){
            $link = $this->request->post('link');
            $id = Frontend::getIdByLink($link);
            if(empty($id)){
                return json_encode(array('code'=>'1', 'msg'=>'解析商品出現問題'), JSON_UNESCAPED_UNICODE);
            }

            $res = $this->ali->queryProductDetail($id);
            $js_data = json_decode($res, true);
            if(  $js_data['result']['success'] == false ){
                return json_encode(array('code'=>'1', 'msg'=>$js_data['result']['message']), JSON_UNESCAPED_UNICODE);
            }

            return json_encode(array('code'=>'0', 'data'=>$res), JSON_UNESCAPED_UNICODE);
        }

        return $this->view->fetch('daigou/alibaba/search/index');
    }


    /**
     * 商品详情
     * @return string
     */
    public function aliGoods(){
        $offer = $this->request->param('offer');
        $res = $this->ali->queryProductDetail($offer);
        $data = json_decode($res, true);


        $id = $this->get_field_value('sys_alipay', array('type'=>1, 'status'=>0), 'id');
        $addrlist = $this->query_records('sys_alipay_address', array('sys_alipay_id'=>$id));

        $row = $this->query_record('daigou_alicollect', array('user_id'=>$this->auth->id, 'offer'=>$offer));
        if($row){
            $this->view->assign('collect', true);
        }else{
            $this->view->assign('collect', false);
        }
        $this->view->assign('addr', $addrlist);
        $this->view->assign('data', $data);
        return $this->view->fetch('daigou/alibaba/search/goods');
    }

    /**
     * 加入 模拟购物车
     * @return void
     */
    public function aliAddCart(){
        if( $this->request->isPost() ){
            $data = $this->request->param();
            $sellerId = $data['sellerOpenId'];
            $offer = $data['offer'];
            $images = $data['img'];
            $subject = $data['subject'];
            
            // spec = [{"specId":"11111", "cargoNumber":"棒球-【THE】黑底白字", "price":"4.6", "num":"6", "skuImageUrl":"https://cbu01.alicdn.com/img/ibank/O1CN01EkYnMr2FSdhfnMF4x_!!*************-0-cib.jpg"}]
            $spec = $data['spec'];
            $shop = $this->ali->openuidDecrypt($sellerId);
            $data = array(
                'user_id'       =>$this->auth->id,
                'sellerId'      =>$sellerId,
                'shop'          =>$shop,
                'offer'         =>$offer,
                'images'        =>$images,
                'subject'       =>$subject,
                'spec'          =>json_encode(array('data'=>$spec), JSON_UNESCAPED_UNICODE),
                'createtime'    =>time()
            );
            $id = $this->getid_insert_record('daigou_shop_cart', $data);
            if($id){
                //return $this->success('加購成功');
                return json_encode(array('code'=>'0', 'msg'=>'加購成功'), JSON_UNESCAPED_UNICODE);
            }
            // return $this->error('加購失敗');
            return json_encode(array('code'=>'1', 'msg'=>'加購失敗'), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 购物车列表与删除商品
     */
    public function aliShoppCart(){
        if( $this->request->isPost() ){
            // 删除功能
            $param = $this->request->param();
            $data = $param['data'];

            $msg = [];
            for($i=0; $i<count($data);$i++){
                $item = $data[$i];
                $id = $item['id'];
                $specId = $item['specId'];
                $row = $this->query_record('daigou_shop_cart', array('id'=>$id,'user_id'=>$this->auth->id));
                $jdata = json_decode($row['spec'], true);
                $list = $jdata['data'];
                for($j=0;$j<count($specId);$j++){
                    for($k=0;$k<count($list);$k++){
                        if( $list[$k]['specId'] == $specId[$j] ){
                            unset($list[$k]);
                            break;
                        }
                    }
                }

                if( count($list) <= 0 ){
                    $res = $this->delete_record('daigou_shop_cart', array('id'=>$id));                
                }else{
                    $list = array_values($list);
                    $res = $this->update_record('daigou_shop_cart', array('id'=>$id), array('spec'=>json_encode(array('data'=>$list), JSON_UNESCAPED_UNICODE)));
                }
                if( $res ){
                    // return $this->success('刪除成功');
                    $msg[] = array('id'=>$id,'msg'=>'刪除成功');
                }else{
                    $msg[] = array('id'=>$id,'msg'=>'刪除失敗');
                }
                
            }
            return json_encode(array('code'=>0, 'msg'=>$msg), JSON_UNESCAPED_UNICODE);
        }


        $id = $this->get_field_value('sys_alipay', array('type'=>1, 'status'=>0), 'id');
        $addrlist = $this->query_records('sys_alipay_address', array('sys_alipay_id'=>$id));
        $this->view->assign('addr', $addrlist);

        $list = $this->query_records('daigou_shop_cart', array('user_id'=>$this->auth->id));
        $this->view->assign('list', $list);
        return $this->view->fetch('daigou/alibaba/shoppCart/index');
    }


    public function aliSubmitOrder(){

        if( $this->request->isPost() ){
            $param = $this->request->param();
            $data = $param['data'];

            // 获取阿里代购账号ID
            $id = $this->get_field_value('sys_alipay', array('type'=>1, 'status'=>0), 'id');
            $addrlist = $this->query_records('sys_alipay_address', array('sys_alipay_id'=>$id));

            $this->view->assign('list', $data);
            $this->view->assign('addr', $addrlist);
            return $this->view->fetch('daigou/alibaba/submitali/index');
        }
    }



    /**
     * 订单预览
     * @param mixed $data
     * @param mixed $addressId
     * @return array{code: int, msg: string}
     */
    private function aliOrderPreview($data, $addressId){
        $address_param['addressId'] = $addressId;
        for($i=0;$i<count($data);$i++){
            $item = $data[$i];
            if( count($item['spec']) <= 0 ){
                continue;
            }
            for($j=0;$j<count($item['spec']);$j++){
                $tmp = $item['spec'][$j];
                $cargoParam_list = [];
                $cargoParam_list[] = [
                    'offerId' => $item['offer'],
                    'specId' => $tmp['specId'],
                    'quantity' => $tmp['num'],
                ];
            }
            $res = $this->ali->createOrderPreview($address_param, $cargoParam_list);
            $js_data = json_decode($res, true);
            if(  $js_data['success'] == false ){
                $msg = $js_data['errorMsg'] ?? $js_data['message'];
                return array('code'=>1,'msg'=> '商品' . $item['offer'] . ": " . $msg);
            }

        }
        return array('code'=>0,'msg'=>'成功');
    }


    /**
     * 创建订单
     * @return bool|string
     */
    public function aliOrderCreate(){
        if( $this->request->isPost() ){
            // {"addr":"221122", "data":[{"id":"0", "sellerId":"","offer":"","img":"","subject":"", 
            // "spec":[{"specId":"", "cargoNumber":"", "price":"","num":"","skuImageUrl":""}, {"specId":"", "cargoNumber":"", "price":"","num":"","skuImageUrl":""}]},
            // {"id":"0", "sellerId":"",   "offer":"",  "img":"", "subject":"", "spec":[{"specId":"", "cargoNumber":"", "price":"","num":"","skuImageUrl":""}, {"specId":"", "cargoNumber":"", "price":"","num":"","skuImageUrl":""}]}]}
            

            $row_res = $this->dgUserOrder();
            if( !$row_res ){
                return json_encode(array('code'=>'1', 'msg'=>"你有壹筆未完成訂單，請先完成"), JSON_UNESCAPED_UNICODE);    
            }

            $data = $this->request->param();
            $addressId = $data['addr'];
            $shop = $data['data'];
            if( count($shop) <= 0 ){
                return json_encode(array('code'=>'1', 'msg'=>'未找到商品'), JSON_UNESCAPED_UNICODE);
            }

            if( count($shop) > 10 ){
                return json_encode(array('code'=>'1', 'msg'=>'每次生成訂單的商品種類不能超過10個'), JSON_UNESCAPED_UNICODE);
            }

            //订单预处理查看
            $preview_res = $this->aliOrderPreview($shop, $addressId);
            if($preview_res['code'] != 0){
                return json_encode(array('code'=>'1', 'msg'=>$preview_res['msg']), JSON_UNESCAPED_UNICODE);
            }

            $address_param['addressId'] = $addressId;
            // 生成不同店铺订单
            $goodlist = [];
            for($i=0;$i<count($shop);$i++){
                $item = $shop[$i];
                $sellerOpenId = $item['sellerId'];
                if( count($item['spec']) <= 0 ){
                    continue;
                }
                $cargoParam_list = [];
                for($j=0;$j<count($item['spec']);$j++){
                    $tmp = $item['spec'][$j];
                    $cargoParam_list[] = [
                        'offerId' => $item['offer'],
                        'specId' => $tmp['specId'],
                        'quantity' => $tmp['num'],
                    ];
                }
                $goods = array();
                $goods['offer'] = $item['offer'];
                $goods['subject'] = $item['subject'];

                $res = $this->ali->createCrossOrder($address_param, $cargoParam_list);
                $js_data = json_decode($res, true);
                if(  $js_data['success'] ){
                    $goods['success'] = $js_data['success'];
                    $goods['result'] = $js_data['result'];
                    $id = $this->aliOrderInfo($js_data['result']['orderId'], $item['offer'], $addressId, $sellerOpenId);
                    $goods['index'] = $id;
                }else{
                    $goods['success'] = $js_data['success'];
                    $goods['message'] = $js_data['message'];
                    return json_encode(array('code'=>'1', 'msg'=>'商品' . $item['offer'] . ": " . $js_data['message']), JSON_UNESCAPED_UNICODE);
                }
                $goodlist[] = $goods;
            }

            //创建平台订单
            $res = $this->daigouAliOrder($goodlist);
            if( $res == false ){
                return json_encode(array('code'=>'1', 'msg'=>"创建平台订单失败！"), JSON_UNESCAPED_UNICODE);    
            }

            // 这里需要删除购物车相关物品  记录
            if( isset($data['is_cart']) ){
                $list = array_column($shop, 'offer');
                $this->aliCartDelete($list);
            }
            return json_encode(array('code'=>'0', 'msg'=>"成功",'id'=>$res), JSON_UNESCAPED_UNICODE);
        }
    }


    private function aliCartDelete($offerlist){
        $del_res = $this->delete_record('daigou_shop_cart', array('user_id'=>$this->auth->id,'offer'=>array('in', implode(',', $offerlist))));
        return $del_res;
    }




    /**
     * 创建平台阿里订单
     * @param mixed $goodlist
     * @return bool|string
     */
    private function daigouAliOrder($goodlist){
        $cvalues = array_column($goodlist, "index");
        $id_list = implode(',', $cvalues);
        $rows = $this->query_records('daigou_order_goods', array('id'=>array("in", $id_list)));

        $totalAmount = 0;
        $shippingFee = 0;
        $couponFee = 0;
        $ids = [];
        
        for($i=0;$i<count($rows);$i++){
            $ids[] = $rows[$i]['id'];
            $totalAmount += $rows[$i]['totalAmount'];
            $shippingFee += $rows[$i]['shippingFee'];
            $couponFee += $rows[$i]['couponFee'];
        }

        // 汇率暂时写死                    =============================？？？？
        $exchange = 4.585;
        $site_exchange = 4.585;
        $tb = ceil( $totalAmount * $exchange );
        $service =  ceil( $tb * 0.01 );
        $tb_money = $tb + $service;             // 后面还有优惠，要减去

        $map = array(
            'user_id'               =>$this->auth->id,
            'type'                  =>0,
            'order_no'              =>orderNo('101'),
            'tb_money'              =>$tb_money,
            'balance_money'         =>0,         //这个是T币支付的显示    // 不知道要不要服务费，这里相当于F币支付
            'actual_money'          =>0,
            'receipts_money'        =>0,
            'service'               =>$service,
            'exchange'              =>$exchange,
            'site_exchange'         =>$site_exchange,
            'pay_type'              =>0,
            'pay_status'            =>0,
            'bank_id'               =>0,
            'bank_num'              =>"",
            'order_status'          =>0,
            'totalAmount'           =>$totalAmount,
            'shippingFee'           =>$shippingFee,
            'couponFee'             =>$couponFee,
            'goods_id'              =>implode(',', $ids),
            'remarks'               =>"",
            'createtime'            =>time(),
            // 'expiretime'            =>time() + 60 * 10,              //////////  需要处理
        );

        $id = $this->getid_insert_record('daigou_order', $map);
        if($id){
            return $map['order_no'];
        }
        return false;
    }


    /**
     * 订单创建成功->提交付款方式
     * @return string
     */
    public function daigouAliOrderConfirm(){
        if( $this->request->isPost() ){
            $id = $this->request->post('id');
            $type = $this->request->post('type');
            $bankid = $this->request->post('bankid');
            $map = array();
            $map['pay_type'] = $type;
            if( $type == 2 ){
                $bank_num = $this->get_field_value('user_bank', array('id'=>$bankid), 'account_six');
                $map['bank_id'] = $bankid;
                $map['bank_num'] = $bank_num;
            }
            $res = $this->update_record('daigou_order', array('id'=>$id), $map);
            if( $res ){
                return json_encode(array('code'=>'0', 'msg'=>"更新成功"), JSON_UNESCAPED_UNICODE); 
            }
            echo Db::name('daigou_order')->getLastSql();
            return json_encode(array('code'=>'1', 'msg'=>"更新失敗"), JSON_UNESCAPED_UNICODE); 
        }

        $no = $this->request->get('no');
        $row = $this->query_record('daigou_order', array('order_no'=>$no));
        $goodses = $this->query_records('daigou_order_goods', array('id'=>array("in", $row['goods_id'])));

        $avalues = array_column($goodses, "addressId");
        $addrArr = array_unique($avalues);
        $addr_list = $this->query_records("sys_alipay_address", array("addressId"=>array("in", implode(',', $addrArr))));

        for($i=0;$i<count($goodses);$i++){
            $addr_row = $this->list_get_line($addr_list, "addressId", $goodses[$i]['addressId']);
            $goodses[$i]['address'] = $addr_row;
        }
        $row['goods'] = $goodses;

        $sys_bank = $this->query_record('sys_bank', array('is_relation'=>1, 'status'=>'normal'));
        $user_bank = Db::name('user_bank')->alias('ub')
                                ->join('bank bk', 'ub.bank_id=bk.id')
                                ->where(['ub.user_id'=>$this->auth->id,'ub.is_state'=>1,'ub.status'=>'normal'])
                                ->field('ub.id,bk.name,ub.account_name,ub.account_six')
                                ->select();

        // 可能暂时不用
        // $twpay = $this->query_record('sys_twpay', array('is_relation'=>1, 'status'=>'normal'));
        // $this->view->assign('twpay', $twpay);

        $this->view->assign('list', $row);
        $this->view->assign('sys_bank', $sys_bank);
        $this->view->assign('user_bank', $user_bank);
        return $this->view->fetch('daigou/alibaba/orderconfirm/index');
    }


    /**
     * 平台取消订单 :  取消平台订单里的单个阿里订单
     * @return string
     */
    public function daigouAliOrderCancel(){
        if( $this->request->isPost() ){

            $no = $this->request->post('no');           //平台单号
            $orderid = $this->request->post('id');      //阿里单号

            $goodsid = $this->get_field_value('daigou_order_goods', array('orderId'=>$orderid), 'id');
            $order = $this->query_record('daigou_order', array('order_no'=>$no));
            if(!$order){
                return json_encode(array('code'=>'1', 'msg'=>"訂單不存在"), JSON_UNESCAPED_UNICODE);    
            }
            // 然后判断订单是否已经确定支付方式--->确认的订单不允许修改
            if( $order['pay_type'] != 0 ){
                return json_encode(array('code'=>'1', 'msg'=>"確認支付方式後不能再修改訂單"), JSON_UNESCAPED_UNICODE);    
            }

            $goodsArr = explode(",", $order['goods_id']);
            $index = array_search($goodsid, $goodsArr);
            if($index !== false){
                unset($goodsArr[$index]);
            }
            if( count($goodsArr) < 0 ){
                $this->update_record('daigou_order_goods', array('id'=>$goodsid), array('status'=>3));      //取消
                $this->update_record('daigou_order', array('order_no'=>$no), array('order_status'=>3));     //取消
                return json_encode(array('code'=>'0', 'msg'=>"取消订单成功！"), JSON_UNESCAPED_UNICODE);    
            }

            // 重新计算
            $rows = $this->query_records('daigou_order_goods', array('id'=>array("in", implode(',', $goodsArr))));

            $totalAmount = 0;
            $shippingFee = 0;
            $couponFee = 0;
            $ids = [];
            
            for($i=0;$i<count($rows);$i++){
                $ids[] = $rows[$i]['id'];
                $totalAmount += $rows[$i]['totalAmount'];
                $shippingFee += $rows[$i]['shippingFee'];
                $couponFee += $rows[$i]['couponFee'];
            }

            // 汇率暂时写死                    =============================？？？？
            $exchange = 4.585;
            $site_exchange = 4.585;
            $tb = ceil( $totalAmount * $exchange );
            $service =  ceil( $tb * 0.01 );
            $tb_money = $tb + $service;             // 后面还有优惠，要减去

            $map = array(
                'tb_money'              =>$tb_money,
                'balance_money'         =>$totalAmount,         // 不知道要不要服务费，这里相当于F币支付
                'service'               =>$service,
                'exchange'              =>$exchange,
                'site_exchange'         =>$site_exchange,
                'totalAmount'           =>$totalAmount,
                'shippingFee'           =>$shippingFee,
                'couponFee'             =>$couponFee,
                'goods_id'              =>implode(',', $ids),
                'createtime'            =>time(),
            );

            $update_res = $this->update_record('daigou_order', array('order_no'=>$no), $map);
            if($update_res){
                return json_encode(array('code'=>'0', 'msg'=>"取消订单成功！", 'no'=>$no), JSON_UNESCAPED_UNICODE);   
            }
            return json_encode(array('code'=>'1', 'msg'=>"取消訂單過程出現異常"), JSON_UNESCAPED_UNICODE);    
        }
    }


    /**
     * 平台订单列表-->阿里巴巴
     * @return string
     */
    public function aliOrderList(){

        $map = array('user_id'=>$this->auth->id,'is_del'=>0);                                
        $order_list = Frontend::getOrderList($map, 10, 'daigou_order_goods');      
        $data = $order_list->toArray()['data'];
        // $data = $order_list->toArray()['page'];  获取页数
        

        $id = $this->get_field_value('sys_alipay', array('type'=>1, 'status'=>0), 'id');
        $addrlist = $this->query_records('sys_alipay_address', array('sys_alipay_id'=>$id));


        $this->view->assign('list', $data);
        $this->view->assign('addr', $addrlist);
        return $this->view->fetch('daigou/alibaba/orderList/index');
    }


    /**
     * 阿里批量订单创建平台订单
     * @return void
     */
    public function aliBatchOrder(){
        //"addr":"221122"   应该还要传地址，  如果要修改的话

        $param = $this->request->param();      //{"data":[{"index":1},{"index":2}]}  表ID
        $data = $param['data'];
        $addr = $param['addr']; 

        if( count($data) > 10 ){
            return json_encode(array('code'=>'1', 'msg'=>'每次生成訂單的商品種類不能超過10個'), JSON_UNESCAPED_UNICODE);
        }

        $row_res = $this->dgUserOrder();

        // Db::name('debugging')->insert(['msg'=>$row_res,'title'=>'aliBatchOrder','createtime'=>time()]);
        if( !$row_res ){
            return json_encode(array('code'=>'1', 'msg'=>"你有壹筆未完成訂單，請先完成"), JSON_UNESCAPED_UNICODE);    
        }

        $cvalues = array_column($data, "index");
        $this->update_record('daigou_order_goods',array('id'=>array("in", implode(',', $cvalues))), array('addressId'=>$addr));

        $res = $this->daigouAliOrder($data);
        if( $res == false ){
            return json_encode(array('code'=>'1', 'msg'=>"创建平台订单失败！"), JSON_UNESCAPED_UNICODE);    
        }
        return json_encode(array('code'=>'0', 'msg'=>"成功",'id'=>$res), JSON_UNESCAPED_UNICODE);
    }


    /**
     * 批量阿里订单取消
     * @return void
     */
    public function aliBatchCancel(){
        if( $this->request->isPost() ){
            $param = $this->request->param();

            $data = $param['data'];
            $id_values = array_column($data, "index");
            $rows = $this->query_records('daigou_order_goods', array('id'=>array("in", implode(',', $id_values))));

            Db::startTrans();  
            try{
                // 检查$rows 里支付状态有 不能修改的情况
                $status_values = array_column($rows, "status");
                if (array_contain_arrayvalue($status_values, array(1,2,3,4,5,6))) {
                    return json_encode(array('code'=>'1', 'msg'=>"請選擇正確狀態下的訂單取消"), JSON_UNESCAPED_UNICODE);
                }

                // 如果这里有平台订单包含在里面，平台订单也要删除
                $row = Db::name('daigou_order')->where(['user_id'=>$this->auth->id, 'is_del'=>0])->order('id desc')->find();
                if (array_contain_arrayvalue($id_values, explode(",", $row['goods_id']))) {
                    if( $row['order_status'] > 0 ){
                        return json_encode(array('code'=>'1', 'msg'=>"特殊狀態無法修改"), JSON_UNESCAPED_UNICODE);
                    }
                    if( $row['pay_status'] > 0 ){
                        return json_encode(array('code'=>'1', 'msg'=>"包含已支付订单，无法修改"), JSON_UNESCAPED_UNICODE);
                    }
                    $this->update_record('daigou_order', array('id'=>$row['id']), array('order_status'=>3));           //已取消
                }
                $update_res = $this->update_record('daigou_order_goods', array('id'=>array("in", implode(',', $id_values))), array('status'=>5));
                Db::commit();
                return json_encode(array('code'=>'0', 'msg'=>"操作完成"), JSON_UNESCAPED_UNICODE);
            }catch(Exception $e){
            }
            Db::rollback();
            return json_encode(array('code'=>'1', 'msg'=>"操作失敗"), JSON_UNESCAPED_UNICODE);
        }

    }


    /**
     * 订单列表中 删除订单
     * @return bool|string
     */
    public function aliDelete(){
        if( $this->request->isPost() ){
            $order = $this->request->post('order');
            $row = $this->query_record('daigou_order_goods', array('orderId'=>$order));
            if( !$row ){
                return json_encode(array('code'=>'1', 'msg'=>"商品信息不存在"), JSON_UNESCAPED_UNICODE);    
            }
            $dg = Db::name('daigou_order')->where('user_id', $this->auth->id)
                                        ->where('FIND_IN_SET(:id, goods_id)', ['id'=>$row['id']])
                                        ->find();
            if($dg){
                if( $dg['pay_status'] >0 && ($dg['order_status'] ==0 || $dg['order_status'] ==2)){
                    return json_encode(array('code'=>'1', 'msg'=>"訂單當前狀態不允許刪除"), JSON_UNESCAPED_UNICODE);
                }
            }

            $res = $this->update_record('daigou_order_goods', array('orderId'=>$order), array('is_del'=>1));   //删除
            if($res){
                return json_encode(array('code'=>'0', 'msg'=>"操作成功"), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array('code'=>'1', 'msg'=>"操作失敗"), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 订单列表修改备注
     * @return bool|string
     */
    public function aliRemarks(){
        if( $this->request->isPost() ){
            $order = $this->request->post('order');
            $msg = $this->request->post('msg');

            $res = $this->update_record('daigou_order_goods', array('orderId'=>$order), array('remarks'=>$msg));
            if($res){
                return json_encode(array('code'=>'0', 'msg'=>"操作成功"), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array('code'=>'1', 'msg'=>"操作失敗"), JSON_UNESCAPED_UNICODE);
        }
    }



    /**
     * 阿里订单列表搜索
     * @return bool|string
     */
    public function aliOrderSearch(){
        if( $this->request->isPost() ){
            $time =  $this->request->post('time');
            $sch = $this->request->post('sch');

            $map=array();
            $map_or=array();
            if( !empty($time) ){
                $time_arr = explode("-", $time);
                $map['createtime'] = array('between', array($time_arr[0], $time_arr[1]));
            }

            if( !empty($sch) ){
                $map_or['orderId'] = $sch;
                $map_or['no']  = $sch;  
                $map_or['sellerLoginId']  = $sch;
            }

            $rows = Db::name('daigou_order_goods')->whereOr($map_or)->where($map)->order("id desc")->paginate(5);
            $data = $rows->toArray()['data'];
            return json_encode(array('code'=>'0', 'data'=>$data ), JSON_UNESCAPED_UNICODE); 
        }
    }











    /**
     * 判断或者删除用户存在的未完成 平台订单
     * @return bool
     */
    private function dgUserOrder(){

        //$row = $this->query_record('daigou_order',array('user_id'=>$this->auth->id, 'is_del'=>0, 'pay_status'=>array('<>', 0), 'order_status'=>array("in",array("0","2"))));
        $row = $this->query_record('daigou_order',array('user_id'=>$this->auth->id, 'is_del'=>0, 'pay_type'=>array('<>', 0), 'order_status'=>array("in",array("0","2"))));
        
        
        // $sql = Db::name('daigou_order')->getLastSql();
        // Db::name('debugging')->insert(['msg'=>$sql,'title'=>'dgUserOrder','createtime'=>time()]);
        if($row){
            return false;
        }
        
        $this->delete_record('daigou_order', array('user_id'=>$this->auth->id, 'is_del'=>0, 'pay_status'=>0,'order_status'=>array("in",array("0"))));
        return true;
    }





    private function aliOrderInfo($orderId, $offer, $addressId, $sellerOpenId){
        $html = $this->ali->orderDetails($orderId);
        $json_data = json_decode($html, true);
        if( $json_data['success']== false ){
            return false;
        }
        
        $wh_id = $this->get_field_value('sys_alipay_address', array('addressId'=>$addressId), 'wh_id');
        $wh_name = $this->get_field_value('warehouse', array('id'=>$wh_id), 'title');

        $result = $json_data['result'];
        $productItems = $result['productItems'];

        $productes = [];
        for($i=0;$i<count($productItems);$i++){
            $cvalues = array_column($productItems[$i]['skuInfos'], "value");
            $cargoNumber = implode('-', $cvalues);
            $productes[] = array(
                'cargoNumber'           =>$cargoNumber ?? ($productItems[$i]['cargoNumber'] ?? ''),             //小标题
                'itemAmount'            =>$productItems[$i]['itemAmount'],              // 总价格
                'name'                  =>$productItems[$i]['name'],                    // 大标题
                'price'                 =>$productItems[$i]['price'],                   //单价
                'productImgUrl'         =>$productItems[$i]['productImgUrl'],           //商品图片
                'quantity'              =>$productItems[$i]['quantity'],                //数量
                'subItemID'             =>$productItems[$i]['subItemID'],               //订单ID
                'productCargoNumber'    =>$productItems[$i]['productCargoNumber'] ?? '',      //标题
                'specId'                =>$productItems[$i]['specId'],                  // specID
                'gmtCreate'             =>$productItems[$i]['gmtCreate'] ?? '',                   // 创建时间
                'gmtPayExpireTime'      =>$productItems[$i]['gmtPayExpireTime'] ?? ''          // 过期时间
            );
        }

        $map = array(
            'user_id'           =>$this->auth->id,
            'no'                =>orderNo('100'),
            'orderId'           =>$result['baseInfo']['id'],
            'offer'             =>$offer,
            'sellerLoginId'     =>$result['baseInfo']['sellerLoginId'],
            'sellerOpenId'      =>$sellerOpenId,
            'buyerLoginId'      =>$result['baseInfo']['buyerLoginId'],
            'totalAmount'       =>$result['baseInfo']['totalAmount'],
            'sumProductPayment' =>$result['baseInfo']['sumProductPayment'],
            'shippingFee'       =>$result['baseInfo']['shippingFee'],
            'couponFee'         =>$result['baseInfo']['couponFee'],
            'addressId'         =>$addressId,
            'wh'                =>$wh_name,
            'modifyTime'        =>$this->ali->aliTimeToTimestamp($result['baseInfo']['modifyTime']),
            'productItems'      =>json_encode(array('data'=>$productes), JSON_UNESCAPED_UNICODE),
            'createtime'        =>time()
        );

        $id = $this->getid_insert_record('daigou_order_goods', $map);
        if($id){
            return $id;
        }
        return false;
    }



    /**
     * 阿里订单物流信息
     * @return void
     */
    public function aliLogistics(){
        $orderid = $this->request->param('no');
        $goods = $this->query_record('daigou_order_goods', array('orderId'=>$orderid));

        if(!$goods){
            return json_encode(array('code'=>'1', 'msg'=>"信息獲取失敗"), JSON_UNESCAPED_UNICODE);    
        }

        if( empty($goods['waybill']) ){
            $loginfo = $this->ali->getLogisticsInfos($orderid);
            echo json_encode($loginfo, JSON_UNESCAPED_UNICODE);
            $loginfo = json_decode($loginfo, true);
            if($loginfo['success']){
                $result = $loginfo['result'][0];
                $waybill = $result['logisticsBillNo'];
                $wbname = preg_replace("/\([^)]+\)/", "", $result['logisticsCompanyName']);
                $this->update_record('daigou_order_goods', array('orderId'=>$orderid), array('waybill'=>$waybill, 'wbname'=>$wbname));
            }else{
                return json_encode(array('code'=>'1', 'msg'=>$loginfo['errorMessage'] ), JSON_UNESCAPED_UNICODE);    
            }
        }
        //4396458493631424121
        $traceinfo = $this->ali->getLogisticsTraceInfo($orderid);
        $traceinfo = json_decode($traceinfo, true);
        if( $traceinfo['success'] == false ){
            return json_encode(array('code'=>'1', 'msg'=>$traceinfo['errorMessage'] ), JSON_UNESCAPED_UNICODE);    
        }

        $traceinfo['waybill'] = $goods['waybill'] ?? $waybill;
        $traceinfo['wbname'] = $goods['wbname'] ?? $wbname;
        return json_encode(array('code'=>'0', 'data'=>$traceinfo ), JSON_UNESCAPED_UNICODE); 
    }



    /**
     * 订单详情
     * @return bool|string
     */
    public function aliOrderDetails(){
        if($this->request->isPost()){
            $id = $this->request->param('id');      // 记录id
            $row = Db::name('daigou_order')->where('user_id', $this->auth->id)
                                        ->where('FIND_IN_SET(:id, goods_id)', ['id'=>$id])
                                        ->find();
            if(!$row){
                return json_encode(array('code'=>'1', 'msg'=>'當前訂單沒有支付詳情'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array('code'=>'0', 'msg'=>'', 'data'=>$row['order_no']), JSON_UNESCAPED_UNICODE);
        }
    }






    /////////////////////////////////////////////////////////
    // 平台订单有定时销毁





    /**
     * 再次购买--阿里
     * @return void
     */
    public function aliBuyAgain(){
        $no = $this->request->param('no');          // 流水号
        $goods = $this->query_record('daigou_order_goods', array('no'=>$no), 'offer,sellerLoginId,productItems');

        $res = $this->ali->queryProductDetail($goods['offer']);
        $data = json_decode($res, true);

        $id = $this->get_field_value('sys_alipay', array('type'=>1, 'status'=>0), 'id');
        $addrlist = $this->query_records('sys_alipay_address', array('sys_alipay_id'=>$id));

        $this->view->assign('data', $data);
        $this->view->assign('addr', $addrlist);
        $this->view->assign('list', $goods);
        return $this->view->fetch('daigou/alibaba/buyagain/index');
    }


    /**
     * 添加阿里商品收藏
     * @return bool|string
     */
    public function aliAddCollect(){
        if( $this->request->isPost() ){
            $offer = $this->request->post('offer');
            $price = $this->request->post('price');
            $images = $this->request->post('images');
            $subject = $this->request->post('subject');

            $row = $this->query_record('daigou_alicollect', array("user_id"=>$this->auth->id, "offer"=>$offer));
            if($row){
                return json_encode(array('code'=>'1', 'msg'=>'商品已經收藏過了'), JSON_UNESCAPED_UNICODE);
            }

            $data = array(
                'user_id'   =>$this->auth->id,
                'offer'     =>$offer,
                'price'     =>$price,
                'images'    =>$images,
                'subject'   =>$subject,
            );
            $id = $this->getid_insert_record('daigou_alicollect', $data);
            if($id){
                return json_encode(array('code'=>'1', 'msg'=>'商品收藏成功'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array('code'=>'1', 'msg'=>'商品收藏失败'), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 阿里商品收藏
     * @return string
     */
    public function aliCollect(){
        
        $map = array('user_id'=>$this->auth->id);                                
        $order_list = Frontend::getOrderList($map, 10, 'daigou_alicollect');   
        $data = $order_list->toArray()['data'];

        $this->view->assign('list', $data);
        return $this->view->fetch('daigou/alibaba/collect/index');
    }

    /**
     * 阿里收藏品删除
     * @return string
     */
    public function aliCollectDel(){
        if( $this->request->isPost() ){
            $param = $this->request->param();
            $data = $param['data'];  // {"data":['offer','offer']}
            $res = $this->delete_record('daigou_alicollect', array('offer'=>array("in", implode(',', $data)), 'user_id'=>$this->auth->id));
            if($res){
                return json_encode(array('code'=>'0', 'msg'=>'删除成功'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array('code'=>'1', 'msg'=>'删除失败'), JSON_UNESCAPED_UNICODE);
        }
    }
    




    /**
     * 图片转base64，解决跨域问题
    */
    public function imageProxy()
    {
        $url = $this->request->get('url');
        if (empty($url)) {
            $this->error('图片URL不能为空');
        }

        // 验证URL是否为阿里巴巴域名
        if (!preg_match('/alicdn\.com|alibaba\.com/', $url)) {
            $this->error('只允许访问阿里巴巴图片');
        }

        try {
            // 设置请求头，模拟正常浏览器访问
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer: https://www.alibaba.com/',
                'Accept: image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding: gzip, deflate, br',
                'Connection: keep-alive',
                'Upgrade-Insecure-Requests: 1'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $imageData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200 && $imageData) {
                // 设置响应头
                header('Content-Type: image/jpeg');
                header('Cache-Control: public, max-age=86400');
                header('Access-Control-Allow-Origin: *');
                echo $imageData;
            } else {
                $this->error('图片获取失败');
            }

        } catch (Exception $e) {
            $this->error('图片代理错误：' . $e->getMessage());
        }
    }


    










    // /**
    //  * 
    //  * 测试  专用接口部分
    //  * @return bool|string
    //  */
    // public function aliShoppCart(){
    //     $quantity = '8';
    //     $offerId = '714617846651';
    //     $specId = '09b4a5c219caa04129db9646ff0c962a';
    //     $addressId = '8403696725';

    //     if( $quantity <= 0 || empty($offerId) || empty($specId) || empty($addressId) ){
    //         return json_encode(array('code'=>'1', 'msg'=>'參數不完整'), JSON_UNESCAPED_UNICODE);
    //     }

    //     $cargoParam_list = [];
    //     $address_param['addressId'] = $addressId;
    //     $cargoParam_list[] = [
    //         'offerId' => $offerId,
    //         'specId' => $specId,
    //         'quantity' => $quantity,
    //     ];

    //     $config = Config::get('site.alibaba');
    //     $ali = new Alibaba($config);
    //     $res = $ali->createOrderPreview($address_param, $cargoParam_list);
    //     echo $res;
        
    //     // $js_data = json_decode($res, true);
    //     // if(  $js_data['success'] == false ){
    //     //     return json_encode(array('code'=>'1', 'msg'=>$js_data['message']), JSON_UNESCAPED_UNICODE);
    //     // }

    //     // // 生成订单的后续工作

    //     // return json_encode(array('code'=>'0', 'data'=>$res), JSON_UNESCAPED_UNICODE);
    // }

    // public function aliOrderCreate2(){
    //     $quantity = '10';
    //     $offerId = '714617846651';
    //     $specId = '09b4a5c219caa04129db9646ff0c962a';
    //     $addressId = '8403696725';

    //     if( $quantity <= 0 || empty($offerId) || empty($specId) || empty($addressId) ){
    //         return json_encode(array('code'=>'1', 'msg'=>'參數不完整'), JSON_UNESCAPED_UNICODE);
    //     }

    //     $cargoParam_list = [];
    //     $address_param['addressId'] = $addressId;
    //     $cargoParam_list[] = [
    //         'offerId' => $offerId,
    //         'specId' => $specId,
    //         'quantity' => $quantity,
    //     ];

    //     $config = Config::get('site.alibaba');
    //     $ali = new Alibaba($config);
    //     $res = $ali->createCrossOrder($address_param, $cargoParam_list);
        
    //     return $res;

    //     //{"result":{"totalSuccessAmount":4950,"orderId":"4645839854167424121","success":true,"postFee":350,"orderList":[]},"success":true}

    // }

    // public function order(){
    //     $config = Config::get('site.alibaba');
    //     $ali = new Alibaba($config);
    //     $res = $ali->orderDetails('4645839854167424121');
    //     return $res;
    // }

    // public function aliShoppCart(){
    //     if( $this->request->isPost() ){
    //         $quantity = $this->request->post('num');
    //         $offerId = $this->request->post('offer');
    //         $specId = $this->request->post('spec');
    //         $addressId = $this->request->post('addr');

    //         if( $quantity <= 0 || empty($offerId) || empty($specId) || empty($addressId) ){
    //             return json_encode(array('code'=>'1', 'msg'=>'參數不完整'), JSON_UNESCAPED_UNICODE);
    //         }

    //         $cargoParam_list = [];
    //         $address_param['addressId'] = $addressId;
    //         $cargoParam_list[] = [
    //             'offerId' => $offerId,
    //             'specId' => $specId,
    //             'quantity' => $quantity,
    //         ];

    //         $config = Config::get('site.alibaba');
    //         $ali = new Alibaba($config);
    //         $res = $ali->createOrderPreview($address_param, $cargoParam_list);
    //         $js_data = json_decode($res, true);
    //         if(  $js_data['success'] == false ){
    //             return json_encode(array('code'=>'1', 'msg'=>$js_data['message']), JSON_UNESCAPED_UNICODE);
    //         }

    //         // 生成订单的后续工作

    //         return json_encode(array('code'=>'0', 'data'=>$res), JSON_UNESCAPED_UNICODE);
    //     }


    //     return $this->view->fetch('daigou/alibaba/shoppCart/index');
    // }



    
}
