<?php



namespace app\index\controller;

use addons\wechat\model\WechatCaptcha;
use app\common\controller\Frontend;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Attachment;
use think\Config;
use think\Cookie;
use think\Hook;
use think\Session;
use think\Validate;
use think\Db;
use app\common\sdk\Alibaba;
use app\common\sdk\KuaiDiHd;
use think\Exception;



/**
 * 工具类
 */
class Tool extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = ['send','calc','keywordQuery','embargo','count'];
    protected $noNeedRight = ['*'];


    public function _initialize()
    {
        parent::_initialize();
        // $auth = $this->auth;
		// if(!$this->auth->id){
		// 	$this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		// }
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'), '/');
        }
    }




    

    
    /**
     * 1688 关键字获取多个商品信息及类目
     * @return bool|string
     */
    public function keywordQuery(){
        if( $this->request->isPost() ){
            $keyword = $this->request->post('kw');
            $page = $this->request->post('p');
            $size = $this->request->post('s');

            $config = Config::get('site.alibaba');
            $ali = new Alibaba($config);
            $res = $ali->keywordQuery($keyword, intval($page), intval($size));
            $js_data = json_decode($res, true);
            if( $js_data['result']['success'] == true ){
                $data = $js_data['result']['result']['data'];
                return json_encode(array('code'=>'0', "data"=>$data), JSON_UNESCAPED_UNICODE);    
            }
            return json_encode(array('code'=>'1', "msg"=>$js_data['result']['message']), JSON_UNESCAPED_UNICODE);
        }
    }


    /**
     * 禁运查询     , 主要是接收3级类目ID, 如果3级类目没有就传2级
     * @return void
     */
    public function embargo(){
        if( $this->request->isPost() ){
            $id = $this->request->post('id');
            $row = $this->query_record('alibaba_category', array('categoryId'=>$id), 'categoryId, chineseName, level, hk, ky, hy, judge, refer');        
            if( !$row ){
                return json_encode(array('code'=>'1', "msg"=>"沒有找到相關數據"), JSON_UNESCAPED_UNICODE);            
            }

            return json_encode(array('code'=>'0', "data"=>$row), JSON_UNESCAPED_UNICODE);                    
        }
    }

    


    public function send(){ 
        return $this->view->fetch();
    }





    public function calc(){ 
        return $this->view->fetch();
    }


    public function count(){

        $rows = Db::name('count_transport')->alias('ct')
        ->join('goodstype gt', 'ct.goodstype=gt.id', 'LEFT')
        ->join('warehouse wh', 'ct.wh_id=wh.id')
        ->field('ct.*, wh.title, gt.title as name')
        ->select();

        return json_encode(array('code'=>'0', "data"=>$rows), JSON_UNESCAPED_UNICODE); 
    }


    public function imgproxy(){
        $url = $this->request->get('url');
        return $this->aliImageProxy($url);
    }



    
}
