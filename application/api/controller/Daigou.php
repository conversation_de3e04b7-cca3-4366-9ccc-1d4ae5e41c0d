<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Exception;
use think\Config;
use think\Db;
use app\common\sdk\Alibaba;

/**
 * 示例接口
 */
class Daigou extends Api
{

    // 无需登录的接口,*表示全部
    protected $noNeedLogin = '*';
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = '*';

    protected $ali = null;


    public function __construct()
    {
        parent::__construct();

        $config = Config::get('site.alibaba');
        $this->ali = new Alibaba($config);
    }

    
    protected function get_post_data(){
        $post_data = file_get_contents('php://input', 'r' );
        $post_data = trim( $post_data,chr(239).chr(187).chr(191) );
        //$post_data = stripslashes( $post_data );
        $data = json_decode( $post_data,true );
        return $data;
    }

    protected function http_post($url,$data,$cookie=''){

        $curl = curl_init();//初始化curl模块 
        curl_setopt($curl, CURLOPT_URL, $url);//登录提交的地址 
        curl_setopt($curl, CURLOPT_HEADER, 0);//是否显示头信息 
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);//是否自动显示返回的信息 
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);//绕过ssl验证
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if(!empty($cookie)){
            curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie); //设置Cookie信息保存在指定的文件中 
        }
        curl_setopt($curl, CURLOPT_POST, 1);//post方式提交 
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);//要提交的信息 
        $result = curl_exec($curl);
        curl_close($curl);
        $res = json_encode($result, JSON_UNESCAPED_UNICODE);
        return $res;
    }

    /**
     * 获取任务
     * @return bool|string
     */
    public function getTask(){

        $map = array(
            'pay_status'    =>array('>=', 2),
            'order_status'  =>0,
            'is_del'        =>0
        );

        $row = Db::name('daigou_order')->where($map)->order('id desc')->find();
        if(!$row){
            return json_encode(array('code'=>1, 'msg'=>'暂无任务'), JSON_UNESCAPED_UNICODE);
        }
        $goods = Db::name('daigou_order_goods')->where(array('id'=>array('in', $row['goods_id'])))->select();
        $orders = array_column($goods, "orderId");

        $json_res = $this->ali->alipayUrlGet($orders);
        $ali_data = json_decode($json_res,true);
        if($ali_data['success'] == false){
            return json_encode(array('code'=>1, 'msg'=>$ali_data['erroMsg']), JSON_UNESCAPED_UNICODE);
        }

        $data = array(
            'code'      =>0,
            'no'        =>$row['order_no'],
            'price'     =>$row['totalAmount'],
            'orderlist' =>$orders,
            'payUrl'    =>$ali_data['payUrl'],
        );

        Db::name('daigou_order')->where('id', $row['id'])->update(array('updatetime'=>time()));
        Db::name('daigou_order_goods')->where('id', 'in', $row['goods_id'])->update(['status'=>10]);
        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }


    /**
     * 支付结果
     * @return void
     */
    public function payResult(){
        $data = $this->get_post_data();
        if( empty($data) ){
            return  json_encode(array('code'=>1,'msg'=>'数据错误'), JSON_UNESCAPED_UNICODE);   
        }

        if( $data['code'] == 1){
            // 支付失败转人工
            if( $data['status'] == '支付失败' ){
                $order_res = Db::name('daigou_order')->where('order_no', $data['no'])->update(['order_status'=>2]);    
                return  json_encode(array('code'=>1,'msg'=>'已转人工'), JSON_UNESCAPED_UNICODE);
            }

            // 支付失败退款  关闭订单




        }
        // 支付成功
        $order = Db::name('daigou_order')->where('order_no', $data['no'])->find();
        if( empty($order) ){
            return json_encode(array('code'=>1,'msg'=>'数据错误'), JSON_UNESCAPED_UNICODE);   
        }
        $order_res = Db::name('daigou_order')->where('order_no', $data['no'])->update(['order_status'=>1]);
        $goods_res = Db::name('daigou_order_goods')->where('id', 'in', $order['goods_id'])->update(['status'=>1]);

        if( $order_res && $goods_res){
            return  json_encode(array('code'=>0,'msg'=>'成功支付-更新成功'), JSON_UNESCAPED_UNICODE);
        }
        return  json_encode(array('code'=>1,'msg'=>'成功支付-更新失败'), JSON_UNESCAPED_UNICODE);
    }




}
