<?php

return array(
    'name' => '我的网站',
    'beian' => '',
    'cdnurl' => '',
    'version' => '1.0.3',
    'timezone' => 'Asia/Shanghai',
    'forbiddenip' => '',
    'languages' =>
        array(
            'backend' => 'zh-cn',
            'frontend' => 'zh-cn',
        ),
    'fixedpage' => 'dashboard',
    'categorytype' =>
        array(
            'default' => 'Default',
            'page' => 'Page',
            'article' => 'Article',
            'test' => 'Test',
        ),
    'configgroup' =>
        array(
            'basic' => 'Basic',
            'email' => 'Email',
            'dictionary' => 'Dictionary',
            'user' => 'User',
            'example' => 'Example',
        ),
    'mail_type' => '1',
    'mail_smtp_host' => 'smtp.qq.com',
    'mail_smtp_port' => '465',
    'mail_smtp_user' => '',
    'mail_smtp_pass' => '',
    'mail_verify_type' => '2',
    'mail_from' => '',
    'attachmentcategory' =>
        array(
            'category1' => 'Category1',
            'category2' => 'Category2',
            'custom' => 'Custom',
        ),


    'tax'  =>
        array(
            1   => 'X1 不包稅',
            2   => 'X2 包頻繁稅',
            3   => 'X3 全包稅'
        ),


    'wallettype' =>
        array(
            1 => '添加服务',
            2 => '钱包充值',
            3 => '集运订单',
            4 => '',
            5 => '',
            6 => '',
            7 => '',
            8 => '退款',
            9 => '系统减少',
            10 => '系统增加'
        ),

    'category' =>
        array(
            'recharge' => '充值',
            'jyorder' => '集运',
            'addservice' => '服务',
        ),

    'ordertype' =>
        array(
            'recharge' => 1,
            'jyorder' => 2,
        ),

    'pay_type'  =>
        array(
            1   => '銀行',
            2   => '超商',
            3   => '台灣PAY'
        ),
    
    //支付状态
    'pay_status'  =>
        array(
            0   => '未支付',
            1   => '部分支付',
            2   => '已支付',
            3   => '多支付',
        ),
    
    // 服务详情状态
    'service_detail_status'  =>
        array(
            0   => '進行中',
            1   => '已完成',
        ),


    'cz_order_status'   =>
        array(
            0   => '待轉賬',
            1   => '完成',
            2   => '轉人工',
        ),

    'transport'         =>
        array(
            1   => '海快',
            2   => '空運',
            3   => '海運',
        ),
    
    'jiyunmap'          =>
        array(
            0   => '集运说明',
            1   => '海快说明',
            2   => '空运说明',
            3   => '海运说明',
        ),

    'notice'          =>
        array(
            1   => '通知公告',
            2   => '集運説明',
            3   => '代購相關',
            4   => '常見Q&A',
            5   => '會員權益',
            38  => '滾動公告',
        ),   

    'notice_chunk'          =>
        array(
            1   => '通知公告',
            2   => '集運説明',
            3   => '代購相關',
            4   => '常見Q&A',
        ),

    'help'          =>
        array(
            1   => '會員權益',
            2   => '集運説明',
            3   => '代購相關',
            4   => '通知公告',
            5   => '常見Q&A',
            38  => '滾動公告',
        ), 

    'dgali_status'          =>
        array(
            0   => '待付款',
            1   => '已付款',
            2   => '已出貨',
            3   => '已收貨',
            4   => '已退款',
            5   => '已取消',
            6   => '已關閉',
            7   => '售後中',              
        ),     


    'alibaba' => array(
        'key' => '2733123',
        'secret' => 'WB9aETNHB9Dj',
        'token' => '3a74d381-7bab-41b6-a33d-09cf946c34be',
    ),

    'kuaidi100' => array(
        'key' => 'SPPgHIwt2937',
        'customer' => 'BFE3271FF1FC5FD40DF892C6B679C089',
        'secret' => '8ec312ea0f694700b5b7c1389db9b9f5',
    ),
    'line_config' => [
        'appid' => '2007624452',
        'secret' => '61a78053d90919a5acd27dc110ab8d1e',
        'redirect_uri' => 'https://www.ezfiow.com/index/third_party/line_callback',
    ],

    'ktj'  =>array(
        '00'     => '收件人不在',
        '01'     => '運輸商已接收貨件',
        '02'     => '配送完成',
        '03'     => '將重新安排配送站所',
        '04'     => '取消託運資料修改',
        '05'     => '收件人已收取部分貨件',
        '06'     => '收件人因缺件拒絕收件',
        '08'     => '運輸商配送至站所，收件人未領取',
        '09'     => '運輸商配送至站所，收件人已領取',
        '10'     => '收件人拒絕收件',
        '11'     => '收件人因貨件破損拒絕收件',
        '12'     => '運輸商因貨件破損未安排配送',
        '13'     => '收件人地址改變',
        '14'     => '貨件保留在配送站所，待寄件人/收件人通知後配送',
        '15'     => '貨件海關扣押中',
        '16'     => '因無採購單號，無法進行配送',
        '17'     => '貨件全部未抵達配送站所',
        '18'     => '貨件遺失',
        '19'     => '與收件人另約時間配送',
        '22'     => '指定收件人不在，無法完成配送',
        '24'     => '收件人與運輸商約定至站所收取貨件',
        '25'     => '節日後在安排配送',
        '26'     => '寄件人/收件人要求更改收件地址',
        '29'     => '收件地址錯誤',
        '30'     => '配送完成',
        '31'     => '雖件數有少，但已交由運輸商夥伴配送',
        '32'     => '因件數有少，故尚未交由運輸商夥',
        '33'     => '已交由運輸商夥伴配送，現保存在站所內',
        '34'     => '因貨件破損，尚未交由運輸商夥伴配送',
        '35'     => '收件人已收取運輸商夥伴配送之部分貨件',
        '36'     => '收件人已收取運輸商夥伴配送之全部貨件',
        '37'     => '運輸商已將貨件轉交運輸商夥伴',
        '38'     => '配送完成',
        '39'     => '已交由運輸商夥伴配送，尚未配送',
        '40'     => '無此收件人',
        '41'     => '轉運中心處理貨件中',
        '42'     => '收件公司已停止營業',
        '43'     => '因事故發生，警方查扣貨件中',
        '45'     => '轉運中心處理貨件中',
        '46'     => '聯繫收件人，但無人接聽',
        '49'     => '運輸商處理貨件中',
        '50'     => '貨件破損，送至貨故管理中心',
        '51'     => '運輸商海運夥伴收到貨件處理中',
        '52'     => '運輸商空運夥伴收到貨件處理中',
        '53'     => '貨件退回發貨站所',
        '54'     => '貨件交至配合統倉',
        '55'     => '運輸商海運夥伴收到貨件處理中',
        '56'     => '運輸商空運夥伴收到貨件處理中',
        '57'     => '運輸商改由其他配送站所配送',
        '58'     => '貨件退回發貨站所',
        '59'     => '貨件已由配合統倉驗收入倉',
        '62'     => '收件人已收取未配送之貨件',
        '63'     => '託運件數更正',
        '64'     => '無人認領，丟棄處置',
        '65'     => '部分貨件破損並退回發貨站所',
        '66'     => '超過託運處理期限',
        '67'     => '運輸商同業轉交配送，但退回同業',
        '68'     => '託運資訊更正取消',
        '69'     => '收件人收取部分貨件並退回部分貨件',
        '72'     => '配送門市完成',
        '73'     => '司機配送中',
        '74'     => '運輸商轉由子公司配送中',
        '75'     => '配送安排錯誤，將重新安排',
        '76'     => '司機安排配送路線中',
        '77'     => '貨件送回寄件人',
        '78'     => '收件人已至門市取件',
        '79'     => '收件人超過 7 天未至門市取件',
        '82'     => '運輸商部分貨件有缺',
        '83'     => '運輸商重新安排配送中',
        '84'     => '運輸商重新安排配送中',
        '85'     => '運輸商夥伴確認貨件已到港口',
        '86'     => '貨件已到運輸商夥伴站所',
        '87'     => '運輸商已將貨件轉交運輸商夥伴',
        '88'     => '貨件已由運輸商合作夥伴配送中',
        '89'     => '貨件破損，貨故處理中',
        '90'     => '由運輸商夥伴轉運，貨件已到配送轉運中心',
        '91'     => '運輸商將貨件轉由集團子公司接收',
        '92'     => '運輸商將貨件轉由集團子公司配送',
        '93'     => '運輸商子公司嘉里快遞已收到貨件',
        '94'     => '轉回運輸商配送',
        '95'     => '運輸商夥伴轉回貨件',
        '96'     => '收件人未至運輸商夥伴站所收件',
        '97'     => '貨件已由配合統倉驗退',
        '99'     => '貨件處理中',
        'A1'     => '託運資料已輸入系統',
        'A2'     => '貨件已到配送站所',
        'A3'     => '貨件保存在運輸商站所',
        'A4'     => '貨件外部無託運標籤資訊',
        'B1'     => '配送物流櫃完成',
        'B2'     => '收件人超過期限未至物流櫃取件',
        'B3'     => '運輸商依需求取回物流櫃貨件',
        'F0'     => '貨件預計轉交全家門市',
        'F1'     => '貨件已交至全家門市',
        'F2'     => '收件人超過期限未至門市取件退回',
        'F3'     => '運輸商依需求取回全家門市貨件',
    ),











);
