<?php

namespace app\admin\model\base;

use think\Model;


class Contact extends Model
{

    

    

    // 表名
    protected $name = 'contact';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function warehouse()
    {
        return $this->belongsTo('app\admin\model\Warehouse', 'wh_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
