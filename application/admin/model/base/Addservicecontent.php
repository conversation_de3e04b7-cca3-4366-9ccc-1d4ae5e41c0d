<?php

namespace app\admin\model\base;

use think\Model;


class Addservicecontent extends Model
{

    

    

    // 表名
    protected $name = 'addservice_content';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function type()
    {
        return $this->belongsTo('app\admin\model\addservice\Type', 'aid', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
