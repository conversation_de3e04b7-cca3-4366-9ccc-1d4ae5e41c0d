<?php

namespace app\admin\model\order;

use think\Model;


class Package extends Model
{

    

    

    // 表名
    protected $name = 'package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'arrivetime_text'
    ];
    

    



    public function getArrivetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['arrivetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setArrivetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function warehouse()
    {
        return $this->belongsTo('app\admin\model\base\Warehouse', 'wh_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function addservice()
    {
        return $this->belongsTo('app\admin\model\order\Addservice', 'addser_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function goodstype()
    {
        return $this->belongsTo('app\admin\model\base\Goodstype', 'goodstype_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function twlogistics()
    {
        return $this->belongsTo('app\admin\model\base\Twlogistics', 'twe_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
