<?php

namespace app\admin\model\order;

use think\Model;


class Jyorder extends Model
{

    

    

    // 表名
    protected $name = 'jyorder';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function package()
    {
        return $this->belongsTo('app\admin\model\order\Package', 'pid', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function coupon()
    {
        return $this->belongsTo('app\admin\model\user\Coupon', 'user_coupon_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function trade()
    {
        return $this->belongsTo('app\admin\model\order\Banktrade', 'bank_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
