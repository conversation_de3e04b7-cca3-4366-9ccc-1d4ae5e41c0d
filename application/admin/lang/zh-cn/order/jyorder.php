<?php

return [
    'Id'                 => 'id',
    'Pid'                => '包裹表ID',
    'Order_no'           => '订单编号',
    'User_id'            => '用户ID',
    'Num'                => '总包裹数',
    'Scale'              => '总重量',
    'Volume'             => '总材积',
    'Volumetwo'          => '总材积二',
    'Dispatch'           => '派件费',
    'Seaway'             => '海运条件0否1是',
    'Prot_type'          => '保险类别0不要1超时险2保价服务',
    'Prot_money'         => '保险金额',
    'Tb_money'           => '订单总金额',
    'Bal_money'          => '抵扣金',
    'User_coupon_id'     => '用户优惠券ID',
    'Actual_money'       => '实际需支付',
    'Rec_money'          => '实际收款',
    'Outer_island'       => '外岛总费用',
    'Pay_status'         => '支付方式默认1银行',
    'Pay_type'           => '支付状态0未支付1部分支付2已支付3多支付',
    'Bank_id'            => '银行流水ID',
    'Bank_num'           => '银行后6码',
    'Order_status'       => '0未发货1已发货',
    'Address_json'       => '地址记录',
    'Is_invoice'         => '是否开发票0否1是',
    'Invoice_id('        => '发票ID',
    'Remarks'            => '备注',
    'Createtime'         => '创建时间',
    'Updatetime'         => '更新时间',
    'Package.entrust_no' => '委托单号',
    'User.username'      => '用户名',
    'Coupon.title'       => '优惠卷名称',
    'Trade.id'           => 'id',
    'Trade.bank_title'   => '银行名称',
    'Trade.account'      => '银行账号',
    'Trade.in_account'   => '入賬銀行賬號',
    'Trade.version'      => '版本',
    'Trade.date_time'    => '交易时间',
    'Trade.taken'        => '取款金额',
    'Trade.save'         => '存款金额',
    'Trade.balance'      => '余额',
    'Trade.abstract'     => '存取款方式',
    'Trade.remark'       => '备注',
    'Trade.admin_id'     => '操作人ID',
    'Trade.remarks'      => '备注',
    'Trade.user_id'      => '用户ID',
    'Trade.jorder_id'    => '集运订单ID',
    'Trade.rorder_id'    => '充值订单ID',
    'Trade.is_normal'    => '是否转人工0否1是',
    'Trade.createtime'   => '创建时间',
    'Trade.updatetime'   => '更新时间'
];
