<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pid" data-rule="required" class="form-control" name="row[pid]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_no" data-rule="required" class="form-control" name="row[order_no]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Scale')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-scale" data-rule="required" class="form-control" step="0.01" name="row[scale]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Volume')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-volume" data-rule="required" class="form-control" step="0.01" name="row[volume]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Volumetwo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-volumetwo" data-rule="required" class="form-control" step="0.01" name="row[volumetwo]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Dispatch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-dispatch" data-rule="required" class="form-control" name="row[dispatch]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Seaway')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-seaway" data-rule="required" class="form-control" name="row[seaway]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prot_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-prot_type" data-rule="required" class="form-control" name="row[prot_type]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prot_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-prot_money" data-rule="required" class="form-control" name="row[prot_money]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tb_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tb_money" data-rule="required" class="form-control" name="row[tb_money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bal_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bal_money" data-rule="required" class="form-control" name="row[bal_money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_coupon_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_coupon_id" data-rule="required" data-source="user/coupon/index" class="form-control selectpage" name="row[user_coupon_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Actual_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-actual_money" data-rule="required" class="form-control" name="row[actual_money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rec_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rec_money" data-rule="required" class="form-control" name="row[rec_money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Outer_island')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-outer_island" data-rule="required" class="form-control" name="row[outer_island]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_status" data-rule="required" class="form-control" name="row[pay_status]" type="number" value="1">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_type" data-rule="required" class="form-control" name="row[pay_type]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_id" data-rule="required" data-source="bank/trade/index" class="form-control selectpage" name="row[bank_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_num" data-rule="required" class="form-control" name="row[bank_num]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_status" data-rule="required" class="form-control" name="row[order_status]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[address_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[address_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_invoice')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_invoice" data-rule="required" class="form-control" name="row[is_invoice]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Invoice_id(')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-invoice_id(" data-rule="required" class="form-control" name="row[invoice_id(]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remarks')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remarks" data-rule="required" class="form-control" name="row[remarks]" type="text">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
