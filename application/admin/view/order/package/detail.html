<style type="text/css">

    body .common_table tr th{ background:#F9F9F9; text-align: center; color: #333333; border-bottom: 1px solid #EDEDED; font-size: 14px; font-weight: normal; line-height: 34px; border-top: none }
    body .common_table tr th.l{ text-align: left; }
    body .common_table tr th.r{ text-align: right; }
    body .common_table tr td{ border-top: none; border-bottom: 1px solid #EDEDED; line-height: 34px; }
    .color_green{ color: #1BCBA9 }

    .detail .title{ padding-top: 10px; line-height: 38px; font-size:16px; font-weight: bold; border-bottom: 2px solid #EF436D  }
    body .common_table{border: 1px solid #EDEDED;overflow: hidden;}
    .margin_t{ margin-top: 10px; }
    body .color_red{ color: #ef436d }
    .text-center{ text-align: center; }
    body .color_666{ color: #666 }
    .f12{ font-size: 12px }
</style>




<div class="detail">
    <div class="title">
        {:__('Package details')}
    </div>
    <table class="common_table table margin_t10 text-center">
        <tbody>
        <tr>
            <th colspan="2" class="l">用戶資訊</th>
        </tr>
        <tr>
            <td>姓名：<?=$row['username']?></td>
            <td>手機號：<?=$row['mobile']?></td>
        </tr>
        </tbody>
    </table>


    <table class="common_table table margin_t10 text-center">
        <tbody>
        <tr>
            <th colspan="2" class="l">包裹基本信息</th>
        </tr>
        <tr>
            <td>委托单号：</td><td><?=$row['entrust_no']?></td>
        </tr>
        <tr>
            <td>快递名称：</td><td><?=$row['wbill_name']?></td>
        </tr>
        <tr>
            <td>快递单号：</td><td><?=$row['waybill']?></td>
        </tr>
        <tr>
            <td>商品名称： </td><td><?=$row['goods_name']?></td>
        </tr>
        <tr>
            <td>商品网址： </td><td><?=$row['goods_url']?></td>
        </tr>
        <tr>
            <td>商品数量： </td><td><?=$row['num']?></td>
        </tr>
        <tr>
            <td>商品重量： </td><td><?=$row['scale']?></td>
        </tr>
        <tr>
            <td>商品材积： </td><td><?=$row['volume']?></td>
        </tr>
        <tr>
            <td>长度： </td><td><?=$row['length']?></td>
        </tr>
        <tr>
            <td>宽度： </td><td><?=$row['width']?></td>
        </tr>
        <tr>
            <td>高度： </td><td><?=$row['height']?></td>
        </tr>
        <tr>
            <td>单价： </td><td><?=$row['unit_price']?></td>
        </tr>
        <tr>
            <td>仓库：</td><td><?=$row['wtitle']?></td>
        </tr>
        <tr>
        <?php if($row['transport'] == 0){ ?>
            <td>运输方式：</td><td>无</td>
        <?php }else{ ?>
			<td>运输方式：</td><td><?= ($row['transport']==16?'空运':($row['transport']==17?'海运':'海快')) ?></td>				
        <?php } ?>
        </tr>
        <tr>
            <td>货物类型：</td><td><?=$row['gtitle']?></td>
        </tr>
        <tr>
            <td>用户备注：</td><td><?=$row['uremarks']?></td>
        </tr>
        <tr>
            <td>入库时间：</td><td><?=$row['arrivetime']?></td>
        </tr>
        <tr>
            <td>是否拒收：</td><td><?= ($row['refuse']==0?'否':'是') ?></td>
        </tr>
        <tr>
            <td>台湾快递：</td><td><?=$row['twe_id']?></td>
        </tr>
        <tr>
            <td>物流单号：</td><td><?=$row['bill_no']?></td>
        </tr>
        </tbody>
    </table>

    <?php
		if($row['addser_id']>0){
	?>
    <table class="common_table table margin_t10 text-center">
        <tbody>
        <tr>
            <th colspan="2" class="l">额外服务</th>
        </tr>
        {volist name="serarr" id="item"  }
        <tr>
            <tr>
                <td>项目：<?=$item['title']?></td>
                <td>金额：<?=$item['amount']?></td>
            </tr>
        </tr>
        {/volist}
        <tr>
            <td>单号：</td><td><?=$row['order_no']?></td>
        </tr>
        <tr>
            <td>总金额：</td><td><?=$row['tb_money']?></td>
        </tr>
        <tr>
            <td>抵扣金：</td><td><?=$row['bal_money']?></td>
        </tr>
        <tr>
            <td>实际支付：</td><td><?=$row['actual_money']?></td>
        </tr>
        <tr>
            <td>支付状态：</td><td><?= ($row['pay_type']==0?'未支付':'已支付') ?></td>
        </tr>
        <tr>
            <td>订单状态：</td><td><?= ($row['order_status']==0?'未完成':'已完成') ?></td>
        </tr>
        </tbody>
    </table>
    <?php
		}
	?>


















</div>







