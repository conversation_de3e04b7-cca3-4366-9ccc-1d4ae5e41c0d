<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" data-rule="required" class="form-control" name="row[type]" type="number" value="{$row.type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Textual')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-textual" data-rule="" class="form-control" name="row[textual]" type="text" value="{$row.textual|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Scale')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-scale" data-rule="required" class="form-control" name="row[scale]" type="number" value="{$row.scale|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Single')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-single" data-rule="required" class="form-control" name="row[single]" type="number" value="{$row.single|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Two')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-two" data-rule="required" class="form-control" name="row[two]" type="number" value="{$row.two|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total" data-rule="required" class="form-control" name="row[total]" type="number" value="{$row.total|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Count_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-count_value" data-rule="required" class="form-control" name="row[count_value]" type="number" value="{$row.count_value|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Limit_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-limit_value" data-rule="required" class="form-control" name="row[limit_value]" type="number" value="{$row.limit_value|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Over_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-over_price" data-rule="required" class="form-control" name="row[over_price]" type="number" value="{$row.over_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Base_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-base_money" data-rule="required" class="form-control" name="row[base_money]" type="number" value="{$row.base_money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="{$row.status|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
