<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wh_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wh_id" data-rule="required" data-source="base/counttransport/get_wh" class="form-control selectpage" name="row[wh_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transport')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transport" data-rule="required" data-source="base/counttransport/get_transport" class="form-control selectpage" name="row[transport]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Minday')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-minday" data-rule="" class="form-control" name="row[minday]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Maxday')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-maxday" data-rule="" class="form-control" name="row[maxday]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goodstype')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goodstype" data-rule="required" data-source="base/counttransport/get_goodstype" class="form-control selectpage" name="row[goodstype]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" data-rule="" class="form-control" name="row[type]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Style')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-style" data-rule="" class="form-control" name="row[style]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Scale_unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-scale_unit" data-rule="required" class="form-control" name="row[scale_unit]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Volume_unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-volume_unit" data-rule="" class="form-control" name="row[volume_unit]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Volume')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-volume" data-rule="" class="form-control" name="row[volume]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fr_weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fr_weight" data-rule="" class="form-control" name="row[fr_weight]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fl_weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fl_weight" data-rule="" class="form-control" name="row[fl_weight]" type="number">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
