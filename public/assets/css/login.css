/* 登录和注册页样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.fs12 {
    font-size: 12px;
}
.fs13 {
    font-size: 13px;
}

.red {
    color: #EF436D;
}
.fff {
    color: #fff;
}

.pointer {
    cursor: pointer;
}

.underline {
    text-decoration: underline;
}

.mt24 {
    margin-top: 24px;
}
body {
    background-color: #ffffff;
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

.nav {
    width: 100%;
    height: 64px;
    background: #000000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 360px;
    position: relative;
    z-index: 1000;
    /* gap: 700px; */
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 54px;
    font-size: 14px;
    color: #EF436D;
}


/* Process section styles */
.process {
    width: 100vw;
    min-height: 532px;
    background-color: #f9f9f9;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Tab styles */
.process-tabs {
    display: flex;
    width: 1200px;
    justify-content: flex-start;
    align-items: center;
    gap: 48px;
    height: 52px;
    margin: 48px 0 32px 0;
}

.process-tabs .tab {
    font-size: 30px;
    color: #666;
    cursor: pointer;
    position: relative;
}

.process-tabs .tab.active {
    color: #333333;
    font-size: 36px;
}

.process-tabs .tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 70px;
    height: 4px;
    background-color: #EF436D;
    border-radius: 4px;
}

/* Process flow styles */
.process-content {
    background-color: #fff;
    border-radius: 16px;
    width: 1200px;
    height: 312px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.process-flow {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    margin: 0 auto;
    gap: 60px;
}

/* 专为代购流程的6个步骤优化的样式 */
#purchase-process {
    gap: 35px;
}

#purchase-process .process-step {
    width: 150px;
}

#purchase-process .step-image {
    width: 108px;
    height: 108px;
    border-radius: 50%;
    background-color: #F9F9F9;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

#purchase-process .step-image img {
    width: 72px;
    height: 72px;
}

#purchase-process .step-arrow {
    right: -26px;
}

#purchase-process .step-title {
    font-size: 16px;
    color: #333333;
}

#purchase-process .step-number-line {
    width: 144px;
    height: 1px;
    background-color: #E7E7E7;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: calc(100% + 8px);
}

.process-more {
    margin-top: 24px;
    height: 32px;
    width: 88px;
    background-color: #EF436D;
    color: #fff;
    font-size: 13px;
    text-align: center;
    line-height: 32px;
    border-radius: 4px;
    cursor: pointer;
}

.process-more:hover {
    background-color: #e13a5c;
}

.process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 180px;
    cursor: pointer;
}

.step-image {
    width: 108px;
    height: 108px;
    margin-bottom: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.step-image img {
    width: 100%;
    height: 100%;
}

.step-arrow {
    position: absolute;
    top: 38px;
    right: -36px;
    width: 16px;
    height: 32px;
    z-index: 1;
}

.step-arrow img {
    width: 100%;
    height: 100%;
}

.process-step:last-child .step-arrow {
    display: none;
}

.step-number {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: 1px solid #EF436D;
    background-color: #ffffff;
    color: #EF436D;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    margin-bottom: 4px;
    position: relative;
    z-index: 2;
}

.step-number-line {
    width: 144px;
    height: 1px;
    background-color: #E7E7E7;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: calc(100% + 30px);
}

.step-title {
    font-size: 16px;
    text-align: center;
    color: #3D3D3D;
    max-width: 150px;
}


/* 头像和用户信息 */
.avatar-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.avatar {
    width: 27px;
    height: 32px;
    margin-right: 12px;
    flex-shrink: 0;
}

.avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.name {
    font-size: 14px;
    color: #333333;
    font-weight: 500;
}

.time {
    font-size: 12px;
    color: #999999;
    margin-top: 2px;
}

.comment-count {
    font-size: 12px;
    color: #999999;
    white-space: nowrap;
}

/* 评分星星 */
.rating {
    display: flex;
    margin-bottom: 10px;
}

.rating img {
    width: 10px;
    height: 9px;
    margin-right: 3px;
}

/* 评论文本 */
.comment-text {
    font-size: 14px;
    line-height: 1.5;
    color: #333333;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* EZ集運通优势展示项 */
.jiyun-show{
    width: 100vw;
    min-height: 556px;
    background-color: #F9F9F9;
    padding: 48px 0;
}

.jiyun-show-title {
    margin: 0 auto;
    width: 1200px;
    text-align: start;
    font-size: 36px;
    color: #333333;
    text-align: start;
    margin-bottom: 32px;
    position: relative;
}

.jiyun-show-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 70px;
    height: 4px;
    background-color: #EF436D;
    border-radius: 4px;
} 

.jiyun-show-list {
    margin: 0 auto;
    width: 1200px;
    height: 376px;
    background-color: #F9F9F9;
    display: flex;
    justify-content: space-between;
}

.jiyun-show-item {
    width: 280px;
    height: 376px;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.jiyun-icon {
    width: 200px;
    height: 200px;
    margin-bottom: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.jiyun-icon img {
    width: 200px;
    height: 200px;
    object-fit: contain;
}

.jiyun-item-title {
    font-size: 32px;
    font-weight: 500;
    color: #3D3D3D;
}

.jiyun-item-divider {
    width: 88px;
    height: 1px;
    background: rgba(234, 85, 20, 0.3);
    margin: 30px 0 24px 0;
}

.jiyun-item-desc {
    width: 250px;
    font-size: 14px;
    color: #333333;
    line-height: 1.4;
    text-align: center;
}

.footer {
    background: #272727;
    width: 100vw;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 32px;
}

.footer .up {
    font-size: 14px;
    color: #FFFFFF;
    width: 1200px; 
    height: 348px;
    background-color: #272727;
}

.footer .up .top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.footer-column {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.footer-title {
    font-size: 16px;
    color: #ffffff;
    font-weight: 500;
    height: 24px;
    margin-bottom: 24px;
}
 
.footer-link { 
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 16px;
    transition: color 0.3s ease;
}

.footer-link:hover {
    text-decoration: underline;
    color: #ef436d;
}

.qrcode-column {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.qr-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    width: 80px;
    height: 104px;
    overflow: hidden;
    border: 1px solid #fff;
}

.qr-badge {
    background-color: #EF436D;
    width: 100%;
    height: 24px;
    color: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 24px;
    object-fit: contain;
}

.line-badge {
    background-color: #00c300;
}

.qr-code {
    background-color: #272727;
    width: 100%;
    padding: 4px;
}

.qr-code img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.qr-text {
    background-color: #ffffff;
    width: 70px;
    height: 100%;
    font-size: 12px;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: center;
}

.working-info {
    margin: 24px 0;
    padding: 0 48px;
    width: 1200px;
    height: 64px;
    background: #525252;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    border-radius: 8px;
}

.working-time {
    display: flex;
    align-items: center;
    font-size: 16px;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 30px;
}

.contact-link {
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.contact-icon {
    width: 22px;
    height: 22px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-icon img {
    width: 12px;
    height: 12px;
}

.contact-icon i {
    color: #525252;
    font-size: 16px;
}

.footer .line {
    width: 100%;
    margin: 0 auto;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
}   

.footer-bottom {
    width: 1200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.add-line-btn {
    width: 88px;
    height: 24px;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
}

.add-line-btn img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.footer .down {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #272727;
    color: #FFFFFF;
    font-size: 13px;
}

.terms-links {
    color: #fff;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.terms-link {
    text-decoration: none;
    color: #fff;
}

.terms-link:hover {
    color: #ef436d;
    text-decoration: underline;
}

.divider {
    color: #fff;
}

.security {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    font-size: 13px;
}

.ssl-badge {
    width: 36px;
    height: 36px;
}

#content-container {
    margin: 0 auto;
    width: 1200px;
    min-height: 100%;
    display: flex;
    align-items: center;
    /*justify-content: center;*/
    background: #ffffff;
}

#register-container {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
}


.bottom-navigation-bar {
    width: 64px;
    position: fixed;
    bottom: 120px;
    right: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    z-index: 999;
}

.bottom-bar-group {
    width: 48px;
    height: 116px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    border-radius: 303px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.bottom-bar-group-line {
    background-color: #D8D8D8;
    width: 20px;
    height: 1px;
}

.bottom-navigation-bar .first-bar {
    width: 48px;
    height: 48px;
    background: #00C300;
    border-radius: 50%;
    box-shadow: 0px 4px 10px 0px rgba(0, 195, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.bottom-navigation-bar .second-bar {
    width: 24px;
    height: 24px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; /* 添加相对定位 */
}

.bottom-navigation-bar .third-bar {
    width: 24px;
    height: 24px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 红色消息气泡样式 */
.notification-bubble {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background-color: #EF436D;
    color: white;
    font-size: 10px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* QR码容器样式 */
.qrcode-container {
    position: fixed;
    right: calc(30px + 64px + 6px); /* 右侧固定位置 = 页面右侧边距 + bottom-navigation-bar宽度 + 间距 */
    bottom: 122px;
    width: 300px;
    height: 324px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.12);
    z-index: 9998;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 28px;
}

.qrcode-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.qrcode-title {
    color: #EF436D;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 6px;
    text-align: center;
}

.qrcode-subtitle {
    color: #3D3D3D;
    font-size: 13px;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.4;
}

.qrcode-image {
    width: 220px;
    height: 220px;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.qrcode-image img {
    max-width: 100%;
    max-height: 100%;
}

.qrcode-footer {
    color: #666;
    font-size: 14px;
    text-align: center;
}

.user-section.login-section {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.10);
    width: 400px;
    padding: 0;
    margin: 0 auto;
    position: relative;
}

.app-download {
    width: 18px;
    height: 18px;
}

.login-message {
    display: flex; 
    align-items: center; 
    gap: 8px;
    position: relative;
}

.login-message img {
    width: 20px;
    height: 20px;
}

.login-message-num {
    position: absolute;
    top: -2px;
    left: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    font-size: 8px;
    background-color: #EF436D;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-section .logon-tab {
    display: flex;
    border-bottom: 1px solid #f2f2f2;
    margin: 0;
    padding: 0 0 0 0;
}

.login-section .logon-tab a {
    flex: 1;
    text-align: center;
    padding: 24px 0 12px 0;
    font-size: 20px;
    color: #888;
    background: none;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    transition: color 0.2s, border-color 0.2s;
}

.login-section .logon-tab a.active {
    color: #222;
    border-bottom: 2px solid #f44b6a;
    background: none;
}

.login-section .login-main {
    padding: 0 36px;
}

.login-section .form-group {
    margin-bottom: 16px;
}

.login-section .form-group label {
    font-size: 15px;
    color: #222;
    margin-bottom: 8px;
    font-weight: 500;
}

.login-section .form-control {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
    font-size: 15px;
    padding: 0 14px 0 36px;  /* 增加左侧padding给icon留空间 */
    background: #fafbfc;
    transition: border-color 0.2s;
}

.login-section .form-control:focus {
    border-color: #f44b6a;
    background: #fff;
    outline: none;
}

.login-section .help-block {
    color: #f44b6a;
    font-size: 13px;
    margin-top: 4px;
    min-height: 18px;
}

.login-section .checkbox.inline label {
    font-size: 14px;
    color: #888;
    font-weight: 400;
}

.login-section .checkbox.inline input[type="checkbox"] {
    accent-color: #f44b6a;
    margin-right: 4px;
}

.login-section .pull-right {
    float: right;
}

.login-section .btn-forgot {
    color: #888;
    font-size: 14px;
    text-decoration: underline;
    transition: color 0.2s;
}

.login-section .btn-forgot:hover {
    color: #f44b6a;
}

/* 按钮样式 */
.btn {
    width: 100%;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 500;
    height: 48px;
    border: none;
    cursor: pointer;
    transition: background 0.2s;
}

.btn-primary {
    background: #f44b6a;
    color: #fff;
}

.btn-primary:hover {
    background: #e13a5c;
}

.btn-line {
    background: #22B573;
    color: #fff;
}

.btn-line:hover {
    background: #1b9c62;
}

/* 带图标的输入框 */
.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #909399;
    font-size: 18px;
    z-index: 2;
}

/* 验证码输入框组 */
.verification-group {
    display: flex;
    gap: 10px;
}

.verification-group .input-with-icon {
    flex: 1;
}


/* 底部注册链接 */
.no-account {
    text-align: center;
    font-size: 14px;
    color: #666;
}

.register-link {
    color: #f44b6a;
    text-decoration: none;
}

/* 响应式适配 */
@media (max-width: 600px) {
    .user-section.login-section {
        width: 100%;
        min-width: unset;
        border-radius: 0;
        box-shadow: none;
    }

    .login-section .login-main {
        padding: 0 10px 24px 10px;
    }
    
    .verification-group {
        flex-direction: column;
        gap: 5px;
    }
    
}

/* 注册页面样式 - 新增 */
/* 表单标签样式 */
.ez-form-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    display: block;
}

.ez-form-label .required {
    color: #ef436d;
    margin-right: 4px;
}

/* 手机号输入区域 */
.ez-phone-input .el-input-group__prepend {
    background-color: #fff;
    width: 110px;
    padding: 0;
}

.ez-phone-input .el-select {
    width: 100%;
}

.ez-phone-input .el-select .el-input__inner {
    border: none;
    padding-right: 20px;
    text-align: center;
}

.ez-phone-input .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* 修复下拉框对齐问题 */
.el-select-dropdown__item {
    padding: 0 20px;
    text-align: center;
}

.ez-phone-input .el-select-dropdown {
    min-width: 110px !important;
    left: 0 !important;
}

.ez-phone-input .el-select-dropdown__list {
    padding-left: 0;
}

/* 验证码区域 */
.ez-verification-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.ez-verification-wrapper .el-input {
    flex: 1;
}

.ez-send-code-btn {
    min-width: 108px;
    color: #EF436D;
    border: 1px solid #EF436D;
    background-color: #fff;
    border-radius: 4px;
    padding: 11px 15px;
    font-size: 14px;
    line-height: 1;
}

.ez-send-code-btn:hover:not(:disabled) {
    color: #ffffff;
    background-color: #c2c2c2;
    border-color: #c2c2c2;
}

.ez-send-code-btn:disabled {
    color: #ffffff;
    background-color: #c2c2c2;
    border-color: #c2c2c2;
}

/* 协议同意区域 */
.ez-agreement-wrapper {
    margin: 20px 0;
}

.ez-checkbox {
    display: flex;
    align-items: center;
}

.ez-checkbox .el-checkbox__label {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    line-height: 1.5;
    padding-left: 8px;
}

.ez-link-text {
    color: #ef436d;
    text-decoration: none;
}

.ez-link-text:hover {
    text-decoration: underline;
}

/* 按钮样式 */
.ez-btn-wrapper {
    margin-bottom: 15px;
}

.ez-register-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
    background-color: #ef436d;
    border-color: #ef436d;
    border-radius: 4px;
    color: #fff;
    border: 1px solid #ef436d;
    cursor: pointer;
    outline: none;
    padding: 0;
    transition: background-color 0.3s, border-color 0.3s;
}

.ez-register-btn:hover {
    background-color: #ef436d;
    border-color: #ef436d;
    opacity: 0.9;
}

.ez-line-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
    background-color: #22B573;
    border-color: #22B573;
    border-radius: 4px;
    color: #fff;
    border: 1px solid #22B573;
    cursor: pointer;
    outline: none;
    padding: 0;
    transition: background-color 0.3s, border-color 0.3s;
}

.ez-line-btn:hover {
    background-color: #22B573;
    border-color: #22B573;
    opacity: 0.9;
}

.ez-login-link {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-top: 10px;
    margin-bottom: 20px;
}

/* 覆盖Element UI默认样式 */
.ez-checkbox .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #ef436d;
    border-color: #ef436d;
}

.ez-checkbox .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #606266;
}

.home-footer {
    width: 100%;
    height: 48px;
    background-color: #272727;
    color: #FFFFFF;
    font-size: 13px;
    text-align: center;
    line-height: 48px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ez-verification-wrapper {
        flex-direction: column;
    }
    
    .ez-send-code-btn {
        margin-top: 10px;
        width: 100%;
    }
}