/* 通知公告页面样式 */
.notice {
    padding: 24px 0;
    margin: 0 auto;
    width: 1200px;
    min-height: 837px;
}

.notice-container {
    padding: 0;
    width: 100%;
}

/* 公共滚动条样式 - 贴边显示 */
.edge-scrollbar {
    overflow-y: auto;
    margin-right: -17px; /* 让滚动条贴着边缘显示 */
    padding-right: 17px; /* 补偿右侧间距，保持内容不变 */
}

.edge-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.edge-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0;
}

.edge-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 0;
}

.edge-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 需要使用贴边滚动条的父容器样式 */
.scrollbar-container {
    position: relative;
    overflow: hidden;
}

.notice-title {
    font-size: 16px;
    font-weight: 500;
    color: #3D3D3D;
    margin-bottom: 24px !important;
}

/* 标签切换样式 */
.notice-tabs {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    gap: 5px;
}

.tab-item {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px; 
    height: 40px !important;
    margin: 0 8px !important;
    padding: 0 15px !important;
    min-width: 88px !important;
    height: 28px !important;
    font-size: 14px !important;
    background-color: #f5f5f5 !important;
    color: #666666 !important;
    cursor: pointer;
    position: relative;
    border-radius: 50px;
    text-decoration: none;
}

.tab-item.active {
    color: #FFFFFF !important;
    font-weight: 500;
    background-color: #EF436D !important;
}

.tab-item.active::after {
    content: none;
}

.notice-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.site-icon {
    background-image: url('../img/pc/notice_site.svg');
}

.platform-icon {
    background-image: url('../img/pc/notice_platform.svg');
}

/* 通知列表样式 */
.notice-list {
    width: 100%;
    padding: 0; 
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-bottom: 1px solid #e7e7e7;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}
 
.notice-item {
    margin: 0;
    padding: 0 24px;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #e7e7e7;
    cursor: pointer;
    background-color: #ffffff;
    transition: background-color 0.2s ease;
    position: relative;
}

.notice-item:last-child {
    border-bottom: none;
}

.notice-item:hover { 
    background-color: #f7f7f7;
}

.notice-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notice-text {
    font-size: 13px;
    color: #333333;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 20px;
}

.notice-date {
    font-size: 13px;
    color: #666666;
    white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .notice-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .notice-text {
        margin-bottom: 8px;
    }
    
    .notice-date {
        align-self: flex-end;
    }
}