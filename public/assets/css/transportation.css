* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

@media (max-width: 768px) {
    .transport-box {
        flex-direction: column;
    }

    .left_d.block_div {
        width: 100%;
        position: static;
    }
}

a .nav-link:hover {
    color: #F7F7F7;
}

/* el-dialog页脚样式 */
.el-dialog__footer .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    align-items: center;
}

.el-message-box__btns {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
}

.el-message-box__btns

.el-table {
    border-radius: 4px;
}

.el-table th.el-table__cell {
    color: #666666 !important;
}

.el-table td.el-table__cell {
    color: #333333 !important;
}

.el-button {
    height: 32px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px !important;
}

.el-tabs__active-bar {
    height: 2px !important;
    border-radius: 2px !important;
  }

.el-tabs__nav-wrap::after {
    height: 1px;
}

.el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    min-height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    box-sizing: border-box;
    color: #333333;
}

.el-input {
    height: 32px !important;
    min-height: 32px !important;
}

.el-input__icon {
    line-height: 32px !important;
    height: 32px !important;
}

.el-input-group__prepend,
.el-input-group__append {
    height: 32px !important;
    line-height: 32px !important;
    min-height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.el-select .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    min-height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.el-select-dropdown__item {
    min-height: 32px !important;
    line-height: 32px !important;
}

.el-cascader .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    min-height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    min-height: 32px !important;
}

.el-input-number .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    min-height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.el-input-number {
    height: 32px !important;
    min-height: 32px !important;
}

.el-input-number__decrease, .el-input-number__increase {
    height: 30px !important;
    line-height: 30px !important;
    min-height: 30px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    border-bottom: 1px solid #dcdfe6 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box;
}

.el-input-number__decrease i, .el-input-number__increase i {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100%;
    height: 100%;
    font-size: 16px;
}


body {
    background: #edefef;
    font-family: PingFangTC-Regular, sans-serif;
}
.order-detail {
    border-radius: 8px;
}
.order-detail .el-dialog__body {
    height: 60vh; /* 或者100%，但前提是父级有高度 */
    min-height: 400px;
    padding: 0;
}
.order-detail .el-dialog__header {
    margin-bottom: 4px;
}

.Source-Han-Sans {
    font-family: 'Source Han Sans CN', -apple-system, sans-serif;
}

.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

li {
    list-style: none;
    margin: 0;
    padding: 0;
}

.my-tag {
    padding: 0 6px;
    min-width: 52px;
    min-height: 20px;
    line-height: 20px;
    display: inline-block;
    text-align: center;
    background: #FFF1E9;
    border: 1px solid #E37318;
    border-radius: 3px;
    font-size: 10px;
    color: #E37318;
}

.dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background: #f6ce81;
    border-radius: 50%;
}

.select-input .el-input__inner {
    width: 350px;
}

.my-tooltip {
    max-width: 276px !important;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.fs8 {
    font-size: 8px;
}

.fs12 {
    font-size: 12px;
}

.fs13 {
    font-size: 13px;
}

.fs14 {
    font-size: 14px;
}

.fs16 {
    font-size: 16px;
}

.fs18 {
    font-size: 18px;
}

.fs20 {
    font-size: 20px;
}

.fs24 {
    font-size: 24px;
}

.fw {
    font-weight: 500;
}

.c333 {
    color: #333333;
}

.c3d3 {
    color: #3D3D3D;
}

.c666 {
    color: #666666;
}

.c999 {
    color: #999999;
}

.red {
    color: #EF436D;
}

.green {
    color: #22B573;
}

.brown {
    color: #D54941;
}

.mt8 {
    margin-top: 8px;
}

.mt18 {
    margin-top: 18px;
}

.mt12 {
    margin-top: 12px;
}

.mt14 {
    margin-top: 14px;
}

.mt16 {
    margin-top: 16px;
}

.mt20 {
    margin-top: 20px;
}

.mt22 {
    margin-top: 22px;
}

.mt24 {
    margin-top: 24px;
}

.mt32 {
    margin-top: 32px;
}

.mb8 {
    margin-bottom: 8px;
}

.mb12 {
    margin-bottom: 12px;
}

.mb14 {
    margin-bottom: 14px;
}

.mb16 {
    margin-bottom: 16px;
}

.mb18 {
    margin-bottom: 18px;
}

.mb24 {
    margin-bottom: 24px;
}

.mb32 {
    margin-bottom: 32px;
}

.ml4 {
    margin-left: 4px;
}

.ml6 {
    margin-left: 6px;
}

.ml8 {
    margin-left: 8px;
}

.ml12 {
    margin-left: 12px;
}

.mr13 {
    margin-right: 13px;
}

.mr8 {
    margin-right: 8px;
}

.mr12 {
    margin-right: 12px;
}

.mr14 {
    margin-right: 14px;
}

.pl12 {
   padding-left: 12px;
}

.pointer {
    cursor: pointer;
}

a {
    color: #EF436D;
    text-decoration: none;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.jump-tags {
    color: #3D3D3D;
    font-size: 13px;
    text-decoration: underline;
}

.left_d {
    width: 222px;
    background: #edefef;
}

.left_d .title {
    line-height: 0;
    color: #3D3D3D;
    font-size: 18px;
    font-weight: 500;
    padding-left: 12px
}

.left_d .title img {
    position: relative;
    top: 2px;
    margin-right: 10px;
}

.left_d .list_d a {
    display: flex;
    line-height: 48px;
    font-size: 14px;
    transition: 0.3s;
    padding-left: 50px;
    color: #656565
}

.left_d .list_d a img {
    position: relative;
    top: 0;
    margin-right: 6px;
}

.left_d .list_d a:hover {
    background: #F7F7F7;
}

.left_d .list_d a.active {
    color: #EF436D;
    background: #ffe1e1;
    position: relative;
}

.left_d .list_d a.active::after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 100%;
    background: #EF436D;
}

.left_d .list_d a span {
    line-height: 20px;
    font-size: 13px;
    color: #fff;
    border-radius: 20px;
    background: #FF4F4F;
    padding: 0 5px;
}

.menu-list {
    background: #FFFFFF;
    padding-top: 6px;
    border-radius: 4px;
    padding-bottom: 46px;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex-gap {
    display: flex;
    align-items: center;
    gap: 8px;
}

.my-Wallet {
    width: 222px;
    height: 108px;
    background: #FFFFFF;
    font-size: 13px;
    color: #3D3D3D;
    padding: 11px 12px;
    margin-bottom: 6px;
    border-radius: 0 0 4px 4px;
}

.my-Wallet .top {
    display: flex;
    justify-content: space-between;
}

.my-Wallet .middle {
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 20px;
    line-height: 14px;
    height: 14px;
    color: #EF436D;
    margin: 12px 0 16px;
}

.my-Wallet .bottom {
    height: 14px;
    line-height: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.my-coupons {
    width: 222px;
    height: 88px;
    background: #FFFFFF;
    padding: 11px 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 6px;
    border-radius: 4px;
}

.my-coupons .top {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    /*color: #3D3D3D*/
}

.my-coupons .bottom {
    font-size: 13px
}

.underline {
    text-decoration: underline;
}

.el-table__cell {
    font-size: 14px ;
    color: #666666 ;
    font-weight: normal;
}

.custom-header {
    border: 1px solid #D8D8D8;
    border-radius: 4px;
}

.custom-header th {
    background-color: #FFF7F9 !important;
    border-bottom: none !important;
}

.custom-header tr:last-child {
    border-bottom: none !important;
}

.table-class-line {
    position: relative;
}

/* 简单方案：让 top-line 使用 box-shadow 来扩展宽度 */
.top-line {
    width: 100%;
    height: 1px;
    background: #ebeef5;
    margin: 0;
    box-shadow: -1000px 0 0 #ebeef5, 1000px 0 0 #ebeef5;
}

/* 去除 el-table 行 hover 高亮 */
.el-table__body tr:hover > td,
.el-table__body tr.current-row > td {
    background-color: transparent !important;
}

.el-form-item {
    margin-bottom: 10px;
}

.el-table__empty-block {
    height: auto !important;
}

.transport-box {
    position: relative;
    min-height: 100vh;
    border: 1px solid #edefef;
    display: flex;
    align-items: flex-start; /* 顶部对齐 */
    gap: 5px; /* 元素间距 */
    padding: 20px; /* 添加内边距 */
}

/* 右侧容器 */
.right-container {
    margin-top: 46px;
    border-radius: 4px;
    background: #FFFFFF;
    min-height: 837px;
    width: 1518px;
    flex: 1; /* 占据剩余空间 */
    min-width: 0; /* 防止内容溢出 */
    padding: 20px;
}

.wallet-page {
    min-height: 800px;
}

.wallet-page .transportation-page {
    position: relative;
    width: 1446px;
    height: 100%;
}

.wallet-page .title {
    color: #3D3D3D;
    font-weight: 500;
    font-size: 16px;
    margin: 0 0 24px 0;
}

.wallet-page .sub-title {
    color: #333333;
    font-size: 16px;
    margin: 24px 0 16px;
}

.wallet-page .marking {
    color: #EE0000;
    font-size: 13px;
}

.wallet-page .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 120px;
    background: #FFFFFF;
    /*border-top: 1px solid #D8D8D8;*/
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
}

.wallet-page .footer-btn {
    width: 80%;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-btn .left {
    display: flex;
    align-items: center;
}
.footer-btn .right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.wallet-page .clause {
    width: 100vw;
    height: 852px;
    color: #666666;
    font-size: 13px;
}

/* 预报包裹 => 标签头样式 */
.tab-header {
    height: 36px;
    display: flex;
    justify-content: center; /* 水平居中 */
}

.tab-item {
    height: 36px;
    line-height: 12px;
    /* background: #FFF5F7; */
    color: #333333;
    padding: 12px 30px;
    margin: 0 8px;
    border-radius: 32px;
    transition: all 0.3s;
    cursor: pointer;
}

.tab-items {
    width: 88px;
    height: 30px;
    line-height: 30px;
    /* background: #FFF5F7; */
    color: #333333;
    margin: 0 8px;
    border-radius: 32px;
    transition: all 0.3s;
    cursor: pointer;
    text-align: center;
}

.tab-items.active {
    background: #ef436d;
    color: #ffffff;
    font-weight: 500;
}

/* 激活标签样式 */
.tab-item.active {
    background: #ef436d;
    color: #ffffff;
    font-weight: 500;
}

/* 内容区域样式 */

.title {
    font-size: 16px;
    color: #3D3D3D;
    margin: 20px 0;
}

.content-item {
    display: none;
    padding: 20px 0;
}

.content-item.active {
    display: block;
}

.content-box {
    line-height: 1.6;
    background: white;
    border-radius: 32px;
    padding: 20px;
}

.tab-user-info {
    margin-bottom: 32px;
}

.tab-content {
    margin-top: 20px;
}

/*仓库切换*/
.handle-tabs {
    display: flex;
    align-items: center;
    position: relative;
}

.handle-tabs-right {
    position: absolute;
    top: 40px;
    right: 0;
    margin-top: -40px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.handle-tabs-text {
    font-size: 14px;
    color: #333333;
    font-weight: 500;
}

.forecast-footer {
    margin-top: 16px;
    width: 100%;
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    font-size: 14px;
    /* border: 1px solid #d8d8d8; */
}

.forecast-radio .el-checkbox__inner {
    background: #FFFFFF; /* 设置背景色 */
    color: #FFFFFF; /* 设置文字颜色 */
    border-color: #999999; /* 设置边框颜色 */
    border-radius: 50%;
    font-size: 13px;
}

.forecast-radio .el-table__row {
    position: relative;
}

/*.forecast-radio .el-checkbox__inner:hover {*/
/*    border-color: #ef436d;*/
/*}*/

/*.forecast-radio .el-checkbox__input.is-checked .el-checkbox__inner {*/
/*    background: #ef436d; !* 选中状态背景色 *!*/
/*    border-color: #ef436d; !* 选中状态边框颜色 *!*/
/*}*/

/*.forecast-radio .el-checkbox__input {*/
/*    margin-top: -4px;*/
/*}*/

/*.forecast-radio .el-checkbox__input.is-focus .el-checkbox__inner {*/
/*    border-color: #ef436d;*/
/*}*/

.input-container {
    display: flex;
    width: 400px;
    height: 36px;
    align-items: center;
    margin-bottom: 14px;
}

.left-label {
    width: 100px; /* 可调整红色盒子的宽度 */
    height: 48px;
    background-color: #ef436d;
    flex-shrink: 0;
    text-align: center;
    line-height: 48px;
    color: #FFFFFF;
    border-radius: 8px 0 0 8px;
    font-size: 14px;
}

.input-wrapper {
    position: relative;
    flex-grow: 1;
    height: 100%;
    border-radius: 0 8px 8px 0;
}

.custom-input {
    width: 100%;
    height: 100%;
    border: 1px solid #dcdfe6;
    padding: 0 12px;
    box-sizing: border-box;
    outline: none;
    text-align: center;
}

.currency-symbol {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    pointer-events: none;
    background: white;
    padding-left: 4px;
}

/*银行卡选项框*/
.checkbox-group {
    display: flex;
    gap: 48px;
    flex-wrap: wrap;
    margin: 12px 0 16px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    width: 400px;
    height: 56px;
    border: 1px solid #D8D8D8;
    border-radius: 8px;
    margin-bottom: 16px;
    cursor: pointer;
    position: relative;
}


.checkbox-item.selected {
    border-color: #EF436D;
    background: #FCE7EC;
    color: #EF436D;

    /*&::before {*/
    /*    content: '';*/
    /*    position: absolute;*/
    /*    bottom: 0;*/
    /*    right: 0;*/
    /*    width: 24px; !* 根据图片比例调整 *!*/
    /*    height: 24px;*/
    /*    background: #EF436D; !* 与边框同色 *!*/
    /*    clip-path: polygon(100% 0, 100% 100%, 0 100%); !* 右下三角 *!*/
    /*    border-radius: 0 0 8px 0; !* 匹配卡片圆角 *!*/
    /*}*/
    
    /*&::after {*/
    /*    content: "✓";*/
    /*    position: absolute;*/
    /*    bottom: 0; !* 微调位置 *!*/
    /*    right: 0;*/
    /*    font-size: 16px;*/
    /*    color: white;*/
    /*    font-weight: 900;*/
    /*    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);*/
    /*    z-index: 1; !* 确保在遮罩上层 *!*/
    /*}*/
}

.checkbox-input {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
}

.checkbox-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    padding: 2px;
    white-space: nowrap;
    text-align: center;
    margin-left: -58px;
}

.bank-icon {
    margin: 0 12px;
    width: 27px;
    height: 17px;
}

.bank-icon img {
    width: 100%;
    height: 100%;
}

.bank-value {
    font-size: 16px;
    margin-right: 6px;
}

.bank-name {
    font-size: 16px;
}

.bank-code {
    font-size: 16px;
    margin-left: auto;
    margin-right: 16px;
    /* 保证在卡片右侧且不溢出 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.add-card {
    border: 1px solid #D8D8D8;
    border-radius: 8px;
    width: 400px;
    height: 56px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 22px;
    color: #666666;
    font-size: 16px;
}

.add-card.selected {
    border-color: #EF436D;
    background: #FCE7EC;
    color: #EF436D;
    overflow: visible; /* 允许伪元素溢出 */
    position: relative; /* 定位基准 */

    &::before {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 24px; /* 根据图片比例调整 */
        height: 24px;
        background: #EF436D; /* 与边框同色 */
        clip-path: polygon(100% 0, 100% 100%, 0 100%); /* 右下三角 */
        border-radius: 0 0 8px 0; /* 匹配卡片圆角 */
    }

    &::after {
        content: "✓";
        position: absolute;
        bottom: 0; /* 微调位置 */
        right: 0;
        font-size: 16px;
        color: white;
        font-weight: 900;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        z-index: 1; /* 确保在遮罩上层 */
    }
}

.add-card .card-icon {
    width: 27px;
    height: 17px;
    margin-top: -6px;
}

.card-icon {
    width: 100%;
    height: 100%;
}

/*賬戶明細*/
.account-icon {
    width: 36px;
    height: 36px;
}

.account-icon img {
    width: 100%;
    height: 100%;
}

.form-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 34px;
}

/*我的包裹*/
.my-form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 16px;
}

/* 解决 el-select 下拉框覆盖问题 */
.my-form .el-select {
    position: relative;
    z-index: 1;
}

.my-form .el-select .el-select-dropdown {
    z-index: 3000;
}

.my-form .el-form-item {
    position: relative;
    z-index: 1;
}

/* 确保下拉框不会覆盖其他元素 */
.el-select-dropdown {
    z-index: 3000 !important;
}

/* 设置仓库选择下拉框宽度 */
.warehouse-select-dropdown {
    width: 260px !important;
}

.warehouse-select-dropdown .el-select-dropdown__item {
    width: 100%;
}

/* 响应式布局 - 当容器宽度不足时让按钮换行 */
@media (max-width: 1200px) {
    .my-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .my-form > div:first-child {
        justify-content: flex-start;
    }
    
    .my-form > div:last-child {
        justify-content: flex-start;
        margin-top: 8px;
    }
}

/* 当浏览器放大到150%或更大时的样式 */
@media (min-resolution: 1.5dppx), (min-resolution: 144dpi) {
    .my-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .my-form > div:first-child {
        justify-content: flex-start;
    }
    
    .my-form > div:last-child {
        justify-content: flex-start;
        margin-top: 8px;
    }
}

.el-table__row {
    position: relative;
}

.goods-svg img {
    margin-top: 2px;
    width: 20px;
    height: 20px;
}

.goods-up {
    margin-bottom: 16px;
    justify-content: space-between;
    font-size: 13px
}

.goods-up-left {
    flex: 1;
    min-width: 0; /* 允许内容截断 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.goods-small-img {
    margin-right: 4px;
    width: 14px;
    height: 14px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

.goods-remarks {
    display: flex;
    align-items: center;
    /*height: 56px;*/
}

.title-filter {
    font-size: 13px;
    color: #333333;
}

.edit {
    margin-left: 5px;
    width: 16px;
    height: 16px;
}

.edit img {
    width: 100%;
    height: 100%;
}

.goods-middle {
     /*height: 127px;*/
    /*margin: 42px 0 24px 0;*/
    display: flex;
    align-items: center;
    font-size: 14px;
}

.goods-down {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.goods-down img {
    width: 16px;
    height: 16px;
}

.goods-img {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    width: 72px;
    height: 72px;
    margin-right: 16px;
    background: #f3f3f3;
    /*padding: 12px;*/
}

.goods-img img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.goods-name {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 16px;
}

.goods-short-name {
    color: #EF436D;
    font-size: 14px;
}

.handle-up {
    position: absolute;
    top: 8%;
}

.handle-middle {
    position: absolute;
    top: 46%;
}

.handle-down {
    position: absolute;
    top: 84%;
}

.middle-box {
    width: 100%;
    position: absolute;
    top: 53px;
    min-width: 350px;
    height: 120px;
}

.middle-box-top {
    width: 100%; 
    min-height: 45px;
    text-align: center; 
    text-decoration: underline;
    line-height: 45px;
    border-left: 1px solid #ebeef5;
}

.middle-box-center {
    width: 100%;
    height: 82px;
    display: flex; 
    flex-direction: column;
    justify-content: space-between;
    align-content: center;
    padding: 12px 24px 12px 16px;
    border: 1px solid #ebeef5;
}

.flex-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.other-service .el-dialog{
    border-radius: 4px;
}

.other-service .el-dialog__body {
    margin-left: 24px;
    padding-right: 2px;
    padding-left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.other-service .el-dialog__header {
    width: 100%;
    text-align: center;
}

.other-service .el-dialog__title {
    font-size: 20px;
    color: #EF436D;
}

.el-dialog--center {
    border-radius: 8px;
}

.form-flex {
    margin-left: 24px;
    display: flex;
    gap: 60px;
}

.form-flex div {
    margin-bottom: 8px;
}

.form-flex .el-form-item__error {
    margin-top: -8px;
}

.forward .sub-title {
    display: flex;
    width: 912px;
    font-size: 14px;
    color: #EF436D;
    margin-bottom: 16px;
}

.forward .el-dialog__title {
    font-size: 20px;
    font-weight: 500;
    color: #EF436D;
}

.forward .el-dialog__header {
    text-align: center;
}

.forward .el-input {
    width: 280px;
}

.forward .tips {
    margin-left: 24px;
    color: #333333;
    font-size: 13px;
}

.forward .address-info {
    margin: 0 0 16px 24px;
    border: 1px solid #EF436D;
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    justify-content: left;
    gap: 6px;
    width: 97%;
    background: #FFF7F9;
    color: #333333;
    font-size: 13px;
    border-radius: 4px;
}

.show-add-service {
    margin: 12px 24px;
    background: #F7F7F7;
    border-radius: 6px;
    padding: 18px 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.pd30-btn {
    padding: 6px 30px;
}

.forward-content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    border-radius: 8px;
    margin-bottom: 42px;
}

.forward-box-onlyone {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
    width: 450px;
    height: 162px;
    border-radius: 8px;
    background: #FFF7F9;
    border: 1px solid rgba(239, 67, 109, 0.2);
    font-size: 18px;
    color: #333333;
    margin-bottom: 120px;
}

.forward-box-onlyone > .bg {
    width: 94px;
    height: 80px;
}

.forward-box-onlyone > .bg img {
    width: 100%;
    height: 100%;
}

.forward-box-first {
    width: 450px;
    height: 288px;
    border-radius: 8px;
    background: #FFFFFF;
    border: 1px solid rgba(239, 67, 109, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333333;
}
.forward-box-first:hover {
    background: #FFF7F9;
    border: 1px solid #ef436d;
}

.forward-box-second {
    width: 450px;
    height: 288px;
    border-radius: 8px;
    background: #FFFFFF;
    border: 1px solid rgba(239, 67, 109, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333333;
}

.forward-box-second:hover {
    background: #FFF7F9;
    border: 1px solid #ef436d;
}

.my-form .el-select {
    width: 360px;
}

.my-form .el-input {
    width: 360px;
}

.my-form .el-dialog {
    border-radius: 6px;
}

/* 确保 el-select 内部的输入框宽度一致 */
.my-form .el-select .el-input__inner {
    width: 260px !important;
}

.my-form .el-select .el-input {
    width: 260px !important;
}


.check-box .el-checkbox__input {
    margin-top: -35px;
}

.el-popover {
    padding: 16px 16px 16px 25px;
    width: 276px !important;
    height: 94px !important;
}

.el-popconfirm {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.el-popconfirm__action > .el-button--primary {
    text-align: left !important;
    color: #FFFFFF !important;
    background-color: #EF436D !important;
    border: 1px solid #EF436D !important;
}

.service-content {
    width: 912px;
    height: 108px;
    background: #FFF7F9;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 28px 24px;
    border-radius: 4px;
    position: relative;
    margin-bottom: 19px;
    border: 2px solid #EF436D;
}

.service-content .flex {
    display: flex;
    align-items: center;
    height: 128px;
}

.service-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 16px;
    height: 80px;
}


.server-tooltip {
    max-width: 300px;
    line-height: 1.5;
}

.section-title {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #333333 !important;
    margin-bottom: 4px;
}

.section-subtitle {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: #333333 !important;
    margin: 8px 0 4px;
}

.section-content {
    font-size: 13px !important;
    color: #666666 !important;
    margin-bottom: 4px;
}

.insured-num {
    position: absolute;
    top: 45px;
    right: 24px;
}

.insured-price {
    position: absolute;
    bottom: 24px;
    right: 24px;
}

.insured-price-btn {
    position: absolute;
    top: 15px;
    right: 0;
    background: #E52B2B;
    font-size: 13px;
    height: 22px;
    width: 61px;
    text-align: center;
    line-height: 22px;
    color: #FFFFFF;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 10% 100%, 0 0%);
}

.sub-title {
    display: flex;
    width: 912px;
    font-size: 14px;
    color: #EF436D;
    margin-bottom: 18px;
}

.sub-line {
    width: 12px;
    height: 3px;
    transform: rotate(-90deg);
    background: #EF436D;
    margin: 9px 8px 0;
    border-radius: 16px;
}

.sub-text {
    font-size: 13px;
    color: #999999;
}

.service-box {
    position: relative;
    width: 912px;
    height: 128px;
    border: 1px solid rgba(239, 67, 109, 0.2);
    border-radius: 4px;
    padding: 28px 24px;
}

.service-box2 {
    position: relative;
    width: 912px;
    border: 1px solid rgba(239, 67, 109, 0.2);
    border-radius: 4px;
    padding: 24px;
}

.service-box-title {
    margin-bottom: 8px;
}

.float-window {
    width: 88px;
    height: 24px;
    background: #FFFFFF;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #EF436D;
    top: -12px;
    left: 47%;
}

.float-window2 {
    width: 103px;
    height: 24px;
    background: #FFFFFF;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #EF436D;
    top: -12px;
    left: 46%;
}

.parcel-icon {
    margin-top: -3px;
    width: 16px;
    height: 14px;
}

.parcel-icon img {
    width: 100%;
    height: 100%;
}

.inline-block {
    display: inline-block;
}

.reject-content {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.second-registration .title {
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: 500;
    display: flex;
    justify-content: center
}

.to-back {
    position: absolute;
    top: 16px;
    left: 16px;
}

.logo {
    margin-top: 21px;
    margin-bottom: 15px;
    width: 72px;
    height: 72px;
}

.logo img {
    width: 100%;
    height: 100%;
}

.txt-one {
    margin-bottom: 8px;
}

.txt-second {
    margin-bottom: 53px;
}

.txt-third {
    margin-bottom: 1px;
}

.txt-fourth {
    color: #666666;
}

.my-footer {
    position: absolute;
    text-align: center;
    margin: 20px 0;
}

/*支付彈窗*/
.custom-dialog .el-dialog__title {
    color: #EF436D;
    font-size: 20px;
    text-align: center;
    font-weight: 500;
}

.pay-box {
    background: #FFF7F9;
    border-radius: 8px;
    margin: 0 24px;
    padding: 24px 0;
    border: 1px solid rgba(239, 67, 109, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 24px;
}

.pay-amount-title {
    color: #333333;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
}

.pay-amount {
    color: #666666;
    font-size: 18px;
    text-align: center;
}

.pay-amount span {
    color: #EF436D;
    font-size: 30px;
    font-weight: 500;
}

.pay-detail {
    color: #666;
    font-size: 14px;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
}

.pay-detail:last-child {
    margin-bottom: 0;
}

.pay-method-title {
    color: #ff4d7a;
    font-size: 16px;
    margin: 24px 0 8px 24px;
    font-weight: 500;
}

.pay-method-info {
    color: #666;
    font-size: 14px;
}

.pay-password-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 24px;
    margin-bottom: 16px;
}

.pay-password-inputs {
    display: flex;
    gap: 28px;
    margin-top: 8px;
    justify-content: center;
    align-items: center;
}

.pay-password-input {
    width: 48px;
    height: 48px;
    border: 1px solid #E5E5E5;
    border-radius: 8px;
    text-align: center;
    font-size: 24px;
    font-weight: 500;
    outline: none;
    background: #F8F8F8;
    margin-right: 0;
    color: #EF436D;
    transition: all 0.3s ease;
}

.pay-password-input:focus {
    border-color: #EF436D;
    background: #FFFFFF;
    box-shadow: 0 0 0 2px rgba(239, 67, 109, 0.1);
}

.pay-password-input:last-child {
    margin-right: 0;
}

/*附加服务二级弹窗*/
.el-input-number .el-input__inner {
    width: 180px !important;
}

.el-input-number__decrease, .el-input-number__increase {
    padding-top: 12px;
    height: 38px;
}

.el-icon-minus .el-icon-plus {
    margin-top: 20px;
}

.table-other {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-dialog-content {
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.dialog-title {
    color: #333333;
    font-size: 13px;
}

.service-info-row {
    color: #666666;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.service-info-row:last-of-type {
    margin-bottom: 16px;
}

.info-label {
    color: #666666;
}

.info-value {
    font-size: 13px;
    color: #333333;
    font-weight: 500;
}

.example-images {
    display: flex;
    align-items: center;
    gap: 24px;
}

.example-image {
    width: 114px;
    height: 135px;
}

.example-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.input-section {
    margin: 8px 0;
}

.split-notice {
    font-size: 14px;
    color: #333333;
    margin-top: 8px;
}

.notice-title {
    margin-bottom: 4px;
}

.notice-item {
    margin-bottom: 4px;
}

/*优惠券管理*/
.custom-tabs .el-tabs__item {
    color: #666666;
}

.custom-tabs .el-tabs__item:hover {
    color: #EF436D;
}

.custom-tabs .el-tabs__item.is-active {
    color: #EF436D;
}

.custom-tabs .el-tabs__active-bar {
    background-color: #EF436D;
}

.coupon-item {
    width: 144px;
    height: 116px;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    color: #FFFFFF;
}

.coupon-item-top {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;
    padding: 10px 0;
    color: #333333;
}

.coupon-item-btm {
    width: 100%;
    line-height: 1.5;
    text-align: center;
    font-size: 12px;
    border-radius: 194px;
    margin-bottom: 7px;
}

/*待出貨列表*/
.await-box {
    position: relative;
    width: 100%;
    max-width: 100%;
    background: #FFF7F9;
    border: 1px solid rgba(239, 67, 109, 0.2);
    padding: 56px 24px 16px 24px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.await-tags {
    width: 64px;
    height: 24px;
    background: #E37318;
    border-radius: 0 4px 4px 0;
    position: absolute;
    top: 16px;
    left: 0;
    text-align: center;
    line-height: 24px;
    font-size: 13px;
    color: #FFFFFF;
    border-left: none;
    font-weight: 500;
}

.await-btn {
    position: absolute;
    width: 98px;
    height: 76px;
    top: 56px;
    right: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    /*gap: 16px;*/
}

.await-btn > .el-button {
    width: 100%;
    margin: 0 auto;
}

.ezway-box {
    display: flex;
    flex-direction: column;
    width: 546px;
    min-height: 170px;
    background: #fff6f8;
    border: 1px solid #ef436d;
    border-radius: 8px;
    padding: 20px 24px;
    margin-bottom: 24px;
    font-family: 'Alibaba PuHuiTi', -apple-system, PingFang SC, Microsoft YaHei, sans-serif;
}

.ezway-alert {
    display: flex;
    align-items: center;
    color: #ef436d;
    font-size: 14px;
    margin-bottom: 16px;
}

.ezway-alert i {
    font-size: 18px;
    margin-right: 8px;
}

.ezway-btn-row {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.ezway-btn {
    border: none;
    border-radius: 4px;
    padding: 0 16px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.ezway-span {
    border-radius: 4px;
    padding: 0 16px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    cursor: pointer;
}

.ezway-btn-red {
    background: #ef436d;
    color: #fff;
}

.ezway-btn-red.ezway-btn-outline {
    background: #fff;
    color: #ef436d;
    border: 1px solid #ef436d;
}

.ezway-btn-change {
    background: #22b573;
    color: #fff;
    margin-left: 16px;
    transition: background 0.2s, color 0.2s;
}
.ezway-btn-change[disabled], .ezway-btn-change.disabled {
    background: #D8D8D8 !important;
    cursor: not-allowed !important;
    border: none;
}

.ezway-link-row {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 8px;
}

.ezway-link {
    color: #ef436d;
    font-size: 14px;
    text-decoration: underline;
    margin-right: 16px;
}
.dividing-line {
    width: 1610px;
    height: 3px;
    background: #FFE4EB;
    margin: 12px -24px 8px -24px;
}

.addr-svg {
    width: 18px;
    height: 18px;
}

.addr-svg img {
    width: 100%;
    height: 100%;
}

.await-edit-svg {
    width: 13px;
    height: 13px;
}

.await-edit-svg img {
    width: 100%;
    height: 100%;
}

.await-svg {
    margin-top: 14px;
    width: 50px;
    height: 50px;
}

.await-svg img {
    width: 100%;
    height: 100%;
}

.block-box {
    width: 64px;
    height: 24px;
    border-radius: 3px;
    background: #FFE4EB;
    border: 1px solid #EF436D;
    font-size: 13px;
    text-align: center;
    line-height: 22px;
    margin-right: 16px;
}

/* 集运说明 */
.re-content {
    width: 100%;
    margin: 0 auto;
    padding: 32px 0 0 0;
}

.re-content .card-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    margin-bottom: 40px;
}

.re-content .card-item {
    background: #fff;
    border: 2px solid #EF436D;
    border-radius: 8px;
    box-sizing: border-box;
    width: calc(50% - 12px);
    min-height: 120px;
    padding: 20px 24px 16px 24px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    color: #333;
    position: relative;
}

.re-content .card-item .card-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #EF436D;
    margin-bottom: 8px;
}

.re-content .card-item .card-title img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

.re-content .card-item .card-content {
    font-size: 13px;
    color: #333;
    line-height: 1.7;
    word-break: break-all;
}

.re-content .card-item .card-content .red {
    color: #EF436D;
}

.re-content .card-item ul {
    margin: 8px 0 0 18px;
    padding: 0;
    font-size: 13px;
    color: #666;
}

.re-content .card-item ul li {
    margin-bottom: 2px;
    list-style: disc;
}

.re-content .faq-section {
    display: flex;
    flex-wrap: wrap;
    gap: 0 60px;
    padding-bottom: 32px;
}

.re-content .faq-col {
    min-width: 200px;
    margin-bottom: 12px;
}

.re-content .faq-col a {
    display: block;
    color: #3D3D3D;
    font-size: 13px;
    text-decoration: underline;
    margin-bottom: 8px;
    transition: color 0.2s;
}

.re-content .faq-col a:hover {
    color: #EF436D;
}

@media (max-width: 1500px) {
    .re-content {
        width: 100%;
        padding: 16px;
    }

    .re-content .card-grid {
        gap: 16px;
    }

    .re-content .card-item {
        width: 100%;
        min-width: 0;
    }

    .re-content .faq-section {
        gap: 0 24px;
    }
}

@media (max-width: 900px) {
    .re-content {
        width: 100%;
        padding: 8px;
    }

    .re-content .card-grid {
        flex-direction: column;
        gap: 12px;
    }

    .re-content .card-item {
        width: 100%;
    }

    .re-content .faq-section {
        flex-direction: column;
        gap: 0;
    }

    .re-content .faq-col {
        min-width: 0;
    }
}

/* 仓库地址 */
.addr-content {
    margin-top: 56px;
    width: 100%;
    font-size: 14px;
}

.transport-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    display: inline-block;
    position: relative;
    top: 1px; /* 适当调整为1px或2px */
}

.addr-content .el-table__cell > span {
    display: inline-flex;
    align-items: center;
}

/* 申報人管理 */
.application-content {
    width: 100%;
}

/* 訂單列表 */

/* 订单详情弹窗样式 */
.order-detail-dialog .el-dialog {
    border-radius: 8px;
}

.order-detail-title {
    text-align: center;
    font-size: 22px;
    color: #EF436D;
    font-weight: 500;
    margin-top: 16px;
    margin-bottom: 24px;
    letter-spacing: 2px;
}

.order-detail-section {
    margin: 0 16px;
    border: 1px solid #d8d8d8;
}

.order-detail-table-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #FFF7F9;
    border-radius: 4px 4px 0 0;
    font-size: 14px;
    color: #666666;
    padding: 12px 0 12px 16px;
}

.order-detail-table-header span:first-child {
    flex: 5;
}

.order-detail-table-header span:nth-child(n+2) {
    flex: 1;
    padding-right: 8px
}

.order-detail-goods {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    font-size: 14px;
    color: #333;
}

.order-detail-goods-img {
    width: 64px;
    height: 64px;
    border-radius: 6px;
    margin-right: 18px;
    background: #f5f5f5;
}

.order-detail-goods-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.order-detail-goods-info {
    flex: none; /* 防止宽度被压缩 */
    width: 350px; /* 设置固定宽度 */
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
}

.order-detail-goods-price,
.order-detail-goods-pay,
.order-detail-goods-qty,
.order-detail-goods-total {
    flex: 1;
    text-align: center;
    align-self: center;
    font-size: 14px;
    color: #333;
}

.order-detail-summary {
    margin: 18px 0 0 0;
    font-size: 13px;
    color: #333;
    line-height: 1.8;
    display: flex;
    flex-direction: column;
    padding: 12px 24px;
}

.order-detail-meta {
    margin-top: 18px;
    font-size: 13px;
    color: #666;
    line-height: 1.8;
    padding: 12px 24px;
}

.order-detail-meta span {
    color: #333;
    font-weight: 400;
    margin-left: 4px;
    word-break: break-all;
}

.order-detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 24px;
    padding: 24px 40px 24px 0;
    background: transparent;
}

.order-detail-footer .el-button {
    min-width: 120px;
    height: 40px;
    font-size: 15px;
    border-radius: 6px;
}

.order-detail-footer .el-button--danger {
    background: #EF436D;
    border: none;
    color: #fff;
}

.order-detail-footer .el-button:not(.el-button--danger) {
    background: #fff;
    border: 1px solid #e5e5e5;
    color: #666;
}

@media (max-width: 1000px) {
    .order-detail-dialog .el-dialog {
        width: 98vw !important;
        min-width: 0;
    }

    .order-detail-section {
        padding: 12px 4px 8px 4px;
    }

    .order-detail-table-header, .order-detail-goods {
        gap: 12px;
    }
}

/* 展开/折叠按钮 */
.expand-toggle {
    border-radius: 4px;
    padding: 0 16px;
    height: 48px;
    background-color: #F7F7F7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 6px;
    color: #666666;
    font-size: 13px;
    user-select: none;
}

.expand-toggle i {
    font-size: 16px;
    transition: transform 0.2s;
}

/* 展开内容动画 */
.fade-enter-active, .fade-leave-active {
    transition: all 0.3s;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
    height: 0;
    overflow: hidden;
}

.expand-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center;
}

.service-box2-content {
    width: 100%;
    background: #fbfbfb;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
}

.service-box2-line {
    height: 1px;
    background: #D8D8D8;
    width: calc(100% + 32px); /* 父padding左右各32px */
    margin-left: -16px;
}

.service-info-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 18px;
    align-items: flex-start;
}

.service-img {
    width: 90px;
    height: 90px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid #eee;
    background: #fff;
}

.service-info-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.service-info-title {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    font-size: 14px;
    color: #333;
    margin-bottom: 16px;
}

.service-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 12px;
}

.service-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.service-info-ul {
    color: #333;
    font-size: 14px;
    line-height: 1.7;
}

.service-info-ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-price-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 180px;
    margin-left: 32px;
}

.service-price-item {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.service-status {
    display: inline-block;
    border-radius: 10px;
    padding: 2px 10px;
    font-size: 13px;
    font-weight: 500;
    margin-right: 6px;
}

.service-status.done {
    background: #FFF0F3;
    color: #EF436D;
    border: 1px solid #EF436D;
}

.service-status.finished {
    background: #E6F9F0;
    color: #22B573;
    border: 1px solid #22B573;
}

.service-status.undone {
    background: #FFF7E0;
    color: #EE9E03;
    border: 1px solid #EE9E03;
}

/* 申報人管理 */
.applicant-content {
    width: 100%;
    padding: 0 24px;
    min-height: 356px;
}

.applicant-list {
    margin-top: 16px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.applicant-item, .address-item {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #F7F7F7;
    height: 64px;
    border-radius: 4px;
    padding: 12px 16px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.applicant-item.selected, .address-item.selected {
    border: 1px solid #EF436D;
    background: #FFF7F9;
}

.address-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    background-color: #F7F7F7;
    height: 64px;
    border-radius: 4px;
    padding: 12px 16px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.address-item .editAddr {
    position: absolute;
    cursor: pointer;
    right: 16px;
    top: 16px;
}

.applicant-info {
    display: flex;
    flex-direction: column;
}

.applicant-close {
    display: none;
}

.applicant-item:hover .applicant-close {
    display: block;
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 16px;
    cursor: pointer;
}

.input-serach {
    width: 100%;
}

.input-serach > .el-input {
    width: 100%;
}

.add-applicant-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.add-applicant-form .el-form-item {
    width: 100%;
}

/* 收件人管理 */
.tags-item {
    display: flex;
    align-items: center;
    margin-left: 30px;
}

.handle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-height: 64px;
    padding: 20px;
    margin: 24px 0;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.tags-radio {
    display: flex;
}

.tags-radio > .el-radio {
    display: flex;
    align-items: center;
    margin-right: 6px;
}

.el-form-item__content {
    display: flex;
    align-items: center;
}

.el-radio__label {
    padding-left: 4px;
}

/* 集运订单 */
.tra-order-dialog-content {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    font-size: 14px;
    box-sizing: border-box;
    max-width: 100%;
    overflow-x: hidden;
}

.tra-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
}

.tra-order-header .order-title {
    color: #EF436D;
    font-size: 20px;
    font-weight: bold;
}

.tra-order-header .order-summary {
    color: #EF436D;
    font-size: 14px;
    margin-right: 12px;
}

.tra-order-parcel-list {
    margin-bottom: 18px;
}

.parcel-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #F7F7F7;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 8px;
}

.parcel-info {
    display: flex;
    align-items: center;
}

.parcel-img img {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    margin-right: 12px;
}

.parcel-detail {
    display: flex;
    flex-direction: column;
}

.parcel-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 13px;
    color: #666;
}

.tra-order-section {
    margin-bottom: 24px;
}

.tra-order-subtitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.address-radio-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.choose-other-address {
    color: #EF436D;
    cursor: pointer;
    text-decoration: underline;
    font-size: 13px;
}

.address-detail {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;
    background: #F7F7F7;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
}

.channel-info {
    display: flex;
    gap: 24px;
    margin: 12px 0;
}

.channel-item {
    width: 300px;
    /*width: 912px;*/
    height: 88px;
    background: #f7f7f7;
    border-radius: 8px;
    color: #333333;
    font-size: 13px;
    border: 1px solid #D8D8D8;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;
    cursor: pointer;
}

/* 选中状态的样式 */
.channel-item.selected {
    border: 1px solid #fcd3dd;
    background: #FFF7F9;
}

.channel-item.selected .channel-title {
    color: #FFFFFF;
    background: #EF436D;
}

.channel-title {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    width: 100%;
    height: 22px;
    background-color: #d8d8d8;
    color: #333333;
    text-align: center;
    line-height: 22px;
    font-weight: 500;
}

.channel-title.selected {
    color: #FFFFFF;
    background: #EF436D;
}

.channel-conteent {
    margin-top: -11px;
    height: 66px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.insurance-info {
    height: 132px;
    background-color: #F7F7F7;
    padding: 12px 16px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* align-items: center; */
    gap: 12px;
}

.overweight-fee {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333333;
    font-size: 14px;
}

.overweight-desc {
    color: #666666;
    font-size: 13px;
    font-weight: 500;
}

.insurance-block-first {
    padding: 16px 12px;
    border: 1px solid #EF436D;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.insurance-block-item, .tax-number-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #EF436D;
    border-radius: 4px;
    padding: 6px 12px;
}

.tax-number-item {
    cursor: pointer;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    padding: 6px 12px;
}

.tax-number-item.selected {
    border: 1px solid #EF436D;
}

.insurance-desc {
    color: #E37318;
    font-size: 13px;
    margin: 6px 0;
}

.tra-order-fee-detail {
    background: #F7F7F7;
    border-radius: 6px;
    padding: 16px;
    font-size: 13px;
    color: #333;
}

.tra-order-footer {
    display: flex;
    justify-content: flex-end;
    gap: 24px;
    margin-top: 18px;
}

.warehouse {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
}

/* 打包出库 */
/* packUpDialogVisible 静态页面样式 */
.packup-dialog-wrap {
    position: relative;
    border-radius: 8px;
    padding-bottom: 24px;
    min-height: 600px;
    display: flex;
    align-items: stretch;
    background: #fff;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    position: relative;
    overflow: hidden;
}

.packup-dialog-left {
    width: 340px;
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.packup-order-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.packup-status-icon {
    width: 16px;
    height: 16px;
}

.packup-status-pay {
    width: 64px;
    height: 24px;
    top: 0;
    right: 0;
    /*margin-left: auto;*/
    background: #E37318;
    color: #fff;
    font-size: 13px;
    text-align: center;
    line-height: 24px;
    border-radius: 4px 0 0 4px;
}

.line4 {
    height: 4px !important;
    background: #f4f4f4 !important;
}

.packup-order-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 16px;
}

.packup-order-btns {
    display: flex;
    justify-content: flex-end;
    gap: 24px;
    margin-top: 10px;
}

.packup-btn {
    width: 98px;
    height: 30px;
    border: none;
    text-align: center;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    line-height: 30px;
}

.packup-btn-calc {
    background: #fff;
    color: #666666;
    border: 1px solid #D8D8D8;
}

.packup-btn-pay {
    background: #ef436d;
    color: #fff;
}

.packup-section {
    margin-bottom: 10px;
}

.packup-section-title {
    color: #ef436d;
    font-size: 14px;
    margin-bottom: 12px;
}

.packup-address {
    position: relative;
    width: 100%;
    height: 98px;
    display: flex;
    gap: 12px;
    background: #F7F7F7;
    border-radius: 4px;
    padding: 16px 12px;
    font-size: 13px;
}

.packup-address-icon {
    width: 18px;
    height: 18px;
    background: #ef436d;
    border-radius: 50%;
    margin-right: 6px;
}

.packup-address-info {
    font-size: 13px;
    flex: 1;
    color: #333;
}

.packup-address-edit {
    position: absolute;
    right: 17px;
    bottom: 16px;
    color: #22b573;
    font-size: 13px;
    text-decoration: underline;
    white-space: nowrap;
}

.packup-declare {
    padding-left: 12px;
    background: #F7F7F7;
    height: 76px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 12px;
    font-size: 13px;
    color: #333333;
    border-radius: 4px;
}

.packup-declare-person {
    color: #ef436d;
    background: #fff7f9;
    border-radius: 3px;
    padding: 2px 8px;
}

.packup-declare-type {
    color: #ef436d;
    background: #fff7f9;
    border-radius: 3px;
    padding: 2px 8px;
}

.packup-insurance {
    padding: 12px;
    background: #FFEDD6;
    border-radius: 4px;
    font-size: 13px;
    color: #e37318;
    display: flex;
    align-items: center;
    line-height: 16px;
}

.packup-insurance-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.packup-insurance-icon {
    width: 18px;
    height: 18px;
    background: #ef436d;
    border-radius: 50%;
    margin-right: 4px;
}

.packup-summary {
    margin-top: 18px;
    background: #fff;
    border-radius: 8px;
}

.packup-summary-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.packup-summary-row {
    display: flex;
    justify-content: space-between;
    /*align-items: center;*/
    font-size: 13px;
    color: #333;
}

.packup-summary-value {
    color: #ef436d;
    font-weight: 500;
    font-size: 14px;
    text-align: right;
}

.packup-summary-desc {
    color: #999;
    font-size: 13px;
    margin-left: 8px;
}

.packup-summary-total {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin-top: 10px;
    gap: 8px;
}

.packup-summary-total-value {
    color: #EF436D;
    font-size: 18px;
    font-weight: 500;
}

.packup-dialog-right {
    width: 640px;
    background: #fff;
    border-radius: 0 8px 8px 0;
    padding:24px 24px 0 24px;
    min-width: 0;
}

.packup-goods-list {
    display: flex;
    flex-direction: column;
}

.packup-goods-item {
    height: 144px;
    background: #ffffff;
    border: 1px solid #fcd9e2;
    margin-bottom: 8px;
}

.border-goods-item {
    height: 176px;
    background: #ffffff;
    border: 1px solid #fcd9e2;
    border-radius: 0 0 8px 8px;
}

.border-goods-item:first-child {
    border-top: none;
    margin-top: -4px;
}

.packup-goods-header {
    padding: 0 24px;
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: #333333;
    background: #fff7f9;
}

.border-goods-header {
    padding: 0 24px;
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: #333333;
    background:  linear-gradient(to right, transparent 24px, #d8d8d8 24px) bottom / 100% 1px no-repeat,#fff;
}

.packup-goods-body {
    height: 108px;
    padding: 0 24px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 12px;
}

.packup-goods-img {
    width: 72px;
    height: 72px;
    border-radius: 8px;
    background: #f3f3f3;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.packup-goods-img img {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.packup-goods-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.packup-goods-desc {
    font-size: 13px;
    color: #999;
}

/* 选中状态的样式 */
.channel-item.selected {
    border: 1px solid #fcd3dd;
}

.channel-item {
    cursor: pointer;
}

.channel-item.selected .channel-title {
    color: #FFFFFF;
    background: #EF436D;
}

.channel-item.selected .channel-conteent {
    background: #FFF7F9;
}

/* 訂單詳情單打印彈窗專用樣式 */
.order-print-section {
    background: #fff;
    padding: 0;
    border: none;
    box-shadow: none;
}
.order-print-title {
    font-size: 20px;
    color: #EF436D;
    font-weight: 500;
    margin-bottom: 24px;
    letter-spacing: 2px;
    text-align: center;
}
.order-print-table {
    width: 96%;
    background: #fff;
    border-collapse: collapse;
    /* margin-bottom: 24px; */
    font-family: 'Alibaba PuHuiTi', -apple-system, sans-serif;
}
.order-print-table th,
.order-print-table td {
    border: none;
    font-size: 14px;
    padding: 8px 4px;
    color: #333;
}
.order-print-table .section-title {
    font-size: 15px;
    color: #EF436D;
    font-weight: bold;
    padding: 8px 0;
}
.order-print-table .main-title {
    font-size: 16px;
    color: #333;
    font-weight: bold;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}
.order-print-table .sub-label {
    color: #666;
    width: 15%;
    padding: 4px 0;
}
.order-print-table .sub-value {
    color: #333;
    width: 35%;
}
.order-print-table .sub-label2 {
    color: #666;
    width: 15%;
}
.order-print-table .sub-value2 {
    color: #333;
    width: 35%;
}
.order-print-table .address-row td {
    color: #333;
    padding: 4px 0;
}
.order-print-table .goods-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    margin-top: 0;
}
.order-print-table .goods-table th,
.order-print-table .goods-table td {
    border: none;
    font-size: 14px;
    padding: 8px;
    text-align: center;
}
.order-print-table .goods-table th {
    background: #f7f7f7;
    font-weight: bold;
    color: #333;
}
.order-print-table .goods-table td.goods-name {
    text-align: left;
    line-height: 1.6;
    min-height: 40px;
    word-break: break-all;
    white-space: normal;
    vertical-align: top;
}
.order-print-table .goods-summary-row td {
    text-align: right;
    padding: 8px 8px 0 0;
    color: #333;
    font-size: 14px;
}
.order-print-buyer-msg {
    width: 96%;
    text-align: left;
    color: #333;
    font-size: 14px;
    margin-bottom: 24px;
}
@media (max-width: 900px) {
    .order-print-table, .order-print-buyer-msg {
        width: 100%;
        font-size: 13px;
    }
    .order-print-title {
        font-size: 16px;
    }
}

.order-print-table, .order-print-table tr, .order-print-table td, .order-print-table th {
    border: 1px solid #d8d8d8 !important;
    border-collapse: collapse;
}
.order-print-table {
    border-width: 1px;
}
.order-print-table tr {
    border-bottom: 1px solid #d8d8d8;
}
.order-print-table td, .order-print-table th {
    border-right: 1px solid #d8d8d8;
    border-bottom: 1px solid #d8d8d8;
}
.order-print-table td:last-child, .order-print-table th:last-child {
    border-right: 1px solid #d8d8d8;
}
.order-print-table tr:last-child td {
    border-bottom: 1px solid #d8d8d8;
}
.order-print-table .section-title {
    background: #fafafa;
    border-top: 1px solid #d8d8d8;
    border-bottom: 1px solid #d8d8d8;
}
.goods-table, .goods-table tr, .goods-table td, .goods-table th {
    border: 1px solid #d8d8d8 !important;
    border-collapse: collapse;
}
.goods-table th, .goods-table td {
    border-right: 1px solid #d8d8d8;
    border-bottom: 1px solid #d8d8d8;
}
.goods-table th:last-child, .goods-table td:last-child {
    border-right: 1px solid #d8d8d8;
}
.goods-table tr:last-child td {
    border-bottom: 1px solid #d8d8d8;
}

.logistics-box {
    width: 560px;
    height: 396px;
    font-size: 13px;
    overflow-y: auto;
    padding: 20px;
}

.logistics-tooltip {
    box-shadow: 0 12px 32px 0 rgba(0,0,0,0.18), 12px 0 32px 0 rgba(0,0,0,0.15) !important;
    border-radius: 8px !important;
    background: #fff !important;
    border: 1px solid #d8d8d8 !important;
}
.logistics-tooltip .logistics-box::-webkit-scrollbar {
    width: 4px;
}
.logistics-tooltip .logistics-box::-webkit-scrollbar-track {
    background: transparent;
}
.logistics-tooltip .logistics-box::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}
.logistics-tooltip .logistics-box::-webkit-scrollbar-button {
    display: none;
}


/* 自定义确认按钮样式 */
.el-message-box__btns .el-button--danger {
    background-color: #EF436D !important;
    border-color: #EF436D !important;
}

.el-message-box__btns .el-button--danger:hover {
    background-color: #d63d5f !important;
    border-color: #d63d5f !important;
}

/* 覆盖Element UI确认对话框的内边距 */
.el-message-box--center {
    padding-bottom: 12px !important;
}

@media print {
    .order-print-table .goods-name {
        min-height: 40px !important;
        word-break: break-all !important;
        white-space: normal !important;
        vertical-align: top !important;
    }
}

.channel-item.disabled {
    background: #f5f5f5 !important;
    color: #bbb !important;
    border: 1px dashed #ccc !important;
    cursor: not-allowed !important;
    pointer-events: none;
    opacity: 0.7;
}

/* 通用自定义滚动条样式，适用于所有有纵向滚动条的容器 */
.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
}
.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
.custom-scrollbar::-webkit-scrollbar-button {
    display: none;
}
.custom-scrollbar .el-dialog__body {
    padding: 0;
}

/* 固定表格头部，只让表格主体滚动 */
.el-table.is-scrolling-none {
    height: 686px !important;
    overflow: hidden !important;
}

.el-table.is-scrolling-none .el-table__header-wrapper {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    background: #fff !important;
    border-bottom: 1px solid #ebeef5 !important;
}

.el-table.is-scrolling-none .el-table__body-wrapper {
    height: calc(686px - 48px) !important; /* 减去表头高度 */
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* 为表格主体滚动区域应用自定义滚动条样式 */
.el-table.custom-scrollbar .el-table__body-wrapper::-webkit-scrollbar {
    width: 4px !important;
}

.el-table.custom-scrollbar .el-table__body-wrapper::-webkit-scrollbar-track {
    background: transparent !important;
}

.el-table.custom-scrollbar .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #c1c1c1 !important;
    border-radius: 2px !important;
}

.el-table.custom-scrollbar .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8 !important;
}

.el-table.custom-scrollbar .el-table__body-wrapper::-webkit-scrollbar-button {
    display: none !important;
}

/* 确保表格头部有背景色 */
.el-table.is-scrolling-none .el-table__header {
    background: #fff !important;
}

.el-table.is-scrolling-none .el-table__header th {
    background: #fff7f9 !important;
    border-bottom: 1px solid #ebeef5 !important;
}

/* 分隔線樣式 */
.col-line {
    width: 2px;
    background: #f7f7f7;
    height: calc(100% + 24px + 34px);
    align-self: stretch;
    flex-shrink: 0;
}

/*用戶中心*/
.user {
    display: flex;
    justify-content: space-between;
    width: 222px;
    height: 58px;
    background: #FFF7F9;
    font-size: 13px;
    color: #3D3D3D;
    padding: 11px 12px;
    border-radius: 4px 4px 0 0;
}

.user .user-info {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 18px;
    color: #333333;
}

.log-out-btn {
    display: flex;
    align-items: center;
}

.home-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 30;
}

.send {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 222px;
    height: 64px;
    background: #FFFFFF;
    font-size: 13px;
    color: #3D3D3D;
    padding: 22px 12px;
    border-radius: 4px;
    margin: 6px 0 ;
    cursor: pointer;
}

.send:hover {
    background: #F7F7F7;
}

.send-input .el-input__inner {
    width: 215px;
    border-right: none;
    background: #ffffff;
}

.send-btn {
    border-left: none;
    background-color: #fff5f7;
}

.search-result-container {
    max-height: 500px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.send .send-info {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
    font-size: 18px;
    font-weight: 500;
    color: #3D3D3D;
}

.send-content, .calc-content {
    position: relative;
    margin: 0 auto;
    margin-top: -122px;
    width: 800px;
    min-height: 306px;
    border-radius: 16px;
    background: #FFFFFF;
    padding: 32px;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1);
    z-index: 20;
}

.send-title {
    font-size: 18px;
    font-weight: 500;
    color: #333333; 
    margin-bottom: 20px;
}

.send-search-input {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 14px;
    border: 1px solid #EF436D
}

.send-search-input:focus {
    outline: none;
}

.search-container {
    width: 464px;
    margin: 0 auto;
    background: #FFFFFF;
}

.search-input-container {
    position: relative;
    margin-bottom: 16px;
    height: 48px;
    margin-top: 62px;
}

.search-input {
    width: 100%;
    height: 40px;
    border: 1px solid #fff5f7;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 14px;
    background-color: #ffffff;
}

.search-input:focus {
    /* outline: none; */
    border-color: #fff5f7;
}

.search-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 0 4px 4px 0;
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-result-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 16px;
}

.search-result-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.product-item {
    width: 133px;
    height: 172px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 6px;
    margin-bottom: 10px;
    /* border: 1px solid #EBEEF5; */
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
    padding: 4px;
    border: 1px dashed #cccccc;
}

.product-item:hover {
    border-color: #EF436D;
}

.product-item.active {
    border-color: #EF436D;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.product-image {
    width: 100%;
    height: 124px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-title {
    font-size: 13px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-detail {
    /* margin-top: 24px; */
    /* padding: 20px; */
    background: #FFFFFF;
    /* border-radius: 4px; */
    /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
}

.product-detail-title {
    font-size: 18px;
    color: #ef436d;
    margin-bottom: 24px;
    text-align: center;
}

.product-question {
    font-size: 14px;
    color: #333;
    margin-bottom: 12px;
}

.question-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.radio-group {
    display: flex;
    gap: 24px;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-option input {
    margin-right: 8px;
}

.submit-btn {
    width: 80px;
    height: 32px;
    background: #EF436D;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.divider {
    height: 1px;
    background-color: #E5E5E5;
    margin: 16px 0 16px -20px;
    width: calc(100% + 20px);
}

.shipping-options {
    margin-top: 16px;
}

.shipping-title, .tax-title, .import-title {
    font-size: 13px;
    color: #333333;
    margin-bottom: 12px;
    position: relative;
    display: flex;
    align-items: center;
}

.shipping-title::before, .tax-title::before, .import-title::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 12px;
    background: #EF436D;
    margin-right: 8px;
    border-radius: 16px;
}

.shipping-method {
    padding-left: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.shipping-method-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.shipping-left,.shipping-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.shipping-icon {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
}
.shipping-icon img {
    width: 100%;
    height: 100%;
}

.shipping-name {
    color: #333;
    font-size: 14px;
}

.shipping-status {
    font-size: 13px;
}

.tax-info, .import-info {
    margin-top: 16px;
    position: relative;
}

.tax-content, .import-content {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.6;
}

.tax-content.orange {
    color: #E37318;
}

.import-content {
    color: #2BA471;
    font-size: 13px;
}

.import-alert {
    font-size: 13px;
    color: #D54941;
    margin-top: 8px;
    line-height: 1.5;
}


.calc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 222px;
    height: 64px;
    background: #FFFFFF;
    font-size: 13px;
    color: #3D3D3D;
    padding: 22px 12px;
    border-radius: 4px;
}

.calc:hover {
    background: #F7F7F7;
}

.calc .calc-info {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
    font-size: 18px;
    font-weight: 500;
    color: #3D3D3D;
}

.send-icon { width: 20px; height: 20px;}

.calc-icon {  width: 20px; height: 20px;}

.send-icon img { width: 100%; height: 100%;}

.calc-icon img { width: 100%; height: 100%;}

/* 在文件末尾添加计算运费页面的样式 */

/* 计算运费页面样式 */
.calc-box {
    margin: 0 auto;
    width: 464px;
    background: #FFFFFF;
}

.tool-bg {
    width: 100%;
    display: inline-flex; /* 或 inline-block */
    position: relative;
}

.tool-bg img {
    display: block;
    width: 100%;
    height: auto;
}

.tool-bg span {
    position: absolute;
    top: 23%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 36px;
    font-weight: 500;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.8);
    letter-spacing: 4px;
    pointer-events: none;
    white-space: nowrap;
}

.calc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calc-title {
    font-size: 20px;
    color: #3D3D3D;
}

.calc-view-link {
    font-size: 13px;
    color: #EF436D;
    text-decoration: none;
}

.transport-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.transport-tab {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    position: relative;
}

.transport-tab.active {
    color: #EF436D;
    font-weight: 500;
}

.transport-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #EF436D;
}

.calc-form {
    margin-bottom: 20px;
}

.form-item {
    margin-bottom: 12px;
}

.form-label {
    font-size: 13px;
    color: #333;
    margin-bottom: 6px;
}

.required {
    color: #EF436D;
}

.calc-form .input-group {
    display: flex;
    gap: 14px;
}

.calc-input {
    width: 110px;
    height: 24px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 12px;
}

.calc-input.weight {
    width: 358px;
}

.tax-radio {
    font-size: 12px;
    display: flex;
    gap: 20px;
    margin-top: 8px;
}

.tax-radio .radio-option {
    display: flex;
    align-items: center;
    gap: 2px;
}

.calc-checkbox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
}

.calc-checkbox label {
    display: flex;
    align-items: center;
    gap: 5px;
}

.combine-fee-btn {
    background-color: #EF436D;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    cursor: pointer;
}

.fee-note {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 12px;
    font-size: 13px;
}

.fee-note .icon {
    width: 18px;
    height: 18px;
}

.fee-note .icon img {
    width: 100%;
    height: 100%;
}


.note-text {
    color: #999;
    font-size: 12px;
    margin-left: auto;
}

.fee-table {
    width: 100%;
    border-radius: 16px;
    border-collapse: collapse;
    margin-bottom: 20px;
    border: 1px solid #E5E5E5;
    overflow: hidden;
}

.fee-row {
    display: flex;
    align-items: center;
    height: 48px;
    border-bottom: 1px solid #e5e5e5;
}

.fee-row:last-child {
    border-bottom: none;
}

.fee-row.header {
    background-color: #FFF7F9;
    font-size: 13px;
    font-weight: 500;
}

.fee-row > div {
    padding: 8px;
    flex: 1;
    font-size: 13px;
    display: flex;
    flex-direction: column;
}

.city-name {
    width: 80px;
    flex: none;
    color: #333;
    font-weight: 500;
}

.price-cell {
    /* width: 92px; */
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 2px 0;
}

.fee-row.header .price-cell {
    font-size: 13px;
    height: 24px;
    font-weight: normal;
    display: flex;
    align-items: center;
}

.tag-label {
    display: inline-block;
    width: 52px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 8px;
    font-size: 13px;
}

.warehouse-tag {
    margin-left: -24px;
    background-color: #fde5eb;
    color: #EF436D !important;
}

.air-price {
    background: #FFF1E9;
    color: #E37318 !important;
}

.sea-price {
    background: #E3F9E9;
    color: #2BA471 !important;
}

.price-cell .price {
    font-size: 16px;
    font-weight: 500;
    color: #EF436D;
    /* margin-bottom: 4px; */
}

.price-formula {
    width: 100%;
    font-size: 10px;
    color: #666;
}

.price {
    font-weight: 500;
    color: #EF436D;
}

.section-header {
    position: relative;
    display: flex;
    align-items: center;
    margin: 16px 0 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.section-header::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background-color: #EF436D;
    margin-right: 8px;
    border-radius: 2px;
}

.price-total {
    font-size: 16px;
    color: #EF436D;
}

.total-price {
    font-weight: 500;
}

.wooden-box-section {
    margin: 20px 0;
}

.wooden-box-options {
    display: flex;
    gap: 20px;
}

.wooden-box-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 4px;
    min-width: 120px;
}

.wooden-icon {
    width: 44px;
    height: 44px;
    background-color: #FFF5F7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wooden-icon img {
    width: 16px;
    height: 16px;
}

.wooden-price {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.wooden-price span {
    font-size: 13px;
}

.wooden-price .price {
    font-size: 16px;
}

.send-method {
    margin: 20px 0;
}


.package-icon {
    width: 60px;
    height: 60px;
    background-color: #f0f0f0;
    border-radius: 4px;
}

.fee-description {
    margin: 20px 0;
    font-size: 13px;
    line-height: 1.5;
    color: #666;
}

.fee-description p {
    margin-bottom: 10px;
}

.fee-description table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.fee-description table td {
    padding: 8px;
    border: 1px solid #eee;
}

.fee-description table td:first-child {
    width: 30%;
}

.note-red {
    color: #F10107;
    font-size: 10px;
}

/* 送货方式样式 */
.send-method {
    margin: 20px 0;
}

.send-method-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;
}

.send-method-item-first {
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    padding: 24px;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
}

.send-method-item-second {
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #2BA471;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
}

.method-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.method-icon img {
    max-width: 100%;
    max-height: 100%;
}

.method-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    flex: 1;
}

.method-desc {
    font-size: 10px;
    color: #666;
    text-align: right;
    line-height: 1.5;
}

.method-status {
    font-size: 14px;
    color: #2BA471;
    text-align: right;
}

/* 棧板費用表格样式 */
.fee-description .calc-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 6px;
    overflow: hidden;
    margin: 12px 0;
    box-shadow: 0 0 0 1px #eaeaea;
    background-color: #fff;
    table-layout: fixed;
    border-spacing: 0;
    display: table;
}

.fee-description .calc-table tr {
    border-bottom: 1px solid #f5f5f5;
    display: table-row;
}

.fee-description .calc-table tr:last-child {
    border-bottom: none;
}

.fee-description .calc-table td {
    padding: 16px 20px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    width: 50%;
    border: none;
    display: table-cell;
    vertical-align: middle;
}

.fee-description .calc-table td:first-child {
    text-align: left;
    font-weight: 500;
}

.fee-description .calc-table td:last-child {
    text-align: right;
    color: #333;
}

.calc-tooltip {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 312px;
    min-height: 112px;
}

.calc-tooltip-send-method {
    width: 312px;
    max-height: 500px;
    overflow-y: auto;
    padding-right: 8px;
}

.tooltip-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
}


.section-send-title {
    font-size: 12px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.8);
    margin-bottom: 4px;
    text-indent: 0.5em;
}

.section-send-content {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 1.5;
}

.price-highlight {
    color: #EF436D;
    font-weight: 500;
}

.tooltip-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    background-color: #e1f5fe;
    border: 1px solid #c6d7df;
    margin: 8px 0;
    border-radius: 4px;
    overflow: hidden;
}

.tooltip-table th,
.tooltip-table td {
    padding: 8px;
    border: 1px solid #c6d7df;
    color: #333;
    text-align: left;
}

.tooltip-table .table-header {
    background-color: #e1f5fe;
}

.tooltip-table th {
    color: #333;
}

.tooltip-notes {
    font-size: 12px;
    color: #666666;
    line-height: 1.5;
}

.tooltip-notes p {
    margin-bottom: 4px;
}

.calc-box-shadow {
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    padding-right: 0 !important;
}

.tooltip-icon {
    width: 36;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0; 
    top: -8px;
    cursor: pointer;
}

.my-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.nav-tabs {
    display: flex;
    gap: 20px;
    border-bottom: 1px solid #e4e7ed;
    width: 100%;
}

.nav-tab {
    font-size: 14px;
    color: #666;
    cursor: pointer;
    padding: 5px 0;
    margin-right: 30px;
    height: 40px;
    line-height: 40px;
    position: relative;
}

.nav-tab.active {
    color: #409eff;
    font-weight: bold;
}

.nav-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #409eff;
}

.nav-search {
    position: absolute;
    right: 0;
    top: 5px;
    display: flex;
    align-items: center;
}

.nav-search .search-input {
    width: 300px;
    height: 30px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 30px 0 15px;
    font-size: 12px;
    outline: none;
}

.nav-search .search-icon {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: #ef436d;
    cursor: pointer;
    padding: 0;
}

/* 自定义确认按钮样式 */
.el-message-box__btns .el-button--danger {
    background-color: #EF436D !important;
    border-color: #EF436D !important;
}

.el-message-box__btns .el-button--danger:hover {
    background-color: #d63d5f !important;
    border-color: #d63d5f !important;
}

/* 覆盖Element UI确认对话框的内边距 */
.el-message-box--center {
    padding-bottom: 12px !important;
}

/* 自定义el-popconfirm按钮样式 */
.el-popconfirm__action {
    display: flex !important;
    justify-content: flex-end !important;
}

.el-popconfirm__action .el-button {
    margin-left: 10px !important;
    padding: 7px 15px !important;
}

.el-popconfirm__action .el-button--primary {
    background-color: #EF436D !important;
    border-color: #EF436D !important;
    color: #FFFFFF !important;
}

.el-popconfirm__action .el-button--primary:hover {
    background-color: #d63d5f !important;
    border-color: #d63d5f !important;
}

/* LINE Binding Dialog Styles */
.line-binding-dialog .el-dialog {
  width: 904px !important;
  height: 536px !important;
  border-radius: 8px;
  overflow: hidden;
}

.line-binding-dialog .el-dialog__body {
  padding: 24px 32px;
}

.line-binding-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.line-binding-logo {
  height: 40px;
  display: flex;
  align-items: center;
}

.line-binding-logo img {
  height: 100%;
  max-width: 100%;
}

.line-binding-title {
  color: #00b900;
  font-size: 16px;
  font-weight: normal;
  margin-left: 8px;
}

.line-binding-steps {
  display: flex;
  justify-content: center;
  margin-bottom: 72px;
}



.line-binding-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  margin: 0 50px;
}

.line-binding-step-arrow {
    width: 32px;
    height: 16px;
    margin-bottom: 24px;
}

.line-binding-step-arrow img {
    width: 100%;
    height: 100%;
}

.line-binding-step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  margin-bottom: 18px;
}

.line-binding-step-number.active {
  background-color: #22B573;
}

.line-binding-step-number.inactive {
  background-color: #D0D0D0;
}

.line-binding-step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 300px;
}

.line-binding-step-image {
  width: 225px;
  height: 128px;
  margin-bottom: 20px;
}

.line-binding-step-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.line-binding-step-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
}

.line-binding-step-link {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.line-binding-step-link a {
  color: #ff6c9d;
  text-decoration: none;
}

.line-binding-footer {
  display: flex;
  justify-content: flex-end;
}

.line-binding-footer .el-button {
    background-color: #ef436d;
    color: white;
    border-color: #ef436d;
    border-radius: 20px;
    padding: 10px 30px;
}

.filter-btns {
    display: flex;
    gap: 16px;
    margin-left: auto;
    flex-wrap: wrap; /* 允许按钮在必要时换行 */
}

/* 修复v-loading加载动画位置问题 */
.loading-container {
    position: relative !important;
    min-height: 400px;
    width: 100% !important;
}

.transport-box .loading-container {
    position: relative !important;
    display: block !important;
}

.transport-box [v-loading] {
    position: relative !important;
    min-height: 200px;
}

/* 确保Element UI的loading遮罩正确显示 */
.transport-box .el-loading-parent--relative {
    position: relative !important;
}

.transport-box .el-loading-mask {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 2000 !important;
    margin: 0 !important;
    transform: none !important;
}

/* 加载图标居中样式 */
.transport-box .el-loading-spinner {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
}

.transport-box .el-loading-spinner .el-loading-text {
    margin-top: 15px !important;
}

/* 额外确保loading组件正确定位 */
.transport-box .loading-container.el-loading-parent--relative {
    position: relative !important;
    overflow: hidden !important;
}

.transport-box .loading-container .el-loading-mask {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: auto !important;
    height: auto !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保loading图标在容器中心 */
.transport-box .loading-container .el-loading-spinner {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: auto !important;
    height: auto !important;
}

/* 新的div表格样式 - activeIndex === 3 专用 */
.parcel-div-table {
    width: 100%;
    min-height: 638px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #ebeef5;
}

.parcel-table-header {
    display: grid;
    grid-template-columns: 33.5% 21% 10% 9% 7.5% 298px;
    background: #fff7f9;
    border-bottom: 1px solid #e8e8e8;
    color: #666666;
    font-size: 14px;
    height: 50px;
    align-items: center;
    justify-content: start;
    padding: 0 16px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.parcel-table-header-cell {
    padding: 0 16px;
    display: flex;
    align-items: center;
}

.parcel-table-row {
    border-bottom: 2px solid #EBEEF5;
    transition: background-color 0.2s ease;
}

/* 自定义表格数据区域滚动 */
.parcel-table-data-container {
    max-height: calc(686px - 50px); /* 减去表头高度 */
    overflow-y: auto;
}

/* 为自定义表格数据容器应用自定义滚动条样式 */
.parcel-table-data-container.custom-scrollbar::-webkit-scrollbar {
    width: 4px !important;
}

.parcel-table-data-container.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent !important;
}

.parcel-table-data-container.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #c1c1c1 !important;
    border-radius: 2px !important;
}

.parcel-table-data-container.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8 !important;
}

.parcel-table-data-container.custom-scrollbar::-webkit-scrollbar-button {
    display: none !important;
}

/* 为 activeIndex === 2 和 activeIndex === 3 的容器强化自定义滚动条样式 */
.loading-container.custom-scrollbar::-webkit-scrollbar {
    width: 4px !important;
}

.loading-container.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent !important;
}

.loading-container.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #c1c1c1 !important;
    border-radius: 2px !important;
}

.loading-container.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8 !important;
}

.loading-container.custom-scrollbar::-webkit-scrollbar-button {
    display: none !important;
}

/* 防止 activeIndex === 2 出现横向滚动条 */
.loading-container.custom-scrollbar {
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

/* 确保 await-box 内的元素不会导致横向溢出 */
.await-box * {
    box-sizing: border-box;
}

.await-box > div {
    max-width: 100%;
    word-wrap: break-word;
}

/* 防止长文本导致横向溢出 */
.await-box .fs13,
.await-box .fs14,
.await-box .fs18 {
    word-break: break-all;
    word-wrap: break-word;
    max-width: 100%;
}

/* 确保flex容器内的元素能够正确换行 */
.await-box div[style*="display: flex"] {
    flex-wrap: wrap;
}

/* 为地址编辑按钮文本设置最大宽度 */
.await-box .pointer {
    max-width: 200px;
    word-break: break-all;
    white-space: normal;
}

/* 强制约束整个容器，防止任何横向溢出 */
.loading-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* 确保所有子元素都遵循容器宽度限制 */
.loading-container * {
    max-width: 100%;
    box-sizing: border-box;
}

.parcel-order-content {
    display: flex;
    width: 100%;
    align-items: stretch;
}

.parcel-data-columns {
    flex: 1;
    width: 74%;
    display: flex;
    flex-direction: column;
}

.parcel-row-content {
    display: grid;
    grid-template-columns: 42% 22% 12% 12% 12%;
    min-height: 80px;
    flex: 1;
    padding: 12px 16px;
    align-items: center;
    justify-content: start;
    border-bottom: 1px solid #f5f5f5;
}

.parcel-row-content:last-child {
    border-bottom: none;
}

.parcel-actions-column {
    width: 298px;
    border-left: 1px solid #f5f5f5;
}

.parcel-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    padding: 12px 16px;
    border-bottom: 1px solid #f4f4f4;

}

.parcel-cell {
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.parcel-cell-goods {
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}



.parcel-goods-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.parcel-goods-tag {
    background: #fee;
    color: #f56c6c;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    border: 1px solid #f56c6c;
    margin-right: 14px;
}

.parcel-goods-date {
    color: #666666;
    font-size: 13px;
    margin-right: 12px;
}

.parcel-goods-order {
    color: #333333;
    font-size: 13px;
}

.parcel-goods-items {
    margin: 12px 0;
}

.parcel-goods-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.parcel-goods-image {
    width: 72px;
    height: 72px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.parcel-goods-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.parcel-goods-info {
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 16px;
}

.parcel-goods-name {
    color: #EF436D;
    font-size: 14px;
}

.parcel-goods-desc {
    color: #333333;
    font-size: 12px;
}

.parcel-logistics-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f4f4f4;
    color: #666666;
    font-size: 13px;
}

.parcel-logistics-info img {
    width: 14px;
    height: 14px;
}

.parcel-logistics-number {
    color: #ef436d;
    text-decoration: underline;
}

.parcel-logistics-status {
    color: #ef436d;
    font-weight: 500;
}

.parcel-courier-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333333;
    font-size: 13px;
}

.parcel-transport-method {
    color: #333333;
    font-size: 14px;
    text-align: center;
}

.parcel-quantity {
    color: #333333;
    font-size: 14px;
    text-align: center;
}

.parcel-weight {
    color: #333333;
    font-size: 14px;
    text-align: center;
}

.parcel-actions {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.parcel-actions > .el-popconfirm {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.parcel-action-delete {
    display: flex;
    justify-content: flex-end;
}

.parcel-delete-btn {
    color: #d54941;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
}

.parcel-confirm-receipt {
    color: #ef436d;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100% - 82px);
}

.parcel-row-logistics {
    padding: 0 16px;
    border-top: 1px solid #f4f4f4;
    display: flex;
    justify-content: space-between;
    height: 40px;
    border-bottom: 1px solid #f4f4f4;
}

.parcel-footer-left {
    display: flex;
    gap: 48px;
}

.parcel-footer-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666666;
}

.parcel-footer-value {
    color: #333333;
    font-weight: 500;
}

.parcel-footer-right {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #666666;
}

.parcel-declarant-info {
    color: #333333;
    font-weight: 500;
}

.parcel-weight-summary {
    position: absolute;
    bottom: 16px;
    left: 16px;
    font-size: 13px;
    color: #666666;
}

.parcel-weight-value {
    color: #333333;
    font-weight: 500;
}

.parcel-order-info {
    height: 82px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;
    padding: 0 16px;
    border-radius: 4px;
    border-top: 1px solid #f4f4f4;
}

.parcel-order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.parcel-order-label {
    color: #666666;
}

.parcel-order-value {
    color: #333333;
    font-weight: 500;
}

.parcel-footer-logistics {
    display: flex;
    align-items: center;
    color: #666666;
    font-size: 13px;
}

.parcel-footer-logistics img {
    width: 14px;
    height: 14px;
}

.parcel-footer-logistics-number {
    color: #ef436d;
    text-decoration: underline;
    margin-right: 8px;
}

.parcel-footer-logistics-status {
    color: #ef436d;
    font-weight: 500;
}

.parcel-footer-weight {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666666;
    font-size: 13px;
}

.parcel-footer-weight-value {
    color: #333333;
    font-weight: 500;
}

.parcel-footer-declarant {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666666;
    font-size: 13px;
}

.parcel-footer-declarant-info {
    color: #333333;
    font-weight: 500;
}

.parcel-row-remarks {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    color: #666666;
    font-size: 13px;
}

.logistics-share {
    display: flex; justify-content: center; align-items: center; width: 24px; height: 24px;
}

.el-icon-share:hover {
    color: #ef436d !important;
}








