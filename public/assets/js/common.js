/**
 * 公共JavaScript函數庫
 */

/**
 * 打開LINE官方帳號，固定在右上角
 */
function openLine() {
    // 計算窗口位置，使其顯示在右上角
    const width = 488;
    const height = 570;
    const left = window.screen.width - width;
    const top = 0; // 或者一個小值，如導航欄高度
    
    // 打開新窗口，指定尺寸和位置
    const lineWindow = window.open('https://lin.ee/3qltGe9', 'lineWindow', 
        `width=${width},height=${height},left=${left},top=${top},resizable=no,scrollbars=no`);
    
    // 嘗試添加CSS來隱藏滾動條
    if (lineWindow) {
        try {
            lineWindow.addEventListener('DOMContentLoaded', function() {
                // 創建一個style元素
                const styleEl = lineWindow.document.createElement('style');
                styleEl.textContent = `
                    html, body {
                        overflow-x: hidden !important;
                        width: ${width}px !important;
                        margin: 0 auto !important;
                    }
                `;
                // 將style元素添加到頭部
                lineWindow.document.head.appendChild(styleEl);
            });
        } catch (e) {
            // 跨域限制可能會阻止訪問外部窗口
            console.log('無法修改LINE窗口樣式，可能是由於跨域限制');
        }
    }
} 