// 导航链接高亮处理
document.addEventListener('DOMContentLoaded', function () {
    var navLinks = document.querySelectorAll('.nav-link');
    var current = window.location.pathname.toLowerCase();
    var found = false;
    
    // 检查是否有父导航标记
    var parentNavElement = document.querySelector('[data-parent-nav]');
    var parentNav = parentNavElement ? parentNavElement.getAttribute('data-parent-nav').toLowerCase() : null;
    
    // 检查sessionStorage中是否有存储的activeNav
    var storedActiveNav = sessionStorage.getItem('activeNav');
    
    navLinks.forEach(function (link) {
        var href = link.getAttribute('href').toLowerCase();
        var img = link.querySelector('img[data-icon]');
        var iconKey = img ? img.getAttribute('data-icon') : '';
        // 使用相对路径
        var normalSrc = '/assets/img/pc/new_index/' + iconKey + '.svg';
        var activeSrc = '/assets/img/pc/new_index/active_' + iconKey + '.svg';

        // 判断是否当前页面或父导航页面或存储的活动导航
        if (current === href || 
            current.indexOf(href) !== -1 || 
            (parentNav && href.indexOf(parentNav) !== -1) || 
            (storedActiveNav && href.indexOf(storedActiveNav) !== -1)) {
            
            navLinks.forEach(function (l) {
                l.classList.remove('active');
                var i = l.querySelector('img[data-icon]');
                if (i) {
                    var key = i.getAttribute('data-icon');
                    i.src = '/assets/img/pc/new_index/' + key + '.svg';
                }
            });
            link.classList.add('active');
            if (img) img.src = activeSrc;
            found = true;
            
            // 如果是通过存储的activeNav匹配到的，使用后清除它
            if (storedActiveNav && href.indexOf(storedActiveNav) !== -1) {
                sessionStorage.removeItem('activeNav');
            }
        }
    });
    // 如果没有匹配到，默认高亮第一个
    if (!found && navLinks.length > 0) {
        navLinks.forEach(function (l) {
            l.classList.remove('active');
            var i = l.querySelector('img[data-icon]');
            if (i) {
                var key = i.getAttribute('data-icon');
                i.src = '/assets/img/pc/new_index/' + key + '.svg';
            }
        });
        navLinks[0].classList.add('active');
        var firstImg = navLinks[0].querySelector('img[data-icon]');
        if (firstImg) {
            var key = firstImg.getAttribute('data-icon');
            firstImg.src = '/assets/img/pc/new_index/active_' + key + '.svg';
        }
    }
}); 