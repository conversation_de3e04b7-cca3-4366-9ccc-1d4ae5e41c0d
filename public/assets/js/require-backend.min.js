if(function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";function i(t,e,i){var n,o,a=(i=i||ut).createElement("script");if(a.text=t,e)for(n in ht)(o=e[n]||e.getAttribute&&e.getAttribute(n))&&a.setAttribute(n,o);i.head.appendChild(a).parentNode.removeChild(a)}function n(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?nt[ot.call(t)]||"object":typeof t}function o(t){var e=!!t&&"length"in t&&t.length,i=n(t);return!ct(t)&&!dt(t)&&("array"===i||0===e||"number"==typeof e&&0<e&&e-1 in t)}function a(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}function s(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}function r(t,e,i){return ct(e)?mt.grep(t,function(t,n){return!!e.call(t,n,t)!==i}):e.nodeType?mt.grep(t,function(t){return t===e!==i}):"string"!=typeof e?mt.grep(t,function(t){return-1<it.call(e,t)!==i}):mt.filter(e,t,i)}function l(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}function c(t){return t}function d(t){throw t}function u(t,e,i,n){var o;try{t&&ct(o=t.promise)?o.call(t).done(e).fail(i):t&&ct(o=t.then)?o.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}function h(){ut.removeEventListener("DOMContentLoaded",h),t.removeEventListener("load",h),mt.ready()}function p(t,e){return e.toUpperCase()}function f(t){return t.replace(It,"ms-").replace(Nt,p)}function m(){this.expando=mt.expando+m.uid++}function g(t,e,i){var n,o;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(zt,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i="true"===(o=i)||"false"!==o&&("null"===o?null:o===+o+""?+o:Ht.test(o)?JSON.parse(o):o)}catch(t){}Yt.set(t,e,i)}else i=void 0;return i}function v(t,e,i,n){var o,a,s=20,r=n?function(){return n.cur()}:function(){return mt.css(t,e,"")},l=r(),c=i&&i[3]||(mt.cssNumber[e]?"":"px"),d=t.nodeType&&(mt.cssNumber[e]||"px"!==c&&+l)&&Ut.exec(mt.css(t,e));if(d&&d[3]!==c){for(l/=2,c=c||d[3],d=+l||1;s--;)mt.style(t,e,d+c),(1-a)*(1-(a=r()/l||.5))<=0&&(s=0),d/=a;d*=2,mt.style(t,e,d+c),i=i||[]}return i&&(d=+d||+l||0,o=i[1]?d+(i[1]+1)*i[2]:+i[2],n&&(n.unit=c,n.start=d,n.end=o)),o}function y(t,e){for(var i,n,o,a,s,r,l,c=[],d=0,u=t.length;d<u;d++)(n=t[d]).style&&(i=n.style.display,e?("none"===i&&(c[d]=jt.get(n,"display")||null,c[d]||(n.style.display="")),""===n.style.display&&Xt(n)&&(c[d]=(l=s=a=void 0,s=(o=n).ownerDocument,r=o.nodeName,(l=Qt[r])||(a=s.body.appendChild(s.createElement(r)),l=mt.css(a,"display"),a.parentNode.removeChild(a),"none"===l&&(l="block"),Qt[r]=l)))):"none"!==i&&(c[d]="none",jt.set(n,"display",i)));for(d=0;d<u;d++)null!=c[d]&&(t[d].style.display=c[d]);return t}function b(t,e){var i;return i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&a(t,e)?mt.merge([t],i):i}function x(t,e){for(var i=0,n=t.length;i<n;i++)jt.set(t[i],"globalEval",!e||jt.get(e[i],"globalEval"))}function w(t,e,i,o,a){for(var s,r,l,c,d,u,h=e.createDocumentFragment(),p=[],f=0,m=t.length;f<m;f++)if((s=t[f])||0===s)if("object"===n(s))mt.merge(p,s.nodeType?[s]:s);else if(ne.test(s)){for(r=r||h.appendChild(e.createElement("div")),l=(te.exec(s)||["",""])[1].toLowerCase(),c=ie[l]||ie._default,r.innerHTML=c[1]+mt.htmlPrefilter(s)+c[2],u=c[0];u--;)r=r.lastChild;mt.merge(p,r.childNodes),(r=h.firstChild).textContent=""}else p.push(e.createTextNode(s));for(h.textContent="",f=0;s=p[f++];)if(o&&-1<mt.inArray(s,o))a&&a.push(s);else if(d=Vt(s),r=b(h.appendChild(s),"script"),d&&x(r),i)for(u=0;s=r[u++];)ee.test(s.type||"")&&i.push(s);return h}function k(){return!0}function _(){return!1}function C(t,e,i,n,o,a){var s,r;if("object"==typeof e){for(r in"string"!=typeof i&&(n=n||i,i=void 0),e)C(t,r,i,n,e[r],a);return t}if(null==n&&null==o?(o=i,n=i=void 0):null==o&&("string"==typeof i?(o=n,n=void 0):(o=n,n=i,i=void 0)),!1===o)o=_;else if(!o)return t;return 1===a&&(s=o,(o=function(t){return mt().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=mt.guid++)),t.each(function(){mt.event.add(this,e,o,n,i)})}function S(t,e,i){i?(jt.set(t,e,!1),mt.event.add(t,e,{namespace:!1,handler:function(t){var i,n=jt.get(this,e);if(1&t.isTrigger&&this[e]){if(n)(mt.event.special[e]||{}).delegateType&&t.stopPropagation();else if(n=J.call(arguments),jt.set(this,e,n),this[e](),i=jt.get(this,e),jt.set(this,e,!1),n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i}else n&&(jt.set(this,e,mt.event.trigger(n[0],n.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=k)}})):void 0===jt.get(t,e)&&mt.event.add(t,e,k)}function T(t,e){return a(t,"table")&&a(11!==e.nodeType?e:e.firstChild,"tr")&&mt(t).children("tbody")[0]||t}function D(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function $(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function E(t,e){var i,n,o,a,s,r;if(1===e.nodeType){if(jt.hasData(t)&&(r=jt.get(t).events))for(o in jt.remove(e,"handle events"),r)for(i=0,n=r[o].length;i<n;i++)mt.event.add(e,o,r[o][i]);Yt.hasData(t)&&(a=Yt.access(t),s=mt.extend({},a),Yt.set(e,s))}}function A(t,e,n,o){e=tt(e);var a,s,r,l,c,d,u=0,h=t.length,p=h-1,f=e[0],m=ct(f);if(m||1<h&&"string"==typeof f&&!lt.checkClone&&se.test(f))return t.each(function(i){var a=t.eq(i);m&&(e[0]=f.call(this,i,a.html())),A(a,e,n,o)});if(h&&(s=(a=w(e,t[0].ownerDocument,!1,t,o)).firstChild,1===a.childNodes.length&&(a=s),s||o)){for(l=(r=mt.map(b(a,"script"),D)).length;u<h;u++)c=a,u!==p&&(c=mt.clone(c,!0,!0),l&&mt.merge(r,b(c,"script"))),n.call(t[u],c,u);if(l)for(d=r[r.length-1].ownerDocument,mt.map(r,$),u=0;u<l;u++)c=r[u],ee.test(c.type||"")&&!jt.access(c,"globalEval")&&mt.contains(d,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?mt._evalUrl&&!c.noModule&&mt._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},d):i(c.textContent.replace(re,""),c,d))}return t}function O(t,e,i){for(var n,o=e?mt.filter(e,t):t,a=0;null!=(n=o[a]);a++)i||1!==n.nodeType||mt.cleanData(b(n)),n.parentNode&&(i&&Vt(n)&&x(b(n,"script")),n.parentNode.removeChild(n));return t}function F(t,e,i){var n,o,a,s,r=ce.test(e),l=t.style;return(i=i||de(t))&&(s=i.getPropertyValue(e)||i[e],r&&s&&(s=s.replace(xt,"$1")||void 0),""!==s||Vt(t)||(s=mt.style(t,e)),!lt.pixelBoxStyles()&&le.test(s)&&he.test(e)&&(n=l.width,o=l.minWidth,a=l.maxWidth,l.minWidth=l.maxWidth=l.width=s,s=i.width,l.width=n,l.minWidth=o,l.maxWidth=a)),void 0!==s?s+"":s}function P(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}function M(t){return mt.cssProps[t]||me[t]||(t in fe?t:me[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=pe.length;i--;)if((t=pe[i]+e)in fe)return t}(t)||t)}function L(t,e,i){var n=Ut.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function I(t,e,i,n,o,a){var s="width"===e?1:0,r=0,l=0,c=0;if(i===(n?"border":"content"))return 0;for(;s<4;s+=2)"margin"===i&&(c+=mt.css(t,i+Wt[s],!0,o)),n?("content"===i&&(l-=mt.css(t,"padding"+Wt[s],!0,o)),"margin"!==i&&(l-=mt.css(t,"border"+Wt[s]+"Width",!0,o))):(l+=mt.css(t,"padding"+Wt[s],!0,o),"padding"!==i?l+=mt.css(t,"border"+Wt[s]+"Width",!0,o):r+=mt.css(t,"border"+Wt[s]+"Width",!0,o));return!n&&0<=a&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-a-l-r-.5))||0),l+c}function N(t,e,i){var n=de(t),o=(!lt.boxSizingReliable()||i)&&"border-box"===mt.css(t,"boxSizing",!1,n),s=o,r=F(t,e,n),l="offset"+e[0].toUpperCase()+e.slice(1);if(le.test(r)){if(!i)return r;r="auto"}return(!lt.boxSizingReliable()&&o||!lt.reliableTrDimensions()&&a(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===mt.css(t,"display",!1,n))&&t.getClientRects().length&&(o="border-box"===mt.css(t,"boxSizing",!1,n),(s=l in t)&&(r=t[l])),(r=parseFloat(r)||0)+I(t,e,i||(o?"border":"content"),s,n,r)+"px"}function R(t,e,i,n,o){return new R.prototype.init(t,e,i,n,o)}function j(){xe&&(!1===ut.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(j):t.setTimeout(j,mt.fx.interval),mt.fx.tick())}function Y(){return t.setTimeout(function(){be=void 0}),be=Date.now()}function H(t,e){var i,n=0,o={height:t};for(e=e?1:0;n<4;n+=2-e)o["margin"+(i=Wt[n])]=o["padding"+i]=t;return e&&(o.opacity=o.width=t),o}function z(t,e,i){for(var n,o=(B.tweeners[e]||[]).concat(B.tweeners["*"]),a=0,s=o.length;a<s;a++)if(n=o[a].call(i,e,t))return n}function B(t,e,i){var n,o,a=0,s=B.prefilters.length,r=mt.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=be||Y(),i=Math.max(0,c.startTime+c.duration-e),n=1-(i/c.duration||0),a=0,s=c.tweens.length;a<s;a++)c.tweens[a].run(n);return r.notifyWith(t,[c,n,i]),n<1&&s?i:(s||r.notifyWith(t,[c,1,0]),r.resolveWith(t,[c]),!1)},c=r.promise({elem:t,props:mt.extend({},e),opts:mt.extend(!0,{specialEasing:{},easing:mt.easing._default},i),originalProperties:e,originalOptions:i,startTime:be||Y(),duration:i.duration,tweens:[],createTween:function(e,i){var n=mt.Tween(t,c.opts,e,i,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(n),n},stop:function(e){var i=0,n=e?c.tweens.length:0;if(o)return this;for(o=!0;i<n;i++)c.tweens[i].run(1);return e?(r.notifyWith(t,[c,1,0]),r.resolveWith(t,[c,e])):r.rejectWith(t,[c,e]),this}}),d=c.props;for((!function(t,e){var i,n,o,a,s;for(i in t)if(o=e[n=f(i)],a=t[i],Array.isArray(a)&&(o=a[1],a=t[i]=a[0]),i!==n&&(t[n]=a,delete t[i]),(s=mt.cssHooks[n])&&"expand"in s)for(i in a=s.expand(a),delete t[n],a)i in t||(t[i]=a[i],e[i]=o);else e[n]=o}(d,c.opts.specialEasing));a<s;a++)if(n=B.prefilters[a].call(c,t,d,c.opts))return ct(n.stop)&&(mt._queueHooks(c.elem,c.opts.queue).stop=n.stop.bind(n)),n;return mt.map(d,z,c),ct(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),mt.fx.timer(mt.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}function U(t){return(t.match(Ft)||[]).join(" ")}function W(t){return t.getAttribute&&t.getAttribute("class")||""}function q(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(Ft)||[]}function V(t,e,i,o){var a;if(Array.isArray(e))mt.each(e,function(e,n){i||Le.test(t)?o(t,n):V(t+"["+("object"==typeof n&&null!=n?e:"")+"]",n,i,o)});else if(i||"object"!==n(e))o(t,e);else for(a in e)V(t+"["+a+"]",e[a],i,o)}function G(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,o=0,a=e.toLowerCase().match(Ft)||[];if(ct(i))for(;n=a[o++];)"+"===n[0]?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function X(t,e,i,n){function o(r){var l;return a[r]=!0,mt.each(t[r]||[],function(t,r){var c=r(e,i,n);return"string"!=typeof c||s||a[c]?s?!(l=c):void 0:(e.dataTypes.unshift(c),o(c),!1)}),l}var a={},s=t===qe;return o(e.dataTypes[0])||!a["*"]&&o("*")}function Q(t,e){var i,n,o=mt.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((o[i]?t:n||(n={}))[i]=e[i]);return n&&mt.extend(!0,t,n),t}var K=[],Z=Object.getPrototypeOf,J=K.slice,tt=K.flat?function(t){return K.flat.call(t)}:function(t){return K.concat.apply([],t)},et=K.push,it=K.indexOf,nt={},ot=nt.toString,at=nt.hasOwnProperty,st=at.toString,rt=st.call(Object),lt={},ct=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},dt=function(t){return null!=t&&t===t.window},ut=t.document,ht={type:!0,src:!0,nonce:!0,noModule:!0},pt="3.7.1",ft=/HTML$/i,mt=function(t,e){return new mt.fn.init(t,e)};mt.fn=mt.prototype={jquery:pt,constructor:mt,length:0,toArray:function(){return J.call(this)},get:function(t){return null==t?J.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=mt.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return mt.each(this,t)},map:function(t){return this.pushStack(mt.map(this,function(e,i){return t.call(e,i,e)}))},slice:function(){return this.pushStack(J.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(mt.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(mt.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(0<=i&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:et,sort:K.sort,splice:K.splice},mt.extend=mt.fn.extend=function(){var t,e,i,n,o,a,s=arguments[0]||{},r=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[r]||{},r++),"object"==typeof s||ct(s)||(s={}),r===l&&(s=this,r--);r<l;r++)if(null!=(t=arguments[r]))for(e in t)n=t[e],"__proto__"!==e&&s!==n&&(c&&n&&(mt.isPlainObject(n)||(o=Array.isArray(n)))?(i=s[e],a=o&&!Array.isArray(i)?[]:o||mt.isPlainObject(i)?i:{},o=!1,s[e]=mt.extend(c,a,n)):void 0!==n&&(s[e]=n));return s},mt.extend({expando:"jQuery"+(pt+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,i;return!(!t||"[object Object]"!==ot.call(t)||(e=Z(t))&&("function"!=typeof(i=at.call(e,"constructor")&&e.constructor)||st.call(i)!==rt))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){i(t,{nonce:e&&e.nonce},n)},each:function(t,e){var i,n=0;if(o(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},text:function(t){var e,i="",n=0,o=t.nodeType;if(!o)for(;e=t[n++];)i+=mt.text(e);return 1===o||11===o?t.textContent:9===o?t.documentElement.textContent:3===o||4===o?t.nodeValue:i},makeArray:function(t,e){var i=e||[];return null!=t&&(o(Object(t))?mt.merge(i,"string"==typeof t?[t]:t):et.call(i,t)),i},inArray:function(t,e,i){return null==e?-1:it.call(e,t,i)},isXMLDoc:function(t){var e=t&&t.namespaceURI,i=t&&(t.ownerDocument||t).documentElement;return!ft.test(e||i&&i.nodeName||"HTML")},merge:function(t,e){for(var i=+e.length,n=0,o=t.length;n<i;n++)t[o++]=e[n];return t.length=o,t},grep:function(t,e,i){for(var n=[],o=0,a=t.length,s=!i;o<a;o++)!e(t[o],o)!==s&&n.push(t[o]);return n},map:function(t,e,i){var n,a,s=0,r=[];if(o(t))for(n=t.length;s<n;s++)null!=(a=e(t[s],s,i))&&r.push(a);else for(s in t)null!=(a=e(t[s],s,i))&&r.push(a);return tt(r)},guid:1,support:lt}),"function"==typeof Symbol&&(mt.fn[Symbol.iterator]=K[Symbol.iterator]),mt.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){nt["[object "+e+"]"]=e.toLowerCase()});var gt=K.pop,vt=K.sort,yt=K.splice,bt="[\\x20\\t\\r\\n\\f]",xt=new RegExp("^"+bt+"+|((?:^|[^\\\\])(?:\\\\.)*)"+bt+"+$","g");mt.contains=function(t,e){var i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(t.contains?t.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))};var wt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;mt.escapeSelector=function(t){return(t+"").replace(wt,s)};var kt=ut,_t=et;!function(){function e(t,i,n,o){var a,s,r,d,p,f,m,g=i&&i.ownerDocument,v=i?i.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==v&&9!==v&&11!==v)return n;if(!o&&(c(i),i=i||S,D)){if(11!==v&&(p=tt.exec(t)))if(a=p[1]){if(9===v){if(!(r=i.getElementById(a)))return n;if(r.id===a)return A.call(n,r),n}else if(g&&(r=g.getElementById(a))&&e.contains(i,r)&&r.id===a)return A.call(n,r),n}else{if(p[2])return A.apply(n,i.getElementsByTagName(t)),n;if((a=p[3])&&i.getElementsByClassName)return A.apply(n,i.getElementsByClassName(a)),n}if(!(N[t+" "]||$&&$.test(t))){if(m=t,g=i,1===v&&(q.test(t)||W.test(t))){for((g=et.test(t)&&l(i.parentNode)||i)==i&&lt.scope||((d=i.getAttribute("id"))?d=mt.escapeSelector(d):i.setAttribute("id",d=O)),s=(f=u(t)).length;s--;)f[s]=(d?"#"+d:":scope")+" "+h(f[s]);m=f.join(",")}try{return A.apply(n,g.querySelectorAll(m)),n}catch(i){N(t,!0)}finally{d===O&&i.removeAttribute("id")}}}return b(t.replace(xt,"$1"),i,n,o)}function i(){var t=[];return function e(i,n){return t.push(i+" ")>w.cacheLength&&delete e[t.shift()],e[i+" "]=n}}function n(t){return t[O]=!0,t}function o(t){var e=S.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function s(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&rt(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function r(t){return n(function(e){return e=+e,n(function(i,n){for(var o,a=t([],i.length,e),s=a.length;s--;)i[o=a[s]]&&(i[o]=!(n[o]=i[o]))})})}function l(t){return t&&void 0!==t.getElementsByTagName&&t}function c(t){var i,n=t?t.ownerDocument||t:kt;return n!=S&&9===n.nodeType&&n.documentElement&&(T=(S=n).documentElement,D=!mt.isXMLDoc(S),E=T.matches||T.webkitMatchesSelector||T.msMatchesSelector,T.msMatchesSelector&&kt!=S&&(i=S.defaultView)&&i.top!==i&&i.addEventListener("unload",st),lt.getById=o(function(t){return T.appendChild(t).id=mt.expando,!S.getElementsByName||!S.getElementsByName(mt.expando).length}),lt.disconnectedMatch=o(function(t){return E.call(t,"*")}),lt.scope=o(function(){return S.querySelectorAll(":scope")}),lt.cssHas=o(function(){try{return S.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}}),lt.getById?(w.filter.ID=function(t){var e=t.replace(nt,ot);return function(t){return t.getAttribute("id")===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&D){var i=e.getElementById(t);return i?[i]:[]}}):(w.filter.ID=function(t){var e=t.replace(nt,ot);return function(t){var i=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&D){var i,n,o,a=e.getElementById(t);if(a){if((i=a.getAttributeNode("id"))&&i.value===t)return[a];for(o=e.getElementsByName(t),n=0;a=o[n++];)if((i=a.getAttributeNode("id"))&&i.value===t)return[a]}return[]}}),w.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},w.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&D)return e.getElementsByClassName(t)},$=[],o(function(t){var e;T.appendChild(t).innerHTML="<a id='"+O+"' href='' disabled='disabled'></a><select id='"+O+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||$.push("\\["+bt+"*(?:value|"+j+")"),t.querySelectorAll("[id~="+O+"-]").length||$.push("~="),t.querySelectorAll("a#"+O+"+*").length||$.push(".#.+[+~]"),t.querySelectorAll(":checked").length||$.push(":checked"),(e=S.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),T.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&$.push(":enabled",":disabled"),(e=S.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||$.push("\\["+bt+"*name"+bt+"*="+bt+"*(?:''|\"\")")}),lt.cssHas||$.push(":has"),$=$.length&&new RegExp($.join("|")),R=function(t,i){if(t===i)return C=!0,0;var n=!t.compareDocumentPosition-!i.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)==(i.ownerDocument||i)?t.compareDocumentPosition(i):1)||!lt.sortDetached&&i.compareDocumentPosition(t)===n?t===S||t.ownerDocument==kt&&e.contains(kt,t)?-1:i===S||i.ownerDocument==kt&&e.contains(kt,i)?1:_?it.call(_,t)-it.call(_,i):0:4&n?-1:1)}),S}function d(){}function u(t,i){var n,o,a,s,r,l,c,d=L[t+" "];if(d)return i?0:d.slice(0);for(r=t,l=[],c=w.preFilter;r;){for(s in n&&!(o=U.exec(r))||(o&&(r=r.slice(o[0].length)||r),l.push(a=[])),n=!1,(o=W.exec(r))&&(n=o.shift(),a.push({value:n,type:o[0].replace(xt," ")}),r=r.slice(n.length)),w.filter)!(o=X[s].exec(r))||c[s]&&!(o=c[s](o))||(n=o.shift(),a.push({value:n,type:s,matches:o}),r=r.slice(n.length));if(!n)break}return i?r.length:r?e.error(t):L(t,l).slice(0)}function h(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function p(t,e,i){var n=e.dir,o=e.next,s=o||n,r=i&&"parentNode"===s,l=P++;return e.first?function(e,i,o){for(;e=e[n];)if(1===e.nodeType||r)return t(e,i,o);return!1}:function(e,i,c){var d,u,h=[F,l];if(c){for(;e=e[n];)if((1===e.nodeType||r)&&t(e,i,c))return!0}else for(;e=e[n];)if(1===e.nodeType||r)if(u=e[O]||(e[O]={}),o&&a(e,o))e=e[n]||e;else{if((d=u[s])&&d[0]===F&&d[1]===l)return h[2]=d[2];if((u[s]=h)[2]=t(e,i,c))return!0}return!1}}function f(t){return 1<t.length?function(e,i,n){for(var o=t.length;o--;)if(!t[o](e,i,n))return!1;return!0}:t[0]}function m(t,e,i,n,o){for(var a,s=[],r=0,l=t.length,c=null!=e;r<l;r++)(a=t[r])&&(i&&!i(a,n,o)||(s.push(a),c&&e.push(r)));return s}function g(t,i,o,a,s,r){return a&&!a[O]&&(a=g(a)),s&&!s[O]&&(s=g(s,r)),n(function(n,r,l,c){var d,u,h,p,f=[],g=[],v=r.length,y=n||function(t,i,n){for(var o=0,a=i.length;o<a;o++)e(t,i[o],n);return n}(i||"*",l.nodeType?[l]:l,[]),b=!t||!n&&i?y:m(y,f,t,l,c);if(o?o(b,p=s||(n?t:v||a)?[]:r,l,c):p=b,a)for(d=m(p,g),a(d,[],l,c),u=d.length;u--;)(h=d[u])&&(p[g[u]]=!(b[g[u]]=h));if(n){if(s||t){if(s){for(d=[],u=p.length;u--;)(h=p[u])&&d.push(b[u]=h);s(null,p=[],d,c)}for(u=p.length;u--;)(h=p[u])&&-1<(d=s?it.call(n,h):f[u])&&(n[d]=!(r[d]=h))}}else p=m(p===r?p.splice(v,p.length):p),s?s(null,r,p,c):A.apply(r,p)})}function v(t){for(var e,i,n,o=t.length,a=w.relative[t[0].type],s=a||w.relative[" "],r=a?1:0,l=p(function(t){return t===e},s,!0),c=p(function(t){return-1<it.call(e,t)},s,!0),d=[function(t,i,n){var o=!a&&(n||i!=k)||((e=i).nodeType?l(t,i,n):c(t,i,n));return e=null,o}];r<o;r++)if(i=w.relative[t[r].type])d=[p(f(d),i)];else{if((i=w.filter[t[r].type].apply(null,t[r].matches))[O]){for(n=++r;n<o&&!w.relative[t[n].type];n++);return g(1<r&&f(d),1<r&&h(t.slice(0,r-1).concat({value:" "===t[r-2].type?"*":""})).replace(xt,"$1"),i,r<n&&v(t.slice(r,n)),n<o&&v(t=t.slice(n)),n<o&&h(t))}d.push(i)}return f(d)}function y(t,e){var i,o,a,s,r,l,d=[],h=[],p=I[t+" "];if(!p){for(e||(e=u(t)),i=e.length;i--;)(p=v(e[i]))[O]?d.push(p):h.push(p);(p=I(t,(o=h,s=0<(a=d).length,r=0<o.length,l=function(t,e,i,n,l){var d,u,h,p=0,f="0",g=t&&[],v=[],y=k,b=t||r&&w.find.TAG("*",l),x=F+=null==y?1:Math.random()||.1,_=b.length;for(l&&(k=e==S||e||l);f!==_&&null!=(d=b[f]);f++){if(r&&d){for(u=0,e||d.ownerDocument==S||(c(d),i=!D);h=o[u++];)if(h(d,e||S,i)){A.call(n,d);break}l&&(F=x)}s&&((d=!h&&d)&&p--,t&&g.push(d))}if(p+=f,s&&f!==p){for(u=0;h=a[u++];)h(g,v,e,i);if(t){if(0<p)for(;f--;)g[f]||v[f]||(v[f]=gt.call(n));v=m(v)}A.apply(n,v),l&&!t&&0<v.length&&1<p+a.length&&mt.uniqueSort(n)}return l&&(F=x,k=y),g},s?n(l):l))).selector=t}return p}function b(t,e,i,n){var o,a,s,r,c,d="function"==typeof t&&t,p=!n&&u(t=d.selector||t);if(i=i||[],1===p.length){if(2<(a=p[0]=p[0].slice(0)).length&&"ID"===(s=a[0]).type&&9===e.nodeType&&D&&w.relative[a[1].type]){if(!(e=(w.find.ID(s.matches[0].replace(nt,ot),e)||[])[0]))return i;d&&(e=e.parentNode),t=t.slice(a.shift().value.length)}for(o=X.needsContext.test(t)?0:a.length;o--&&(s=a[o],!w.relative[r=s.type]);)if((c=w.find[r])&&(n=c(s.matches[0].replace(nt,ot),et.test(a[0].type)&&l(e.parentNode)||e))){if(a.splice(o,1),!(t=n.length&&h(a)))return A.apply(i,n),i;break}}return(d||y(t,p))(n,e,!D,i,!e||et.test(t)&&l(e.parentNode)||e),i}var x,w,k,_,C,S,T,D,$,E,A=_t,O=mt.expando,F=0,P=0,M=i(),L=i(),I=i(),N=i(),R=function(t,e){return t===e&&(C=!0),0},j="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",Y="(?:\\\\[\\da-fA-F]{1,6}"+bt+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",H="\\["+bt+"*("+Y+")(?:"+bt+"*([*^$|!~]?=)"+bt+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+Y+"))|)"+bt+"*\\]",z=":("+Y+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+H+")*)|.*)\\)|)",B=new RegExp(bt+"+","g"),U=new RegExp("^"+bt+"*,"+bt+"*"),W=new RegExp("^"+bt+"*([>+~]|"+bt+")"+bt+"*"),q=new RegExp(bt+"|>"),V=new RegExp(z),G=new RegExp("^"+Y+"$"),X={ID:new RegExp("^#("+Y+")"),CLASS:new RegExp("^\\.("+Y+")"),TAG:new RegExp("^("+Y+"|[*])"),ATTR:new RegExp("^"+H),PSEUDO:new RegExp("^"+z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+bt+"*(even|odd|(([+-]|)(\\d*)n|)"+bt+"*(?:([+-]|)"+bt+"*(\\d+)|))"+bt+"*\\)|)","i"),bool:new RegExp("^(?:"+j+")$","i"),needsContext:new RegExp("^"+bt+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+bt+"*((?:-\\d)?\\d*)"+bt+"*\\)|)(?=[^-]|$)","i")},Q=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,tt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,et=/[+~]/,nt=new RegExp("\\\\[\\da-fA-F]{1,6}"+bt+"?|\\\\([^\\r\\n\\f])","g"),ot=function(t,e){var i="0x"+t.slice(1)-65536;return e||(i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320))},st=function(){c()},rt=p(function(t){return!0===t.disabled&&a(t,"fieldset")},{dir:"parentNode",next:"legend"});try{A.apply(K=J.call(kt.childNodes),kt.childNodes),K[kt.childNodes.length].nodeType}catch(x){A={apply:function(t,e){_t.apply(t,J.call(e))},call:function(t){_t.apply(t,J.call(arguments,1))}}}for(x in e.matches=function(t,i){return e(t,null,null,i)},e.matchesSelector=function(t,i){if(c(t),D&&!N[i+" "]&&(!$||!$.test(i)))try{var n=E.call(t,i);if(n||lt.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){N(i,!0)}return 0<e(i,S,null,[t]).length},e.contains=function(t,e){return(t.ownerDocument||t)!=S&&c(t),mt.contains(t,e)},e.attr=function(t,e){(t.ownerDocument||t)!=S&&c(t);var i=w.attrHandle[e.toLowerCase()],n=i&&at.call(w.attrHandle,e.toLowerCase())?i(t,e,!D):void 0;return void 0!==n?n:t.getAttribute(e)},e.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},mt.uniqueSort=function(t){var e,i=[],n=0,o=0;if(C=!lt.sortStable,_=!lt.sortStable&&J.call(t,0),vt.call(t,R),C){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)yt.call(t,i[n],1)}return _=null,t},mt.fn.uniqueSort=function(){return this.pushStack(mt.uniqueSort(J.apply(this)))},(w=mt.expr={cacheLength:50,createPseudo:n,match:X,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(nt,ot),t[3]=(t[3]||t[4]||t[5]||"").replace(nt,ot),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return X.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&V.test(i)&&(e=u(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(nt,ot).toLowerCase();return"*"===t?function(){return!0}:function(t){return a(t,e)}},CLASS:function(t){var e=M[t+" "];return e||(e=new RegExp("(^|"+bt+")"+t+"("+bt+"|$)"))&&M(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,i,n){return function(o){var a=e.attr(o,t);return null==a?"!="===i:!i||(a+="","="===i?a===n:"!="===i?a!==n:"^="===i?n&&0===a.indexOf(n):"*="===i?n&&-1<a.indexOf(n):"$="===i?n&&a.slice(-n.length)===n:"~="===i?-1<(" "+a.replace(B," ")+" ").indexOf(n):"|="===i&&(a===n||a.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,i,n,o){var s="nth"!==t.slice(0,3),r="last"!==t.slice(-4),l="of-type"===e;return 1===n&&0===o?function(t){return!!t.parentNode}:function(e,i,c){var d,u,h,p,f,m=s!==r?"nextSibling":"previousSibling",g=e.parentNode,v=l&&e.nodeName.toLowerCase(),y=!c&&!l,b=!1;if(g){if(s){for(;m;){for(h=e;h=h[m];)if(l?a(h,v):1===h.nodeType)return!1;f=m="only"===t&&!f&&"nextSibling"}return!0}if(f=[r?g.firstChild:g.lastChild],r&&y){for(b=(p=(d=(u=g[O]||(g[O]={}))[t]||[])[0]===F&&d[1])&&d[2],h=p&&g.childNodes[p];h=++p&&h&&h[m]||(b=p=0)||f.pop();)if(1===h.nodeType&&++b&&h===e){u[t]=[F,p,b];break}}else if(y&&(b=p=(d=(u=e[O]||(e[O]={}))[t]||[])[0]===F&&d[1]),!1===b)for(;(h=++p&&h&&h[m]||(b=p=0)||f.pop())&&((l?!a(h,v):1!==h.nodeType)||!++b||(y&&((u=h[O]||(h[O]={}))[t]=[F,b]),h!==e)););return(b-=o)===n||b%n==0&&0<=b/n}}},PSEUDO:function(t,i){var o,a=w.pseudos[t]||w.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return a[O]?a(i):1<a.length?(o=[t,t,"",i],w.setFilters.hasOwnProperty(t.toLowerCase())?n(function(t,e){for(var n,o=a(t,i),s=o.length;s--;)t[n=it.call(t,o[s])]=!(e[n]=o[s])}):function(t){return a(t,0,o)}):a}},pseudos:{not:n(function(t){var e=[],i=[],o=y(t.replace(xt,"$1"));return o[O]?n(function(t,e,i,n){for(var a,s=o(t,null,n,[]),r=t.length;r--;)(a=s[r])&&(t[r]=!(e[r]=a))}):function(t,n,a){return e[0]=t,o(e,null,a,i),e[0]=null,!i.pop()}}),has:n(function(t){return function(i){return 0<e(t,i).length}}),contains:n(function(t){return t=t.replace(nt,ot),function(e){return-1<(e.textContent||mt.text(e)).indexOf(t)}}),lang:n(function(t){return G.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(nt,ot).toLowerCase(),function(e){var i;do{if(i=D?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===T},focus:function(t){return t===function(){try{return S.activeElement}catch(t){}}()&&S.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:s(!1),disabled:s(!0),checked:function(t){return a(t,"input")&&!!t.checked||a(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!w.pseudos.empty(t)},header:function(t){return Z.test(t.nodeName)},input:function(t){return Q.test(t.nodeName)},button:function(t){return a(t,"input")&&"button"===t.type||a(t,"button")},text:function(t){var e;return a(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:r(function(){return[0]}),last:r(function(t,e){return[e-1]}),eq:r(function(t,e,i){return[i<0?i+e:i]}),even:r(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:r(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:r(function(t,e,i){var n;for(n=i<0?i+e:e<i?e:i;0<=--n;)t.push(n);return t}),gt:r(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[x]=function(t){return function(e){return a(e,"input")&&e.type===t}}(x);for(x in{submit:!0,reset:!0})w.pseudos[x]=function(t){return function(e){return(a(e,"input")||a(e,"button"))&&e.type===t}}(x);d.prototype=w.filters=w.pseudos,w.setFilters=new d,lt.sortStable=O.split("").sort(R).join("")===O,c(),lt.sortDetached=o(function(t){return 1&t.compareDocumentPosition(S.createElement("fieldset"))}),mt.find=e,mt.expr[":"]=mt.expr.pseudos,mt.unique=mt.uniqueSort,e.compile=y,e.select=b,e.setDocument=c,e.tokenize=u,e.escape=mt.escapeSelector,e.getText=mt.text,e.isXML=mt.isXMLDoc,e.selectors=mt.expr,e.support=mt.support,e.uniqueSort=mt.uniqueSort}();var Ct=function(t,e,i){for(var n=[],o=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&mt(t).is(i))break;n.push(t)}return n},St=function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i},Tt=mt.expr.match.needsContext,Dt=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;mt.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?mt.find.matchesSelector(n,t)?[n]:[]:mt.find.matches(t,mt.grep(e,function(t){return 1===t.nodeType}))},mt.fn.extend({find:function(t){var e,i,n=this.length,o=this;if("string"!=typeof t)return this.pushStack(mt(t).filter(function(){for(e=0;e<n;e++)if(mt.contains(o[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)mt.find(t,o[e],i);return 1<n?mt.uniqueSort(i):i},filter:function(t){return this.pushStack(r(this,t||[],!1))},not:function(t){return this.pushStack(r(this,t||[],!0))},is:function(t){return!!r(this,"string"==typeof t&&Tt.test(t)?mt(t):t||[],!1).length}})
;var $t,Et=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(mt.fn.init=function(t,e,i){var n,o;if(!t)return this;if(i=i||$t,"string"==typeof t){if(!(n="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:Et.exec(t))||!n[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof mt?e[0]:e,mt.merge(this,mt.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:ut,!0)),Dt.test(n[1])&&mt.isPlainObject(e))for(n in e)ct(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}return(o=ut.getElementById(n[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):ct(t)?void 0!==i.ready?i.ready(t):t(mt):mt.makeArray(t,this)}).prototype=mt.fn,$t=mt(ut);var At=/^(?:parents|prev(?:Until|All))/,Ot={children:!0,contents:!0,next:!0,prev:!0};mt.fn.extend({has:function(t){var e=mt(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if(mt.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,o=this.length,a=[],s="string"!=typeof t&&mt(t);if(!Tt.test(t))for(;n<o;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(s?-1<s.index(i):1===i.nodeType&&mt.find.matchesSelector(i,t))){a.push(i);break}return this.pushStack(1<a.length?mt.uniqueSort(a):a)},index:function(t){return t?"string"==typeof t?it.call(mt(t),this[0]):it.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(mt.uniqueSort(mt.merge(this.get(),mt(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),mt.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return Ct(t,"parentNode")},parentsUntil:function(t,e,i){return Ct(t,"parentNode",i)},next:function(t){return l(t,"nextSibling")},prev:function(t){return l(t,"previousSibling")},nextAll:function(t){return Ct(t,"nextSibling")},prevAll:function(t){return Ct(t,"previousSibling")},nextUntil:function(t,e,i){return Ct(t,"nextSibling",i)},prevUntil:function(t,e,i){return Ct(t,"previousSibling",i)},siblings:function(t){return St((t.parentNode||{}).firstChild,t)},children:function(t){return St(t.firstChild)},contents:function(t){return null!=t.contentDocument&&Z(t.contentDocument)?t.contentDocument:(a(t,"template")&&(t=t.content||t),mt.merge([],t.childNodes))}},function(t,e){mt.fn[t]=function(i,n){var o=mt.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(o=mt.filter(n,o)),1<this.length&&(Ot[t]||mt.uniqueSort(o),At.test(t)&&o.reverse()),this.pushStack(o)}});var Ft=/[^\x20\t\r\n\f]+/g;mt.Callbacks=function(t){var e,i;t="string"==typeof t?(e=t,i={},mt.each(e.match(Ft)||[],function(t,e){i[e]=!0}),i):mt.extend({},t);var o,a,s,r,l=[],c=[],d=-1,u=function(){for(r=r||t.once,s=o=!0;c.length;d=-1)for(a=c.shift();++d<l.length;)!1===l[d].apply(a[0],a[1])&&t.stopOnFalse&&(d=l.length,a=!1);t.memory||(a=!1),o=!1,r&&(l=a?[]:"")},h={add:function(){return l&&(a&&!o&&(d=l.length-1,c.push(a)),function e(i){mt.each(i,function(i,o){ct(o)?t.unique&&h.has(o)||l.push(o):o&&o.length&&"string"!==n(o)&&e(o)})}(arguments),a&&!o&&u()),this},remove:function(){return mt.each(arguments,function(t,e){for(var i;-1<(i=mt.inArray(e,l,i));)l.splice(i,1),i<=d&&d--}),this},has:function(t){return t?-1<mt.inArray(t,l):0<l.length},empty:function(){return l&&(l=[]),this},disable:function(){return r=c=[],l=a="",this},disabled:function(){return!l},lock:function(){return r=c=[],a||o||(l=a=""),this},locked:function(){return!!r},fireWith:function(t,e){return r||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),o||u()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!s}};return h},mt.extend({Deferred:function(e){var i=[["notify","progress",mt.Callbacks("memory"),mt.Callbacks("memory"),2],["resolve","done",mt.Callbacks("once memory"),mt.Callbacks("once memory"),0,"resolved"],["reject","fail",mt.Callbacks("once memory"),mt.Callbacks("once memory"),1,"rejected"]],n="pending",o={state:function(){return n},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return mt.Deferred(function(e){mt.each(i,function(i,n){var o=ct(t[n[4]])&&t[n[4]];a[n[1]](function(){var t=o&&o.apply(this,arguments);t&&ct(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[n[0]+"With"](this,o?[t]:arguments)})}),t=null}).promise()},then:function(e,n,o){function a(e,i,n,o){return function(){var r=this,l=arguments,u=function(){var t,u;if(!(e<s)){if((t=n.apply(r,l))===i.promise())throw new TypeError("Thenable self-resolution");u=t&&("object"==typeof t||"function"==typeof t)&&t.then,ct(u)?o?u.call(t,a(s,i,c,o),a(s,i,d,o)):(s++,u.call(t,a(s,i,c,o),a(s,i,d,o),a(s,i,c,i.notifyWith))):(n!==c&&(r=void 0,l=[t]),(o||i.resolveWith)(r,l))}},h=o?u:function(){try{u()}catch(t){mt.Deferred.exceptionHook&&mt.Deferred.exceptionHook(t,h.error),s<=e+1&&(n!==d&&(r=void 0,l=[t]),i.rejectWith(r,l))}};e?h():(mt.Deferred.getErrorHook?h.error=mt.Deferred.getErrorHook():mt.Deferred.getStackHook&&(h.error=mt.Deferred.getStackHook()),t.setTimeout(h))}}var s=0;return mt.Deferred(function(t){i[0][3].add(a(0,t,ct(o)?o:c,t.notifyWith)),i[1][3].add(a(0,t,ct(e)?e:c)),i[2][3].add(a(0,t,ct(n)?n:d))}).promise()},promise:function(t){return null!=t?mt.extend(t,o):o}},a={};return mt.each(i,function(t,e){var s=e[2],r=e[5];o[e[1]]=s.add,r&&s.add(function(){n=r},i[3-t][2].disable,i[3-t][3].disable,i[0][2].lock,i[0][3].lock),s.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=s.fireWith}),o.promise(a),e&&e.call(a,a),a},when:function(t){var e=arguments.length,i=e,n=Array(i),o=J.call(arguments),a=mt.Deferred(),s=function(t){return function(i){n[t]=this,o[t]=1<arguments.length?J.call(arguments):i,--e||a.resolveWith(n,o)}};if(e<=1&&(u(t,a.done(s(i)).resolve,a.reject,!e),"pending"===a.state()||ct(o[i]&&o[i].then)))return a.then();for(;i--;)u(o[i],s(i),a.reject);return a.promise()}});var Pt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;mt.Deferred.exceptionHook=function(e,i){t.console&&t.console.warn&&e&&Pt.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,i)},mt.readyException=function(e){t.setTimeout(function(){throw e})};var Mt=mt.Deferred();mt.fn.ready=function(t){return Mt.then(t).catch(function(t){mt.readyException(t)}),this},mt.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--mt.readyWait:mt.isReady)||(mt.isReady=!0)!==t&&0<--mt.readyWait||Mt.resolveWith(ut,[mt])}}),mt.ready.then=Mt.then,"complete"===ut.readyState||"loading"!==ut.readyState&&!ut.documentElement.doScroll?t.setTimeout(mt.ready):(ut.addEventListener("DOMContentLoaded",h),t.addEventListener("load",h));var Lt=function(t,e,i,o,a,s,r){var l=0,c=t.length,d=null==i;if("object"===n(i))for(l in a=!0,i)Lt(t,e,l,i[l],!0,s,r);else if(void 0!==o&&(a=!0,ct(o)||(r=!0),d&&(r?(e.call(t,o),e=null):(d=e,e=function(t,e,i){return d.call(mt(t),i)})),e))for(;l<c;l++)e(t[l],i,r?o:o.call(t[l],l,e(t[l],i)));return a?t:d?e.call(t):c?e(t[0],i):s},It=/^-ms-/,Nt=/-([a-z])/g,Rt=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};m.uid=1,m.prototype={cache:function(t){var e=t[this.expando];return e||(e={},Rt(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,o=this.cache(t);if("string"==typeof e)o[f(e)]=i;else for(n in e)o[f(n)]=e[n];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][f(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(f):(e=f(e))in n?[e]:e.match(Ft)||[]).length;for(;i--;)delete n[e[i]]}(void 0===e||mt.isEmptyObject(n))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!mt.isEmptyObject(e)}};var jt=new m,Yt=new m,Ht=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,zt=/[A-Z]/g;mt.extend({hasData:function(t){return Yt.hasData(t)||jt.hasData(t)},data:function(t,e,i){return Yt.access(t,e,i)},removeData:function(t,e){Yt.remove(t,e)},_data:function(t,e,i){return jt.access(t,e,i)},_removeData:function(t,e){jt.remove(t,e)}}),mt.fn.extend({data:function(t,e){var i,n,o,a=this[0],s=a&&a.attributes;if(void 0===t){if(this.length&&(o=Yt.get(a),1===a.nodeType&&!jt.get(a,"hasDataAttrs"))){for(i=s.length;i--;)s[i]&&0===(n=s[i].name).indexOf("data-")&&(n=f(n.slice(5)),g(a,n,o[n]));jt.set(a,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each(function(){Yt.set(this,t)}):Lt(this,function(e){var i;if(a&&void 0===e)return void 0!==(i=Yt.get(a,t))?i:void 0!==(i=g(a,t))?i:void 0;this.each(function(){Yt.set(this,t,e)})},null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each(function(){Yt.remove(this,t)})}}),mt.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=jt.get(t,e),i&&(!n||Array.isArray(i)?n=jt.access(t,e,mt.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=mt.queue(t,e),n=i.length,o=i.shift(),a=mt._queueHooks(t,e);"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===e&&i.unshift("inprogress"),delete a.stop,o.call(t,function(){mt.dequeue(t,e)},a)),!n&&a&&a.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return jt.get(t,i)||jt.access(t,i,{empty:mt.Callbacks("once memory").add(function(){jt.remove(t,[e+"queue",i])})})}}),mt.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?mt.queue(this[0],t):void 0===e?this:this.each(function(){var i=mt.queue(this,t,e);mt._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&mt.dequeue(this,t)})},dequeue:function(t){return this.each(function(){mt.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,o=mt.Deferred(),a=this,s=this.length,r=function(){--n||o.resolveWith(a,[a])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";s--;)(i=jt.get(a[s],t+"queueHooks"))&&i.empty&&(n++,i.empty.add(r));return r(),o.promise(e)}});var Bt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ut=new RegExp("^(?:([+-])=|)("+Bt+")([a-z%]*)$","i"),Wt=["Top","Right","Bottom","Left"],qt=ut.documentElement,Vt=function(t){return mt.contains(t.ownerDocument,t)},Gt={composed:!0};qt.getRootNode&&(Vt=function(t){return mt.contains(t.ownerDocument,t)||t.getRootNode(Gt)===t.ownerDocument});var Xt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&Vt(t)&&"none"===mt.css(t,"display")},Qt={};mt.fn.extend({show:function(){return y(this,!0)},hide:function(){return y(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){Xt(this)?mt(this).show():mt(this).hide()})}});var Kt,Zt,Jt=/^(?:checkbox|radio)$/i,te=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ee=/^$|^module$|\/(?:java|ecma)script/i;Kt=ut.createDocumentFragment().appendChild(ut.createElement("div")),(Zt=ut.createElement("input")).setAttribute("type","radio"),Zt.setAttribute("checked","checked"),Zt.setAttribute("name","t"),Kt.appendChild(Zt),lt.checkClone=Kt.cloneNode(!0).cloneNode(!0).lastChild.checked,Kt.innerHTML="<textarea>x</textarea>",lt.noCloneChecked=!!Kt.cloneNode(!0).lastChild.defaultValue,Kt.innerHTML="<option></option>",lt.option=!!Kt.lastChild;var ie={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ie.tbody=ie.tfoot=ie.colgroup=ie.caption=ie.thead,ie.th=ie.td,lt.option||(ie.optgroup=ie.option=[1,"<select multiple='multiple'>","</select>"]);var ne=/<|&#?\w+;/,oe=/^([^.]*)(?:\.(.+)|)/;mt.event={global:{},add:function(t,e,i,n,o){var a,s,r,l,c,d,u,h,p,f,m,g=jt.get(t);if(Rt(t))for(i.handler&&(i=(a=i).handler,o=a.selector),o&&mt.find.matchesSelector(qt,o),i.guid||(i.guid=mt.guid++),(l=g.events)||(l=g.events=Object.create(null)),(s=g.handle)||(s=g.handle=function(e){return void 0!==mt&&mt.event.triggered!==e.type?mt.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(Ft)||[""]).length;c--;)p=m=(r=oe.exec(e[c])||[])[1],f=(r[2]||"").split(".").sort(),p&&(u=mt.event.special[p]||{},p=(o?u.delegateType:u.bindType)||p,u=mt.event.special[p]||{},d=mt.extend({type:p,origType:m,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&mt.expr.match.needsContext.test(o),namespace:f.join(".")},a),(h=l[p])||((h=l[p]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,n,f,s)||t.addEventListener&&t.addEventListener(p,s)),u.add&&(u.add.call(t,d),d.handler.guid||(d.handler.guid=i.guid)),o?h.splice(h.delegateCount++,0,d):h.push(d),mt.event.global[p]=!0)},remove:function(t,e,i,n,o){var a,s,r,l,c,d,u,h,p,f,m,g=jt.hasData(t)&&jt.get(t);if(g&&(l=g.events)){for(c=(e=(e||"").match(Ft)||[""]).length;c--;)if(p=m=(r=oe.exec(e[c])||[])[1],f=(r[2]||"").split(".").sort(),p){for(u=mt.event.special[p]||{},h=l[p=(n?u.delegateType:u.bindType)||p]||[],r=r[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=a=h.length;a--;)d=h[a],!o&&m!==d.origType||i&&i.guid!==d.guid||r&&!r.test(d.namespace)||n&&n!==d.selector&&("**"!==n||!d.selector)||(h.splice(a,1),d.selector&&h.delegateCount--,u.remove&&u.remove.call(t,d));s&&!h.length&&(u.teardown&&!1!==u.teardown.call(t,f,g.handle)||mt.removeEvent(t,p,g.handle),delete l[p])}else for(p in l)mt.event.remove(t,p+e[c],i,n,!0);mt.isEmptyObject(l)&&jt.remove(t,"handle events")}},dispatch:function(t){var e,i,n,o,a,s,r=new Array(arguments.length),l=mt.event.fix(t),c=(jt.get(this,"events")||Object.create(null))[l.type]||[],d=mt.event.special[l.type]||{};for(r[0]=l,e=1;e<arguments.length;e++)r[e]=arguments[e];if(l.delegateTarget=this,!d.preDispatch||!1!==d.preDispatch.call(this,l)){for(s=mt.event.handlers.call(this,l,c),e=0;(o=s[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,i=0;(a=o.handlers[i++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==a.namespace&&!l.rnamespace.test(a.namespace)||(l.handleObj=a,l.data=a.data,void 0!==(n=((mt.event.special[a.origType]||{}).handle||a.handler).apply(o.elem,r))&&!1===(l.result=n)&&(l.preventDefault(),l.stopPropagation()));return d.postDispatch&&d.postDispatch.call(this,l),l.result}},handlers:function(t,e){var i,n,o,a,s,r=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(a=[],s={},i=0;i<l;i++)void 0===s[o=(n=e[i]).selector+" "]&&(s[o]=n.needsContext?-1<mt(o,this).index(c):mt.find(o,this,null,[c]).length),s[o]&&a.push(n);a.length&&r.push({elem:c,handlers:a})}return c=this,l<e.length&&r.push({elem:c,handlers:e.slice(l)}),r},addProp:function(t,e){Object.defineProperty(mt.Event.prototype,t,{enumerable:!0,configurable:!0,get:ct(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[mt.expando]?t:new mt.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return Jt.test(e.type)&&e.click&&a(e,"input")&&S(e,"click",!0),!1},trigger:function(t){var e=this||t;return Jt.test(e.type)&&e.click&&a(e,"input")&&S(e,"click"),!0},_default:function(t){var e=t.target;return Jt.test(e.type)&&e.click&&a(e,"input")&&jt.get(e,"click")||a(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},mt.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},mt.Event=function(t,e){if(!(this instanceof mt.Event))return new mt.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?k:_,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&mt.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[mt.expando]=!0},mt.Event.prototype={constructor:mt.Event,isDefaultPrevented:_,isPropagationStopped:_,isImmediatePropagationStopped:_,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=k,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=k,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=k,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},mt.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},mt.event.addProp),mt.each({focus:"focusin",blur:"focusout"},function(t,e){function i(t){if(ut.documentMode){var i=jt.get(this,"handle"),n=mt.event.fix(t);n.type="focusin"===t.type?"focus":"blur",n.isSimulated=!0,i(t),n.target===n.currentTarget&&i(n)}else mt.event.simulate(e,t.target,mt.event.fix(t))}mt.event.special[t]={setup:function(){var n;if(S(this,t,!0),!ut.documentMode)return!1;(n=jt.get(this,e))||this.addEventListener(e,i),jt.set(this,e,(n||0)+1)},trigger:function(){return S(this,t),!0},teardown:function(){var t;if(!ut.documentMode)return!1;(t=jt.get(this,e)-1)?jt.set(this,e,t):(this.removeEventListener(e,i),jt.remove(this,e))},_default:function(e){return jt.get(e.target,t)},delegateType:e},mt.event.special[e]={setup:function(){var n=this.ownerDocument||this.document||this,o=ut.documentMode?this:n,a=jt.get(o,e);a||(ut.documentMode?this.addEventListener(e,i):n.addEventListener(t,i,!0)),jt.set(o,e,(a||0)+1)},teardown:function(){var n=this.ownerDocument||this.document||this,o=ut.documentMode?this:n,a=jt.get(o,e)-1;a?jt.set(o,e,a):(ut.documentMode?this.removeEventListener(e,i):n.removeEventListener(t,i,!0),jt.remove(o,e))}}}),mt.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){mt.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,o=t.handleObj;return n&&(n===this||mt.contains(this,n))||(t.type=o.origType,i=o.handler.apply(this,arguments),t.type=e),i}}}),mt.fn.extend({on:function(t,e,i,n){return C(this,t,e,i,n)},one:function(t,e,i,n){return C(this,t,e,i,n,1)},off:function(t,e,i){var n,o;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,mt(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=_),this.each(function(){mt.event.remove(this,t,i,e)})}});var ae=/<script|<style|<link/i,se=/checked\s*(?:[^=]|=\s*.checked.)/i,re=/^\s*<!\[CDATA\[|\]\]>\s*$/g;mt.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,o,a,s,r,l,c,d=t.cloneNode(!0),u=Vt(t);if(!(lt.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||mt.isXMLDoc(t)))for(s=b(d),n=0,o=(a=b(t)).length;n<o;n++)r=a[n],l=s[n],"input"===(c=l.nodeName.toLowerCase())&&Jt.test(r.type)?l.checked=r.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=r.defaultValue);if(e)if(i)for(a=a||b(t),s=s||b(d),n=0,o=a.length;n<o;n++)E(a[n],s[n]);else E(t,d);return 0<(s=b(d,"script")).length&&x(s,!u&&b(t,"script")),d},cleanData:function(t){for(var e,i,n,o=mt.event.special,a=0;void 0!==(i=t[a]);a++)if(Rt(i)){if(e=i[jt.expando]){if(e.events)for(n in e.events)o[n]?mt.event.remove(i,n):mt.removeEvent(i,n,e.handle);i[jt.expando]=void 0}i[Yt.expando]&&(i[Yt.expando]=void 0)}}}),mt.fn.extend({detach:function(t){return O(this,t,!0)},remove:function(t){return O(this,t)},text:function(t){return Lt(this,function(t){return void 0===t?mt.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return A(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||T(this,t).appendChild(t)})},prepend:function(){return A(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=T(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return A(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return A(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(mt.cleanData(b(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return mt.clone(this,t,e)})},html:function(t){return Lt(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!ae.test(t)&&!ie[(te.exec(t)||["",""])[1].toLowerCase()]){t=mt.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(mt.cleanData(b(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return A(this,arguments,function(e){var i=this.parentNode;mt.inArray(this,t)<0&&(mt.cleanData(b(this)),i&&i.replaceChild(e,this))},t)}}),mt.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){mt.fn[t]=function(t){for(var i,n=[],o=mt(t),a=o.length-1,s=0;s<=a;s++)i=s===a?this:this.clone(!0),mt(o[s])[e](i),et.apply(n,i.get());return this.pushStack(n)}});var le=new RegExp("^("+Bt+")(?!px)[a-z%]+$","i"),ce=/^--/,de=function(e){var i=e.ownerDocument.defaultView;return i&&i.opener||(i=t),i.getComputedStyle(e)},ue=function(t,e,i){var n,o,a={};for(o in e)a[o]=t.style[o],t.style[o]=e[o];for(o in n=i.call(t),e)t.style[o]=a[o];return n},he=new RegExp(Wt.join("|"),"i");!function(){function e(){if(d){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",d.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",qt.appendChild(c).appendChild(d);var e=t.getComputedStyle(d);n="1%"!==e.top,l=12===i(e.marginLeft),d.style.right="60%",s=36===i(e.right),o=36===i(e.width),d.style.position="absolute",a=12===i(d.offsetWidth/3),qt.removeChild(c),d=null}}function i(t){return Math.round(parseFloat(t))}var n,o,a,s,r,l,c=ut.createElement("div"),d=ut.createElement("div");d.style&&(d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",lt.clearCloneStyle="content-box"===d.style.backgroundClip,mt.extend(lt,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var e,i,n,o;return null==r&&(e=ut.createElement("table"),i=ut.createElement("tr"),n=ut.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",i.style.cssText="box-sizing:content-box;border:1px solid",i.style.height="1px",n.style.height="9px",n.style.display="block",qt.appendChild(e).appendChild(i).appendChild(n),o=t.getComputedStyle(i),r=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===i.offsetHeight,qt.removeChild(e)),r}}))}();var pe=["Webkit","Moz","ms"],fe=ut.createElement("div").style,me={},ge=/^(none|table(?!-c[ea]).+)/,ve={position:"absolute",visibility:"hidden",display:"block"},ye={letterSpacing:"0",fontWeight:"400"};mt.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=F(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,a,s,r=f(e),l=ce.test(e),c=t.style;if(l||(e=M(r)),s=mt.cssHooks[e]||mt.cssHooks[r],void 0===i)return s&&"get"in s&&void 0!==(o=s.get(t,!1,n))?o:c[e];"string"==(a=typeof i)&&(o=Ut.exec(i))&&o[1]&&(i=v(t,e,o),a="number"),null!=i&&i==i&&("number"!==a||l||(i+=o&&o[3]||(mt.cssNumber[r]?"":"px")),lt.clearCloneStyle||""!==i||0!==e.indexOf("background")||(c[e]="inherit"),s&&"set"in s&&void 0===(i=s.set(t,i,n))||(l?c.setProperty(e,i):c[e]=i))}},css:function(t,e,i,n){var o,a,s,r=f(e);return ce.test(e)||(e=M(r)),(s=mt.cssHooks[e]||mt.cssHooks[r])&&"get"in s&&(o=s.get(t,!0,i)),void 0===o&&(o=F(t,e,n)),"normal"===o&&e in ye&&(o=ye[e]),""===i||i?(a=parseFloat(o),!0===i||isFinite(a)?a||0:o):o}}),mt.each(["height","width"],function(t,e){mt.cssHooks[e]={get:function(t,i,n){if(i)return!ge.test(mt.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?N(t,e,n):ue(t,ve,function(){return N(t,e,n)})},set:function(t,i,n){var o,a=de(t),s=!lt.scrollboxSize()&&"absolute"===a.position,r=(s||n)&&"border-box"===mt.css(t,"boxSizing",!1,a),l=n?I(t,e,n,r,a):0;return r&&s&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(a[e])-I(t,e,"border",!1,a)-.5)),l&&(o=Ut.exec(i))&&"px"!==(o[3]||"px")&&(t.style[e]=i,i=mt.css(t,e)),L(0,i,l)}}}),mt.cssHooks.marginLeft=P(lt.reliableMarginLeft,function(t,e){if(e)return(parseFloat(F(t,"marginLeft"))||t.getBoundingClientRect().left-ue(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),mt.each({margin:"",padding:"",border:"Width"},function(t,e){mt.cssHooks[t+e]={expand:function(i){for(var n=0,o={},a="string"==typeof i?i.split(" "):[i];n<4;n++)o[t+Wt[n]+e]=a[n]||a[n-2]||a[0];return o}},"margin"!==t&&(mt.cssHooks[t+e].set=L)}),mt.fn.extend({css:function(t,e){return Lt(this,function(t,e,i){var n,o,a={},s=0;if(Array.isArray(e)){for(n=de(t),o=e.length;s<o;s++)a[e[s]]=mt.css(t,e[s],!1,n);return a}return void 0!==i?mt.style(t,e,i):mt.css(t,e)},t,e,1<arguments.length)}}),((mt.Tween=R).prototype={constructor:R,init:function(t,e,i,n,o,a){this.elem=t,this.prop=i,this.easing=o||mt.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=a||(mt.cssNumber[i]?"":"px")},cur:function(){var t=R.propHooks[this.prop];return t&&t.get?t.get(this):R.propHooks._default.get(this)},run:function(t){var e,i=R.propHooks[this.prop];return this.options.duration?this.pos=e=mt.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):R.propHooks._default.set(this),this}}).init.prototype=R.prototype,(R.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=mt.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){mt.fx.step[t.prop]?mt.fx.step[t.prop](t):1!==t.elem.nodeType||!mt.cssHooks[t.prop]&&null==t.elem.style[M(t.prop)]?t.elem[t.prop]=t.now:mt.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=R.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},mt.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},mt.fx=R.prototype.init,mt.fx.step={};var be,xe,we,ke,_e=/^(?:toggle|show|hide)$/,Ce=/queueHooks$/;mt.Animation=mt.extend(B,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return v(i.elem,t,Ut.exec(e),i),i}]},tweener:function(t,e){ct(t)?(e=t,t=["*"]):t=t.match(Ft);for(var i,n=0,o=t.length;n<o;n++)i=t[n],B.tweeners[i]=B.tweeners[i]||[],B.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,o,a,s,r,l,c,d,u="width"in e||"height"in e,h=this,p={},f=t.style,m=t.nodeType&&Xt(t),g=jt.get(t,"fxshow");for(n in i.queue||(null==(s=mt._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,r=s.empty.fire,s.empty.fire=function(){s.unqueued||r()}),s.unqueued++,h.always(function(){h.always(function(){s.unqueued--,mt.queue(t,"fx").length||s.empty.fire()})})),e)if(o=e[n],_e.test(o)){if(delete e[n],a=a||"toggle"===o,o===(m?"hide":"show")){if("show"!==o||!g||void 0===g[n])continue;m=!0}p[n]=g&&g[n]||mt.style(t,n)}if((l=!mt.isEmptyObject(e))||!mt.isEmptyObject(p))for(n in u&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=jt.get(t,"display")),"none"===(d=mt.css(t,"display"))&&(c?d=c:(y([t],!0),c=t.style.display||c,d=mt.css(t,"display"),y([t]))),("inline"===d||"inline-block"===d&&null!=c)&&"none"===mt.css(t,"float")&&(l||(h.done(function(){f.display=c}),null==c&&(d=f.display,c="none"===d?"":d)),f.display="inline-block")),i.overflow&&(f.overflow="hidden",h.always(function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]})),l=!1,p)l||(g?"hidden"in g&&(m=g.hidden):g=jt.access(t,"fxshow",{display:c}),a&&(g.hidden=!m),m&&y([t],!0),h.done(function(){for(n in m||y([t]),jt.remove(t,"fxshow"),p)mt.style(t,n,p[n])})),l=z(m?g[n]:0,n,h),n in g||(g[n]=l.start,m&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?B.prefilters.unshift(t):B.prefilters.push(t)}}),mt.speed=function(t,e,i){var n=t&&"object"==typeof t?mt.extend({},t):{complete:i||!i&&e||ct(t)&&t,duration:t,easing:i&&e||e&&!ct(e)&&e};return mt.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in mt.fx.speeds?n.duration=mt.fx.speeds[n.duration]:n.duration=mt.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){ct(n.old)&&n.old.call(this),n.queue&&mt.dequeue(this,n.queue)},n},mt.fn.extend({fadeTo:function(t,e,i,n){return this.filter(Xt).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var o=mt.isEmptyObject(t),a=mt.speed(e,i,n),s=function(){var e=B(this,mt.extend({},t),a);(o||jt.get(this,"finish"))&&e.stop(!0)};return s.finish=s,o||!1===a.queue?this.each(s):this.queue(a.queue,s)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,o=null!=t&&t+"queueHooks",a=mt.timers,s=jt.get(this);if(o)s[o]&&s[o].stop&&n(s[o]);else for(o in s)s[o]&&s[o].stop&&Ce.test(o)&&n(s[o]);for(o=a.length;o--;)a[o].elem!==this||null!=t&&a[o].queue!==t||(a[o].anim.stop(i),e=!1,a.splice(o,1));!e&&i||mt.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,i=jt.get(this),n=i[t+"queue"],o=i[t+"queueHooks"],a=mt.timers,s=n?n.length:0;for(i.finish=!0,mt.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===t&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<s;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish})}}),mt.each(["toggle","show","hide"],function(t,e){var i=mt.fn[e];mt.fn[e]=function(t,n,o){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(H(e,!0),t,n,o)}}),mt.each({slideDown:H("show"),slideUp:H("hide"),slideToggle:H("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){mt.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}}),mt.timers=[],mt.fx.tick=function(){var t,e=0,i=mt.timers;for(be=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||mt.fx.stop(),be=void 0},mt.fx.timer=function(t){mt.timers.push(t),mt.fx.start()},mt.fx.interval=13,mt.fx.start=function(){xe||(xe=!0,j())},mt.fx.stop=function(){xe=null},mt.fx.speeds={slow:600,fast:200,_default:400},mt.fn.delay=function(e,i){return e=mt.fx&&mt.fx.speeds[e]||e,i=i||"fx",
this.queue(i,function(i,n){var o=t.setTimeout(i,e);n.stop=function(){t.clearTimeout(o)}})},we=ut.createElement("input"),ke=ut.createElement("select").appendChild(ut.createElement("option")),we.type="checkbox",lt.checkOn=""!==we.value,lt.optSelected=ke.selected,(we=ut.createElement("input")).value="t",we.type="radio",lt.radioValue="t"===we.value;var Se,Te=mt.expr.attrHandle;mt.fn.extend({attr:function(t,e){return Lt(this,mt.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){mt.removeAttr(this,t)})}}),mt.extend({attr:function(t,e,i){var n,o,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return void 0===t.getAttribute?mt.prop(t,e,i):(1===a&&mt.isXMLDoc(t)||(o=mt.attrHooks[e.toLowerCase()]||(mt.expr.match.bool.test(e)?Se:void 0)),void 0!==i?null===i?void mt.removeAttr(t,e):o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:(t.setAttribute(e,i+""),i):o&&"get"in o&&null!==(n=o.get(t,e))?n:null==(n=mt.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!lt.radioValue&&"radio"===e&&a(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n=0,o=e&&e.match(Ft);if(o&&1===t.nodeType)for(;i=o[n++];)t.removeAttribute(i)}}),Se={set:function(t,e,i){return!1===e?mt.removeAttr(t,i):t.setAttribute(i,i),i}},mt.each(mt.expr.match.bool.source.match(/\w+/g),function(t,e){var i=Te[e]||mt.find.attr;Te[e]=function(t,e,n){var o,a,s=e.toLowerCase();return n||(a=Te[s],Te[s]=o,o=null!=i(t,e,n)?s:null,Te[s]=a),o}});var De=/^(?:input|select|textarea|button)$/i,$e=/^(?:a|area)$/i;mt.fn.extend({prop:function(t,e){return Lt(this,mt.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[mt.propFix[t]||t]})}}),mt.extend({prop:function(t,e,i){var n,o,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return 1===a&&mt.isXMLDoc(t)||(e=mt.propFix[e]||e,o=mt.propHooks[e]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:t[e]=i:o&&"get"in o&&null!==(n=o.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=mt.find.attr(t,"tabindex");return e?parseInt(e,10):De.test(t.nodeName)||$e.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),lt.optSelected||(mt.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),mt.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){mt.propFix[this.toLowerCase()]=this}),mt.fn.extend({addClass:function(t){var e,i,n,o,a,s;return ct(t)?this.each(function(e){mt(this).addClass(t.call(this,e,W(this)))}):(e=q(t)).length?this.each(function(){if(n=W(this),i=1===this.nodeType&&" "+U(n)+" "){for(a=0;a<e.length;a++)o=e[a],i.indexOf(" "+o+" ")<0&&(i+=o+" ");s=U(i),n!==s&&this.setAttribute("class",s)}}):this},removeClass:function(t){var e,i,n,o,a,s;return ct(t)?this.each(function(e){mt(this).removeClass(t.call(this,e,W(this)))}):arguments.length?(e=q(t)).length?this.each(function(){if(n=W(this),i=1===this.nodeType&&" "+U(n)+" "){for(a=0;a<e.length;a++)for(o=e[a];-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");s=U(i),n!==s&&this.setAttribute("class",s)}}):this:this.attr("class","")},toggleClass:function(t,e){var i,n,o,a,s=typeof t,r="string"===s||Array.isArray(t);return ct(t)?this.each(function(i){mt(this).toggleClass(t.call(this,i,W(this),e),e)}):"boolean"==typeof e&&r?e?this.addClass(t):this.removeClass(t):(i=q(t),this.each(function(){if(r)for(a=mt(this),o=0;o<i.length;o++)n=i[o],a.hasClass(n)?a.removeClass(n):a.addClass(n);else void 0!==t&&"boolean"!==s||((n=W(this))&&jt.set(this,"__className__",n),this.setAttribute&&this.setAttribute("class",n||!1===t?"":jt.get(this,"__className__")||""))}))},hasClass:function(t){var e,i,n=0;for(e=" "+t+" ";i=this[n++];)if(1===i.nodeType&&-1<(" "+U(W(i))+" ").indexOf(e))return!0;return!1}});var Ee=/\r/g;mt.fn.extend({val:function(t){var e,i,n,o=this[0];return arguments.length?(n=ct(t),this.each(function(i){var o;1===this.nodeType&&(null==(o=n?t.call(this,i,mt(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=mt.map(o,function(t){return null==t?"":t+""})),(e=mt.valHooks[this.type]||mt.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))})):o?(e=mt.valHooks[o.type]||mt.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(i=e.get(o,"value"))?i:"string"==typeof(i=o.value)?i.replace(Ee,""):null==i?"":i:void 0}}),mt.extend({valHooks:{option:{get:function(t){var e=mt.find.attr(t,"value");return null!=e?e:U(mt.text(t))}},select:{get:function(t){var e,i,n,o=t.options,s=t.selectedIndex,r="select-one"===t.type,l=r?null:[],c=r?s+1:o.length;for(n=s<0?c:r?s:0;n<c;n++)if(((i=o[n]).selected||n===s)&&!i.disabled&&(!i.parentNode.disabled||!a(i.parentNode,"optgroup"))){if(e=mt(i).val(),r)return e;l.push(e)}return l},set:function(t,e){for(var i,n,o=t.options,a=mt.makeArray(e),s=o.length;s--;)((n=o[s]).selected=-1<mt.inArray(mt.valHooks.option.get(n),a))&&(i=!0);return i||(t.selectedIndex=-1),a}}}}),mt.each(["radio","checkbox"],function(){mt.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<mt.inArray(mt(t).val(),e)}},lt.checkOn||(mt.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var Ae=t.location,Oe={guid:Date.now()},Fe=/\?/;mt.parseXML=function(e){var i,n;if(!e||"string"!=typeof e)return null;try{i=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=i&&i.getElementsByTagName("parsererror")[0],i&&!n||mt.error("Invalid XML: "+(n?mt.map(n.childNodes,function(t){return t.textContent}).join("\n"):e)),i};var Pe=/^(?:focusinfocus|focusoutblur)$/,Me=function(t){t.stopPropagation()};mt.extend(mt.event,{trigger:function(e,i,n,o){var a,s,r,l,c,d,u,h,p=[n||ut],f=at.call(e,"type")?e.type:e,m=at.call(e,"namespace")?e.namespace.split("."):[];if(s=h=r=n=n||ut,3!==n.nodeType&&8!==n.nodeType&&!Pe.test(f+mt.event.triggered)&&(-1<f.indexOf(".")&&(f=(m=f.split(".")).shift(),m.sort()),c=f.indexOf(":")<0&&"on"+f,(e=e[mt.expando]?e:new mt.Event(f,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),i=null==i?[e]:mt.makeArray(i,[e]),u=mt.event.special[f]||{},o||!u.trigger||!1!==u.trigger.apply(n,i))){if(!o&&!u.noBubble&&!dt(n)){for(l=u.delegateType||f,Pe.test(l+f)||(s=s.parentNode);s;s=s.parentNode)p.push(s),r=s;r===(n.ownerDocument||ut)&&p.push(r.defaultView||r.parentWindow||t)}for(a=0;(s=p[a++])&&!e.isPropagationStopped();)h=s,e.type=1<a?l:u.bindType||f,(d=(jt.get(s,"events")||Object.create(null))[e.type]&&jt.get(s,"handle"))&&d.apply(s,i),(d=c&&s[c])&&d.apply&&Rt(s)&&(e.result=d.apply(s,i),!1===e.result&&e.preventDefault());return e.type=f,o||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(p.pop(),i)||!Rt(n)||c&&ct(n[f])&&!dt(n)&&((r=n[c])&&(n[c]=null),mt.event.triggered=f,e.isPropagationStopped()&&h.addEventListener(f,Me),n[f](),e.isPropagationStopped()&&h.removeEventListener(f,Me),mt.event.triggered=void 0,r&&(n[c]=r)),e.result}},simulate:function(t,e,i){var n=mt.extend(new mt.Event,i,{type:t,isSimulated:!0});mt.event.trigger(n,null,e)}}),mt.fn.extend({trigger:function(t,e){return this.each(function(){mt.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return mt.event.trigger(t,e,i,!0)}});var Le=/\[\]$/,Ie=/\r?\n/g,Ne=/^(?:submit|button|image|reset|file)$/i,Re=/^(?:input|select|textarea|keygen)/i;mt.param=function(t,e){var i,n=[],o=function(t,e){var i=ct(e)?e():e;n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==i?"":i)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!mt.isPlainObject(t))mt.each(t,function(){o(this.name,this.value)});else for(i in t)V(i,t[i],e,o);return n.join("&")},mt.fn.extend({serialize:function(){return mt.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=mt.prop(this,"elements");return t?mt.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!mt(this).is(":disabled")&&Re.test(this.nodeName)&&!Ne.test(t)&&(this.checked||!Jt.test(t))}).map(function(t,e){var i=mt(this).val();return null==i?null:Array.isArray(i)?mt.map(i,function(t){return{name:e.name,value:t.replace(Ie,"\r\n")}}):{name:e.name,value:i.replace(Ie,"\r\n")}}).get()}});var je=/%20/g,Ye=/#.*$/,He=/([?&])_=[^&]*/,ze=/^(.*?):[ \t]*([^\r\n]*)$/gm,Be=/^(?:GET|HEAD)$/,Ue=/^\/\//,We={},qe={},Ve="*/".concat("*"),Ge=ut.createElement("a");Ge.href=Ae.href,mt.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ae.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ae.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ve,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":mt.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Q(Q(t,mt.ajaxSettings),e):Q(mt.ajaxSettings,t)},ajaxPrefilter:G(We),ajaxTransport:G(qe),ajax:function(e,i){function n(e,i,n,r){var c,h,p,x,w,k=i;d||(d=!0,l&&t.clearTimeout(l),o=void 0,s=r||"",_.readyState=0<e?4:0,c=200<=e&&e<300||304===e,n&&(x=function(t,e,i){for(var n,o,a,s,r=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(o in r)if(r[o]&&r[o].test(n)){l.unshift(o);break}if(l[0]in i)a=l[0];else{for(o in i){if(!l[0]||t.converters[o+" "+l[0]]){a=o;break}s||(s=o)}a=a||s}if(a)return a!==l[0]&&l.unshift(a),i[a]}(f,_,n)),!c&&-1<mt.inArray("script",f.dataTypes)&&mt.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),x=function(t,e,i,n){var o,a,s,r,l,c={},d=t.dataTypes.slice();if(d[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(a=d.shift();a;)if(t.responseFields[a]&&(i[t.responseFields[a]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=a,a=d.shift())if("*"===a)a=l;else if("*"!==l&&l!==a){if(!(s=c[l+" "+a]||c["* "+a]))for(o in c)if((r=o.split(" "))[1]===a&&(s=c[l+" "+r[0]]||c["* "+r[0]])){!0===s?s=c[o]:!0!==c[o]&&(a=r[0],d.unshift(r[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+l+" to "+a}}}return{state:"success",data:e}}(f,x,_,c),c?(f.ifModified&&((w=_.getResponseHeader("Last-Modified"))&&(mt.lastModified[a]=w),(w=_.getResponseHeader("etag"))&&(mt.etag[a]=w)),204===e||"HEAD"===f.type?k="nocontent":304===e?k="notmodified":(k=x.state,h=x.data,c=!(p=x.error))):(p=k,!e&&k||(k="error",e<0&&(e=0))),_.status=e,_.statusText=(i||k)+"",c?v.resolveWith(m,[h,k,_]):v.rejectWith(m,[_,k,p]),_.statusCode(b),b=void 0,u&&g.trigger(c?"ajaxSuccess":"ajaxError",[_,f,c?h:p]),y.fireWith(m,[_,k]),u&&(g.trigger("ajaxComplete",[_,f]),--mt.active||mt.event.trigger("ajaxStop")))}"object"==typeof e&&(i=e,e=void 0),i=i||{};var o,a,s,r,l,c,d,u,h,p,f=mt.ajaxSetup({},i),m=f.context||f,g=f.context&&(m.nodeType||m.jquery)?mt(m):mt.event,v=mt.Deferred(),y=mt.Callbacks("once memory"),b=f.statusCode||{},x={},w={},k="canceled",_={readyState:0,getResponseHeader:function(t){var e;if(d){if(!r)for(r={};e=ze.exec(s);)r[e[1].toLowerCase()+" "]=(r[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=r[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return d?s:null},setRequestHeader:function(t,e){return null==d&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==d&&(f.mimeType=t),this},statusCode:function(t){var e;if(t)if(d)_.always(t[_.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||k;return o&&o.abort(e),n(0,e),this}};if(v.promise(_),f.url=((e||f.url||Ae.href)+"").replace(Ue,Ae.protocol+"//"),f.type=i.method||i.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(Ft)||[""],null==f.crossDomain){c=ut.createElement("a");try{c.href=f.url,c.href=c.href,f.crossDomain=Ge.protocol+"//"+Ge.host!=c.protocol+"//"+c.host}catch(e){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=mt.param(f.data,f.traditional)),X(We,f,i,_),d)return _;for(h in(u=mt.event&&f.global)&&0==mt.active++&&mt.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Be.test(f.type),a=f.url.replace(Ye,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(je,"+")):(p=f.url.slice(a.length),f.data&&(f.processData||"string"==typeof f.data)&&(a+=(Fe.test(a)?"&":"?")+f.data,delete f.data),!1===f.cache&&(a=a.replace(He,"$1"),p=(Fe.test(a)?"&":"?")+"_="+Oe.guid+++p),f.url=a+p),f.ifModified&&(mt.lastModified[a]&&_.setRequestHeader("If-Modified-Since",mt.lastModified[a]),mt.etag[a]&&_.setRequestHeader("If-None-Match",mt.etag[a])),(f.data&&f.hasContent&&!1!==f.contentType||i.contentType)&&_.setRequestHeader("Content-Type",f.contentType),_.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Ve+"; q=0.01":""):f.accepts["*"]),f.headers)_.setRequestHeader(h,f.headers[h]);if(f.beforeSend&&(!1===f.beforeSend.call(m,_,f)||d))return _.abort();if(k="abort",y.add(f.complete),_.done(f.success),_.fail(f.error),o=X(qe,f,i,_)){if(_.readyState=1,u&&g.trigger("ajaxSend",[_,f]),d)return _;f.async&&0<f.timeout&&(l=t.setTimeout(function(){_.abort("timeout")},f.timeout));try{d=!1,o.send(x,n)}catch(e){if(d)throw e;n(-1,e)}}else n(-1,"No Transport");return _},getJSON:function(t,e,i){return mt.get(t,e,i,"json")},getScript:function(t,e){return mt.get(t,void 0,e,"script")}}),mt.each(["get","post"],function(t,e){mt[e]=function(t,i,n,o){return ct(i)&&(o=o||n,n=i,i=void 0),mt.ajax(mt.extend({url:t,type:e,dataType:o,data:i,success:n},mt.isPlainObject(t)&&t))}}),mt.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),mt._evalUrl=function(t,e,i){return mt.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){mt.globalEval(t,e,i)}})},mt.fn.extend({wrapAll:function(t){var e;return this[0]&&(ct(t)&&(t=t.call(this[0])),e=mt(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return ct(t)?this.each(function(e){mt(this).wrapInner(t.call(this,e))}):this.each(function(){var e=mt(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)})},wrap:function(t){var e=ct(t);return this.each(function(i){mt(this).wrapAll(e?t.call(this,i):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){mt(this).replaceWith(this.childNodes)}),this}}),mt.expr.pseudos.hidden=function(t){return!mt.expr.pseudos.visible(t)},mt.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},mt.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Xe={0:200,1223:204},Qe=mt.ajaxSettings.xhr();lt.cors=!!Qe&&"withCredentials"in Qe,lt.ajax=Qe=!!Qe,mt.ajaxTransport(function(e){var i,n;if(lt.cors||Qe&&!e.crossDomain)return{send:function(o,a){var s,r=e.xhr();if(r.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)r[s]=e.xhrFields[s];for(s in e.mimeType&&r.overrideMimeType&&r.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)r.setRequestHeader(s,o[s]);i=function(t){return function(){i&&(i=n=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===t?r.abort():"error"===t?"number"!=typeof r.status?a(0,"error"):a(r.status,r.statusText):a(Xe[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=i(),n=r.onerror=r.ontimeout=i("error"),void 0!==r.onabort?r.onabort=n:r.onreadystatechange=function(){4===r.readyState&&t.setTimeout(function(){i&&n()})},i=i("abort");try{r.send(e.hasContent&&e.data||null)}catch(o){if(i)throw o}},abort:function(){i&&i()}}}),mt.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),mt.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return mt.globalEval(t),t}}}),mt.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),mt.ajaxTransport("script",function(t){var e,i;if(t.crossDomain||t.scriptAttrs)return{send:function(n,o){e=mt("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",i=function(t){e.remove(),i=null,t&&o("error"===t.type?404:200,t.type)}),ut.head.appendChild(e[0])},abort:function(){i&&i()}}});var Ke,Ze=[],Je=/(=)\?(?=&|$)|\?\?/;mt.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ze.pop()||mt.expando+"_"+Oe.guid++;return this[t]=!0,t}}),mt.ajaxPrefilter("json jsonp",function(e,i,n){var o,a,s,r=!1!==e.jsonp&&(Je.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Je.test(e.data)&&"data");if(r||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=ct(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,r?e[r]=e[r].replace(Je,"$1"+o):!1!==e.jsonp&&(e.url+=(Fe.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return s||mt.error(o+" was not called"),s[0]},e.dataTypes[0]="json",a=t[o],t[o]=function(){s=arguments},n.always(function(){void 0===a?mt(t).removeProp(o):t[o]=a,e[o]&&(e.jsonpCallback=i.jsonpCallback,Ze.push(o)),s&&ct(a)&&a(s[0]),s=a=void 0}),"script"}),lt.createHTMLDocument=((Ke=ut.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ke.childNodes.length),mt.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(lt.createHTMLDocument?((n=(e=ut.implementation.createHTMLDocument("")).createElement("base")).href=ut.location.href,e.head.appendChild(n)):e=ut),a=!i&&[],(o=Dt.exec(t))?[e.createElement(o[1])]:(o=w([t],e,a),a&&a.length&&mt(a).remove(),mt.merge([],o.childNodes)));var n,o,a},mt.fn.load=function(t,e,i){var n,o,a,s=this,r=t.indexOf(" ");return-1<r&&(n=U(t.slice(r)),t=t.slice(0,r)),ct(e)?(i=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<s.length&&mt.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){a=arguments,s.html(n?mt("<div>").append(mt.parseHTML(t)).find(n):t)}).always(i&&function(t,e){s.each(function(){i.apply(this,a||[t.responseText,e,t])})}),this},mt.expr.pseudos.animated=function(t){return mt.grep(mt.timers,function(e){return t===e.elem}).length},mt.offset={setOffset:function(t,e,i){var n,o,a,s,r,l,c=mt.css(t,"position"),d=mt(t),u={};"static"===c&&(t.style.position="relative"),r=d.offset(),a=mt.css(t,"top"),l=mt.css(t,"left"),("absolute"===c||"fixed"===c)&&-1<(a+l).indexOf("auto")?(s=(n=d.position()).top,o=n.left):(s=parseFloat(a)||0,o=parseFloat(l)||0),ct(e)&&(e=e.call(t,i,mt.extend({},r))),null!=e.top&&(u.top=e.top-r.top+s),null!=e.left&&(u.left=e.left-r.left+o),"using"in e?e.using.call(t,u):d.css(u)}},mt.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){mt.offset.setOffset(this,t,e)});var e,i,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],o={top:0,left:0};if("fixed"===mt.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===mt.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((o=mt(t).offset()).top+=mt.css(t,"borderTopWidth",!0),o.left+=mt.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-mt.css(n,"marginTop",!0),left:e.left-o.left-mt.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===mt.css(t,"position");)t=t.offsetParent;return t||qt})}}),mt.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var i="pageYOffset"===e;mt.fn[t]=function(n){return Lt(this,function(t,n,o){var a;if(dt(t)?a=t:9===t.nodeType&&(a=t.defaultView),void 0===o)return a?a[e]:t[n];a?a.scrollTo(i?a.pageXOffset:o,i?o:a.pageYOffset):t[n]=o},t,n,arguments.length)}}),mt.each(["top","left"],function(t,e){mt.cssHooks[e]=P(lt.pixelPosition,function(t,i){if(i)return i=F(t,e),le.test(i)?mt(t).position()[e]+"px":i})}),mt.each({Height:"height",Width:"width"},function(t,e){mt.each({padding:"inner"+t,content:e,"":"outer"+t},function(i,n){mt.fn[n]=function(o,a){var s=arguments.length&&(i||"boolean"!=typeof o),r=i||(!0===o||!0===a?"margin":"border");return Lt(this,function(e,i,o){var a;return dt(e)?0===n.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(a=e.documentElement,Math.max(e.body["scroll"+t],a["scroll"+t],e.body["offset"+t],a["offset"+t],a["client"+t])):void 0===o?mt.css(e,i,r):mt.style(e,i,o,r)},e,s?o:void 0,s)}})}),mt.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){mt.fn[e]=function(t){return this.on(e,t)}}),mt.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),mt.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){mt.fn[e]=function(t,i){return 0<arguments.length?this.on(e,null,t,i):this.trigger(e)}});var ti=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;mt.proxy=function(t,e){var i,n,o;if("string"==typeof e&&(i=t[e],e=t,t=i),ct(t))return n=J.call(arguments,2),(o=function(){return t.apply(e||this,n.concat(J.call(arguments)))}).guid=t.guid=t.guid||mt.guid++,o},mt.holdReady=function(t){t?mt.readyWait++:mt.ready(!0)},mt.isArray=Array.isArray,mt.parseJSON=JSON.parse,mt.nodeName=a,mt.isFunction=ct,mt.isWindow=dt,mt.camelCase=f,mt.type=n,mt.now=Date.now,mt.isNumeric=function(t){var e=mt.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},mt.trim=function(t){return null==t?"":(t+"").replace(ti,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return mt});var ei=t.jQuery,ii=t.$;return mt.noConflict=function(e){return t.$===mt&&(t.$=ii),e&&t.jQuery===mt&&(t.jQuery=ei),mt},void 0===e&&(t.jQuery=t.$=mt),mt}),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(t){"use strict";var e=jQuery.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1==e[0]&&9==e[1]&&e[2]<1||3<e[0])throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4")}(),function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var i=!1,n=this;return t(this).one("bsTransitionEnd",function(){i=!0}),setTimeout(function(){i||t(n).trigger(t.support.transition.end)},e),this},t(function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),function(t){"use strict";var e='[data-dismiss="alert"]',i=function(i){t(i).on("click",e,this.close)};i.VERSION="3.4.1",i.TRANSITION_DURATION=150,i.prototype.close=function(e){function n(){s.detach().trigger("closed.bs.alert").remove()}var o=t(this),a=o.attr("data-target");a||(a=(a=o.attr("href"))&&a.replace(/.*(?=#[^\s]*$)/,"")),a="#"===a?[]:a;var s=t(document).find(a);e&&e.preventDefault(),s.length||(s=o.closest(".alert")),s.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(s.removeClass("in"),t.support.transition&&s.hasClass("fade")?s.one("bsTransitionEnd",n).emulateTransitionEnd(i.TRANSITION_DURATION):n())};var n=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var n=t(this),o=n.data("bs.alert");o||n.data("bs.alert",o=new i(this)),"string"==typeof e&&o[e].call(n)})},t.fn.alert.Constructor=i,t.fn.alert.noConflict=function(){return t.fn.alert=n,this},t(document).on("click.bs.alert.data-api",e,i.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.button"),a="object"==typeof e&&e;o||n.data("bs.button",o=new i(this,a)),"toggle"==e?o.toggle():e&&o.setState(e)})}var i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.isLoading=!1};i.VERSION="3.4.1",i.DEFAULTS={loadingText:"loading..."},i.prototype.setState=function(e){var i="disabled",n=this.$element,o=n.is("input")?"val":"html",a=n.data();e+="Text",null==a.resetText&&n.data("resetText",n[o]()),setTimeout(t.proxy(function(){n[o](null==a[e]?this.options[e]:a[e]),"loadingText"==e?(this.isLoading=!0,n.addClass(i).attr(i,i).prop(i,!0)):this.isLoading&&(this.isLoading=!1,n.removeClass(i).removeAttr(i).prop(i,!1))},this),0)},i.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var i=this.$element.find("input");"radio"==i.prop("type")?(i.prop("checked")&&(t=!1),e.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==i.prop("type")&&(i.prop("checked")!==this.$element.hasClass("active")&&(t=!1),this.$element.toggleClass("active")),i.prop("checked",this.$element.hasClass("active")),t&&i.trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")};var n=t.fn.button;t.fn.button=e,t.fn.button.Constructor=i,t.fn.button.noConflict=function(){return t.fn.button=n,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(i){var n=t(i.target).closest(".btn");e.call(n,"toggle"),t(i.target).is('input[type="radio"], input[type="checkbox"]')||(i.preventDefault(),n.is("input,button")?n.trigger("focus"):n.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){t(e.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(e.type))})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.carousel"),a=t.extend({},i.DEFAULTS,n.data(),"object"==typeof e&&e),s="string"==typeof e?e:a.slide;o||n.data("bs.carousel",o=new i(this,a)),"number"==typeof e?o.to(e):s?o[s]():a.interval&&o.pause().cycle()})}var i=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",t.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};i.VERSION="3.4.1",i.TRANSITION_DURATION=600,i.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},i.prototype.keydown=function(t){if(!/input|textarea/i.test(t.target.tagName)){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()}},i.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},i.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},i.prototype.getItemForDirection=function(t,e){var i=this.getItemIndex(e);if(("prev"==t&&0===i||"next"==t&&i==this.$items.length-1)&&!this.options.wrap)return e;var n=(i+("prev"==t?-1:1))%this.$items.length;return this.$items.eq(n)},i.prototype.to=function(t){var e=this,i=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(t>this.$items.length-1||t<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){e.to(t)}):i==t?this.pause().cycle():this.slide(i<t?"next":"prev",this.$items.eq(t))},i.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},i.prototype.next=function(){if(!this.sliding)return this.slide("next")},i.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},i.prototype.slide=function(e,n){var o=this.$element.find(".item.active"),a=n||this.getItemForDirection(e,o),s=this.interval,r="next"==e?"left":"right",l=this;if(a.hasClass("active"))return this.sliding=!1;var c=a[0],d=t.Event("slide.bs.carousel",{relatedTarget:c,direction:r});if(this.$element.trigger(d),!d.isDefaultPrevented()){if(this.sliding=!0,s&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var u=t(this.$indicators.children()[this.getItemIndex(a)]);u&&u.addClass("active")}var h=t.Event("slid.bs.carousel",{relatedTarget:c,direction:r});return t.support.transition&&this.$element.hasClass("slide")?(a.addClass(e),"object"==typeof a&&a.length&&a[0].offsetWidth,o.addClass(r),a.addClass(r),o.one("bsTransitionEnd",function(){a.removeClass([e,r].join(" ")).addClass("active"),o.removeClass(["active",r].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(h)},0)}).emulateTransitionEnd(i.TRANSITION_DURATION)):(o.removeClass("active"),a.addClass("active"),this.sliding=!1,this.$element.trigger(h)),s&&this.cycle(),this}};var n=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=i,t.fn.carousel.noConflict=function(){return t.fn.carousel=n,this};var o=function(i){var n=t(this),o=n.attr("href");o&&(o=o.replace(/.*(?=#[^\s]+$)/,""));var a=n.attr("data-target")||o,s=t(document).find(a);if(s.hasClass("carousel")){var r=t.extend({},s.data(),n.data()),l=n.attr("data-slide-to");l&&(r.interval=!1),e.call(s,r),l&&s.data("bs.carousel").to(l),i.preventDefault()}};t(document).on("click.bs.carousel.data-api","[data-slide]",o).on("click.bs.carousel.data-api","[data-slide-to]",o),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var i=t(this);e.call(i,i.data())})})}(jQuery),function(t){"use strict";function e(e){var i,n=e.attr("data-target")||(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"");return t(document).find(n)}function i(e){return this.each(function(){var i=t(this),o=i.data("bs.collapse"),a=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e);!o&&a.toggle&&/show|hide/.test(e)&&(a.toggle=!1),o||i.data("bs.collapse",o=new n(this,a)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.$trigger=t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'),this.transitioning=null,
this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};n.VERSION="3.4.1",n.TRANSITION_DURATION=350,n.DEFAULTS={toggle:!0},n.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},n.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var e,o=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(o&&o.length&&(e=o.data("bs.collapse"))&&e.transitioning)){var a=t.Event("show.bs.collapse");if(this.$element.trigger(a),!a.isDefaultPrevented()){o&&o.length&&(i.call(o,"hide"),e||o.data("bs.collapse",null));var s=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[s](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var r=function(){this.$element.removeClass("collapsing").addClass("collapse in")[s](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return r.call(this);var l=t.camelCase(["scroll",s].join("-"));this.$element.one("bsTransitionEnd",t.proxy(r,this)).emulateTransitionEnd(n.TRANSITION_DURATION)[s](this.$element[0][l])}}}},n.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var i=this.dimension();this.$element[i](this.$element[i]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var o=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};if(!t.support.transition)return o.call(this);this.$element[i](0).one("bsTransitionEnd",t.proxy(o,this)).emulateTransitionEnd(n.TRANSITION_DURATION)}}},n.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},n.prototype.getParent=function(){return t(document).find(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(t.proxy(function(i,n){var o=t(n);this.addAriaAndCollapsedClass(e(o),o)},this)).end()},n.prototype.addAriaAndCollapsedClass=function(t,e){var i=t.hasClass("in");t.attr("aria-expanded",i),e.toggleClass("collapsed",!i).attr("aria-expanded",i)};var o=t.fn.collapse;t.fn.collapse=i,t.fn.collapse.Constructor=n,t.fn.collapse.noConflict=function(){return t.fn.collapse=o,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(n){var o=t(this);o.attr("data-target")||n.preventDefault();var a=e(o),s=a.data("bs.collapse")?"toggle":o.data();i.call(a,s)})}(jQuery),function(t){"use strict";function e(e){var i=e.attr("data-target");i||(i=(i=e.attr("href"))&&/#[A-Za-z]/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var n="#"!==i?t(document).find(i):null;return n&&n.length?n:e.parent()}function i(i){i&&3===i.which||(t(".dropdown-backdrop").remove(),t(n).each(function(){var n=t(this),o=e(n),a={relatedTarget:this};o.hasClass("open")&&(i&&"click"==i.type&&/input|textarea/i.test(i.target.tagName)&&t.contains(o[0],i.target)||(o.trigger(i=t.Event("hide.bs.dropdown",a)),i.isDefaultPrevented()||(n.attr("aria-expanded","false"),o.removeClass("open").trigger(t.Event("hidden.bs.dropdown",a)))))}))}var n='[data-toggle="dropdown"]',o=function(e){t(e).on("click.bs.dropdown",this.toggle)};o.VERSION="3.4.1",o.prototype.toggle=function(n){var o=t(this);if(!o.is(".disabled, :disabled")){var a=e(o),s=a.hasClass("open");if(i(),!s){"ontouchstart"in document.documentElement&&!a.closest(".navbar-nav").length&&t(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(t(this)).on("click",i);var r={relatedTarget:this};if(a.trigger(n=t.Event("show.bs.dropdown",r)),n.isDefaultPrevented())return;o.trigger("focus").attr("aria-expanded","true"),a.toggleClass("open").trigger(t.Event("shown.bs.dropdown",r))}return!1}},o.prototype.keydown=function(i){if(/(38|40|27|32)/.test(i.which)&&!/input|textarea/i.test(i.target.tagName)){var o=t(this);if(i.preventDefault(),i.stopPropagation(),!o.is(".disabled, :disabled")){var a=e(o),s=a.hasClass("open");if(!s&&27!=i.which||s&&27==i.which)return 27==i.which&&a.find(n).trigger("focus"),o.trigger("click");var r=a.find(".dropdown-menu li:not(.disabled):visible a");if(r.length){var l=r.index(i.target);38==i.which&&0<l&&l--,40==i.which&&l<r.length-1&&l++,~l||(l=0),r.eq(l).trigger("focus")}}}};var a=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var i=t(this),n=i.data("bs.dropdown");n||i.data("bs.dropdown",n=new o(this)),"string"==typeof e&&n[e].call(i)})},t.fn.dropdown.Constructor=o,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=a,this},t(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",n,o.prototype.toggle).on("keydown.bs.dropdown.data-api",n,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,n){return this.each(function(){var o=t(this),a=o.data("bs.modal"),s=t.extend({},i.DEFAULTS,o.data(),"object"==typeof e&&e);a||o.data("bs.modal",a=new i(this,s)),"string"==typeof e?a[e](n):s.show&&a.show(n)})}var i=function(e,i){this.options=i,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};i.VERSION="3.4.1",i.TRANSITION_DURATION=300,i.BACKDROP_TRANSITION_DURATION=150,i.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},i.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},i.prototype.show=function(e){var n=this,o=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(o),this.isShown||o.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){n.$element.one("mouseup.dismiss.bs.modal",function(e){t(e.target).is(n.$element)&&(n.ignoreBackdropClick=!0)})}),this.backdrop(function(){var o=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),n.adjustDialog(),o&&n.$element[0].offsetWidth,n.$element.addClass("in"),n.enforceFocus();var a=t.Event("shown.bs.modal",{relatedTarget:e});o?n.$dialog.one("bsTransitionEnd",function(){n.$element.trigger("focus").trigger(a)}).emulateTransitionEnd(i.TRANSITION_DURATION):n.$element.trigger("focus").trigger(a)}))},i.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(i.TRANSITION_DURATION):this.hideModal())},i.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){document===t.target||this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},i.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},i.prototype.resize=function(){this.isShown?t(window).on("resize.bs.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.bs.modal")},i.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$body.removeClass("modal-open"),t.resetAdjustments(),t.resetScrollbar(),t.$element.trigger("hidden.bs.modal")})},i.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},i.prototype.backdrop=function(e){var n=this,o=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var a=t.support.transition&&o;if(this.$backdrop=t(document.createElement("div")).addClass("modal-backdrop "+o).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())},this)),a&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;a?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var s=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",s).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):s()}else e&&e()},i.prototype.handleUpdate=function(){this.adjustDialog()},i.prototype.adjustDialog=function(){var t=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&t?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!t?this.scrollbarWidth:""})},i.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},i.prototype.checkScrollbar=function(){var t=window.innerWidth;if(!t){var e=document.documentElement.getBoundingClientRect();t=e.right-Math.abs(e.left)}this.bodyIsOverflowing=document.body.clientWidth<t,this.scrollbarWidth=this.measureScrollbar()},i.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";var i=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("padding-right",e+i),t(this.fixedContent).each(function(e,n){var o=n.style.paddingRight,a=t(n).css("padding-right");t(n).data("padding-right",o).css("padding-right",parseFloat(a)+i+"px")}))},i.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad),t(this.fixedContent).each(function(e,i){var n=t(i).data("padding-right");t(i).removeData("padding-right"),i.style.paddingRight=n||""})},i.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var n=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=i,t.fn.modal.noConflict=function(){return t.fn.modal=n,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(i){var n=t(this),o=n.attr("href"),a=n.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,""),s=t(document).find(a),r=s.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(o)&&o},s.data(),n.data());n.is("a")&&i.preventDefault(),s.one("show.bs.modal",function(t){t.isDefaultPrevented()||s.one("hidden.bs.modal",function(){n.is(":visible")&&n.trigger("focus")})}),e.call(s,r,this)})}(jQuery),function(t){"use strict";function e(e,i){var n=e.nodeName.toLowerCase();if(-1!==t.inArray(n,i))return-1===t.inArray(n,o)||Boolean(e.nodeValue.match(s)||e.nodeValue.match(r));for(var a=t(i).filter(function(t,e){return e instanceof RegExp}),l=0,c=a.length;l<c;l++)if(n.match(a[l]))return!0;return!1}function i(i,n,o){if(0===i.length)return i;if(o&&"function"==typeof o)return o(i);if(!document.implementation||!document.implementation.createHTMLDocument)return i;var a=document.implementation.createHTMLDocument("sanitization");a.body.innerHTML=i;for(var s=t.map(n,function(t,e){return e}),r=t(a.body).find("*"),l=0,c=r.length;l<c;l++){var d=r[l],u=d.nodeName.toLowerCase();if(-1!==t.inArray(u,s))for(var h=t.map(d.attributes,function(t){return t}),p=[].concat(n["*"]||[],n[u]||[]),f=0,m=h.length;f<m;f++)e(h[f],p)||d.removeAttribute(h[f].nodeName);else d.parentNode.removeChild(d)}return a.body.innerHTML}var n=["sanitize","whiteList","sanitizeFn"],o=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],a={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},s=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,r=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i,l=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};l.VERSION="3.4.1",l.TRANSITION_DURATION=150,l.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:a},l.prototype.init=function(e,i,n){if(this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(n),this.$viewport=this.options.viewport&&t(document).find(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var o=this.options.trigger.split(" "),a=o.length;a--;){var s=o[a];if("click"==s)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=s){var r="hover"==s?"mouseenter":"focusin",l="hover"==s?"mouseleave":"focusout";this.$element.on(r+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},l.prototype.getDefaults=function(){return l.DEFAULTS},l.prototype.getOptions=function(e){var o=this.$element.data();for(var a in o)o.hasOwnProperty(a)&&-1!==t.inArray(a,n)&&delete o[a];return(e=t.extend({},this.getDefaults(),o,e)).delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e.sanitize&&(e.template=i(e.template,e.whiteList,e.sanitizeFn)),e},l.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,n){i[t]!=n&&(e[t]=n)}),e},l.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusin"==e.type?"focus":"hover"]=!0),i.tip().hasClass("in")||"in"==i.hoverState)i.hoverState="in";else{if(clearTimeout(i.timeout),i.hoverState="in",!i.options.delay||!i.options.delay.show)return i.show();i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)}},l.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},l.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusout"==e.type?"focus":"hover"]=!1),!i.isInStateTrue()){if(clearTimeout(i.timeout),i.hoverState="out",!i.options.delay||!i.options.delay.hide)return i.hide();i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)}},l.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var i=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!i)return;var n=this,o=this.tip(),a=this.getUID(this.type);this.setContent(),o.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&o.addClass("fade");var s="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,r=/\s?auto?\s?/i,c=r.test(s);c&&(s=s.replace(r,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(s).data("bs."+this.type,this),this.options.container?o.appendTo(t(document).find(this.options.container)):o.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var d=this.getPosition(),u=o[0].offsetWidth,h=o[0].offsetHeight;if(c){var p=s,f=this.getPosition(this.$viewport);s="bottom"==s&&d.bottom+h>f.bottom?"top":"top"==s&&d.top-h<f.top?"bottom":"right"==s&&d.right+u>f.width?"left":"left"==s&&d.left-u<f.left?"right":s,o.removeClass(p).addClass(s)}var m=this.getCalculatedOffset(s,d,u,h);this.applyPlacement(m,s);var g=function(){var t=n.hoverState;n.$element.trigger("shown.bs."+n.type),n.hoverState=null,"out"==t&&n.leave(n)};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",g).emulateTransitionEnd(l.TRANSITION_DURATION):g()}},l.prototype.applyPlacement=function(e,i){var n=this.tip(),o=n[0].offsetWidth,a=n[0].offsetHeight,s=parseInt(n.css("margin-top"),10),r=parseInt(n.css("margin-left"),10);isNaN(s)&&(s=0),isNaN(r)&&(r=0),e.top+=s,e.left+=r,t.offset.setOffset(n[0],t.extend({using:function(t){n.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),n.addClass("in");var l=n[0].offsetWidth,c=n[0].offsetHeight;"top"==i&&c!=a&&(e.top=e.top+a-c);var d=this.getViewportAdjustedDelta(i,e,l,c);d.left?e.left+=d.left:e.top+=d.top;var u=/top|bottom/.test(i),h=u?2*d.left-o+l:2*d.top-a+c,p=u?"offsetWidth":"offsetHeight";n.offset(e),this.replaceArrow(h,n[0][p],u)},l.prototype.replaceArrow=function(t,e,i){this.arrow().css(i?"left":"top",50*(1-t/e)+"%").css(i?"top":"left","")},l.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();this.options.html?(this.options.sanitize&&(e=i(e,this.options.whiteList,this.options.sanitizeFn)),t.find(".tooltip-inner").html(e)):t.find(".tooltip-inner").text(e),t.removeClass("fade in top bottom left right")},l.prototype.hide=function(e){function i(){"in"!=n.hoverState&&o.detach(),n.$element&&n.$element.removeAttr("aria-describedby").trigger("hidden.bs."+n.type),e&&e()}var n=this,o=t(this.$tip),a=t.Event("hide.bs."+this.type);if(this.$element.trigger(a),!a.isDefaultPrevented())return o.removeClass("in"),t.support.transition&&o.hasClass("fade")?o.one("bsTransitionEnd",i).emulateTransitionEnd(l.TRANSITION_DURATION):i(),this.hoverState=null,this},l.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},l.prototype.hasContent=function(){return this.getTitle()},l.prototype.getPosition=function(e){var i=(e=e||this.$element)[0],n="BODY"==i.tagName,o=i.getBoundingClientRect();null==o.width&&(o=t.extend({},o,{width:o.right-o.left,height:o.bottom-o.top}));var a=window.SVGElement&&i instanceof window.SVGElement,s=n?{top:0,left:0}:a?null:e.offset(),r={scroll:n?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},l=n?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},o,r,l,s)},l.prototype.getCalculatedOffset=function(t,e,i,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-i}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},l.prototype.getViewportAdjustedDelta=function(t,e,i,n){var o={top:0,left:0};if(!this.$viewport)return o;var a=this.options.viewport&&this.options.viewport.padding||0,s=this.getPosition(this.$viewport);if(/right|left/.test(t)){var r=e.top-a-s.scroll,l=e.top+a-s.scroll+n;r<s.top?o.top=s.top-r:l>s.top+s.height&&(o.top=s.top+s.height-l)}else{var c=e.left-a,d=e.left+a+i;c<s.left?o.left=s.left-c:d>s.right&&(o.left=s.left+s.width-d)}return o},l.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},l.prototype.getUID=function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},l.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},l.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},l.prototype.enable=function(){this.enabled=!0},l.prototype.disable=function(){this.enabled=!1},l.prototype.toggleEnabled=function(){this.enabled=!this.enabled},l.prototype.toggle=function(e){var i=this;e&&((i=t(e.currentTarget).data("bs."+this.type))||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i))),e?(i.inState.click=!i.inState.click,i.isInStateTrue()?i.enter(i):i.leave(i)):i.tip().hasClass("in")?i.leave(i):i.enter(i)},l.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide(function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null,t.$element=null})},l.prototype.sanitizeHtml=function(t){return i(t,this.options.whiteList,this.options.sanitizeFn)};var c=t.fn.tooltip;t.fn.tooltip=function(e){return this.each(function(){var i=t(this),n=i.data("bs.tooltip"),o="object"==typeof e&&e;!n&&/destroy|hide/.test(e)||(n||i.data("bs.tooltip",n=new l(this,o)),"string"==typeof e&&n[e]())})},t.fn.tooltip.Constructor=l,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=c,this}}(jQuery),function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.VERSION="3.4.1",e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),((e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype)).constructor=e).prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),i=this.getContent();if(this.options.html){var n=typeof i;this.options.sanitize&&(e=this.sanitizeHtml(e),"string"===n&&(i=this.sanitizeHtml(i))),t.find(".popover-title").html(e),t.find(".popover-content").children().detach().end()["string"===n?"html":"append"](i)}else t.find(".popover-title").text(e),t.find(".popover-content").children().detach().end().text(i);t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var i=t.fn.popover;t.fn.popover=function(i){return this.each(function(){var n=t(this),o=n.data("bs.popover"),a="object"==typeof i&&i;!o&&/destroy|hide/.test(i)||(o||n.data("bs.popover",o=new e(this,a)),"string"==typeof i&&o[i]())})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=i,this}}(jQuery),function(t){"use strict";function e(i,n){this.$body=t(document.body),this.$scrollElement=t(t(i).is(document.body)?window:i),this.options=t.extend({},e.DEFAULTS,n),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",t.proxy(this.process,this)),this.refresh(),this.process()}function i(i){return this.each(function(){var n=t(this),o=n.data("bs.scrollspy"),a="object"==typeof i&&i;o||n.data("bs.scrollspy",o=new e(this,a)),"string"==typeof i&&o[i]()})}e.VERSION="3.4.1",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e=this,i="offset",n=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),t.isWindow(this.$scrollElement[0])||(i="position",n=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var e=t(this),o=e.data("target")||e.attr("href"),a=/^#./.test(o)&&t(o);return a&&a.length&&a.is(":visible")&&[[a[i]().top+n,o]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){e.offsets.push(this[0]),e.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,i=this.getScrollHeight(),n=this.options.offset+i-this.$scrollElement.height(),o=this.offsets,a=this.targets,s=this.activeTarget;if(this.scrollHeight!=i&&this.refresh(),n<=e)return s!=(t=a[a.length-1])&&this.activate(t);if(s&&e<o[0])return this.activeTarget=null,this.clear();for(t=o.length;t--;)s!=a[t]&&e>=o[t]&&(void 0===o[t+1]||e<o[t+1])&&this.activate(a[t])},e.prototype.activate=function(e){this.activeTarget=e,this.clear();var i=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',n=t(i).parents("li").addClass("active");n.parent(".dropdown-menu").length&&(n=n.closest("li.dropdown").addClass("active")),n.trigger("activate.bs.scrollspy")},e.prototype.clear=function(){t(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var n=t.fn.scrollspy;t.fn.scrollspy=i,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=n,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);i.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.tab");o||n.data("bs.tab",o=new i(this)),"string"==typeof e&&o[e]()})}var i=function(e){this.element=t(e)};i.VERSION="3.4.1",i.TRANSITION_DURATION=150,i.prototype.show=function(){var e=this.element,i=e.closest("ul:not(.dropdown-menu)"),n=e.data("target");if(n||(n=(n=e.attr("href"))&&n.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var o=i.find(".active:last a"),a=t.Event("hide.bs.tab",{relatedTarget:e[0]}),s=t.Event("show.bs.tab",{relatedTarget:o[0]});if(o.trigger(a),e.trigger(s),!s.isDefaultPrevented()&&!a.isDefaultPrevented()){var r=t(document).find(n);this.activate(e.closest("li"),i),this.activate(r,r.parent(),function(){o.trigger({type:"hidden.bs.tab",relatedTarget:e[0]}),e.trigger({type:"shown.bs.tab",relatedTarget:o[0]})})}}},i.prototype.activate=function(e,n,o){function a(){s.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),e.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),r?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu").length&&e.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),o&&o()}var s=n.find("> .active"),r=o&&t.support.transition&&(s.length&&s.hasClass("fade")||!!n.find("> .fade").length);s.length&&r?s.one("bsTransitionEnd",a).emulateTransitionEnd(i.TRANSITION_DURATION):a(),s.removeClass("in")};var n=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=n,this};var o=function(i){i.preventDefault(),e.call(t(this),"show")};t(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',o).on("click.bs.tab.data-api",'[data-toggle="pill"]',o)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.affix"),a="object"==typeof e&&e;o||n.data("bs.affix",o=new i(this,a)),"string"==typeof e&&o[e]()})}var i=function(e,n){this.options=t.extend({},i.DEFAULTS,n);var o=this.options.target===i.DEFAULTS.target?t(this.options.target):t(document).find(this.options.target);this.$target=o.on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};i.VERSION="3.4.1",i.RESET="affix affix-top affix-bottom",i.DEFAULTS={offset:0,target:window},i.prototype.getState=function(t,e,i,n){var o=this.$target.scrollTop(),a=this.$element.offset(),s=this.$target.height();if(null!=i&&"top"==this.affixed)return o<i&&"top";if("bottom"==this.affixed)return null!=i?!(o+this.unpin<=a.top)&&"bottom":!(o+s<=t-n)&&"bottom";var r=null==this.affixed,l=r?o:a.top;return null!=i&&o<=i?"top":null!=n&&t-n<=l+(r?s:e)&&"bottom"},i.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(i.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},i.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},i.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=this.$element.height(),n=this.options.offset,o=n.top,a=n.bottom,s=Math.max(t(document).height(),t(document.body).height());"object"!=typeof n&&(a=o=n),"function"==typeof o&&(o=n.top(this.$element)),"function"==typeof a&&(a=n.bottom(this.$element));var r=this.getState(s,e,o,a);if(this.affixed!=r){null!=this.unpin&&this.$element.css("top","");var l="affix"+(r?"-"+r:""),c=t.Event(l+".bs.affix");if(this.$element.trigger(c),c.isDefaultPrevented())return;this.affixed=r,this.unpin="bottom"==r?this.getPinnedOffset():null,this.$element.removeClass(i.RESET).addClass(l).trigger(l.replace("affix","affixed")+".bs.affix")}"bottom"==r&&this.$element.offset({top:s-e-a})}};var n=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=i,t.fn.affix.noConflict=function(){return t.fn.affix=n,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var i=t(this),n=i.data();n.offset=n.offset||{},null!=n.offsetBottom&&(n.offset.bottom=n.offsetBottom),null!=n.offsetTop&&(n.offset.top=n.offsetTop),e.call(i,n)})})}(jQuery),define("bootstrap",["jquery"],function(){}),require.config({urlArgs:"v="+requirejs.s.contexts._.config.config.site.version,packages:[{name:"moment",location:"../libs/moment",main:"moment"}],include:["css","layer","toastr","fast","backend","backend-init","table","form","dragsort","addtabs","selectpage","bootstrap-daterangepicker"],paths:{lang:"empty:",form:"require-form",table:"require-table",upload:"require-upload",dropzone:"dropzone.min",echarts:"echarts.min","echarts-theme":"echarts-theme",adminlte:"adminlte","bootstrap-table-commonsearch":"bootstrap-table-commonsearch","bootstrap-table-template":"bootstrap-table-template",jquery:"../libs/jquery/dist/jquery.min",bootstrap:"../libs/bootstrap/dist/js/bootstrap.min","bootstrap-datetimepicker":"../libs/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min","bootstrap-daterangepicker":"../libs/bootstrap-daterangepicker/daterangepicker","bootstrap-select":"../libs/bootstrap-select/dist/js/bootstrap-select.min","bootstrap-select-lang":"../libs/bootstrap-select/dist/js/i18n/defaults-zh_CN","bootstrap-table":"../libs/bootstrap-table/dist/bootstrap-table.min",
"bootstrap-table-export":"../libs/bootstrap-table/dist/extensions/export/bootstrap-table-export.min","bootstrap-table-fixed-columns":"../libs/bootstrap-table/dist/extensions/fixed-columns/bootstrap-table-fixed-columns","bootstrap-table-mobile":"../libs/bootstrap-table/dist/extensions/mobile/bootstrap-table-mobile","bootstrap-table-lang":"../libs/bootstrap-table/dist/locale/bootstrap-table-zh-CN","bootstrap-table-jumpto":"../libs/bootstrap-table/dist/extensions/page-jumpto/bootstrap-table-jumpto","bootstrap-slider":"../libs/bootstrap-slider/bootstrap-slider",tableexport:"../libs/tableExport.jquery.plugin/tableExport.min",dragsort:"../libs/fastadmin-dragsort/jquery.dragsort",sortable:"../libs/Sortable/Sortable.min",addtabs:"../libs/fastadmin-addtabs/jquery.addtabs",slimscroll:"../libs/jquery-slimscroll/jquery.slimscroll",validator:"../libs/nice-validator/dist/jquery.validator","validator-lang":"../libs/nice-validator/dist/local/zh-CN",toastr:"../libs/toastr/toastr",jstree:"../libs/jstree/dist/jstree.min",layer:"../libs/fastadmin-layer/dist/layer",cookie:"../libs/jquery.cookie/jquery.cookie",cxselect:"../libs/fastadmin-cxselect/js/jquery.cxselect",template:"../libs/art-template/dist/template-native",selectpage:"../libs/fastadmin-selectpage/selectpage",citypicker:"../libs/fastadmin-citypicker/dist/js/city-picker.min","citypicker-data":"../libs/fastadmin-citypicker/dist/js/city-picker.data"},shim:{addons:["backend"],bootstrap:["jquery"],"bootstrap-table":{deps:["bootstrap"],exports:"$.fn.bootstrapTable"},"bootstrap-table-lang":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-export":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-fixed-columns":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-mobile":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-advancedsearch":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-commonsearch":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-template":{deps:["bootstrap-table","template"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-jumpto":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},tableexport:{deps:["jquery"],exports:"$.fn.extend"},slimscroll:{deps:["jquery"],exports:"$.fn.extend"},adminlte:{deps:["bootstrap","slimscroll"],exports:"$.AdminLTE"},"bootstrap-daterangepicker":["moment/locale/zh-cn"],"bootstrap-datetimepicker":["moment/locale/zh-cn"],"bootstrap-select-lang":["bootstrap-select"],jstree:["css!../libs/jstree/dist/themes/default/style.css"],"validator-lang":["validator"],citypicker:["citypicker-data","css!../libs/fastadmin-citypicker/dist/css/city-picker.css"]},baseUrl:requirejs.s.contexts._.config.config.site.cdnurl+"/assets/js/",map:{"*":{css:"../libs/require-css/css.min"}},waitSeconds:60,charset:"utf-8"}),require(["jquery","bootstrap"],function(t,e){var i=requirejs.s.contexts._.config.config;window.Config=i;var n={};n.lang=i.moduleurl+"/ajax/lang?callback=define&controllername="+i.controllername+"&lang="+i.language,n["backend/"]="backend/",require.config({paths:n}),t(function(){require(["fast"],function(t){require(["backend","backend-init","addons"],function(t,e,n){i.jsname&&require([i.jsname],function(t){t.hasOwnProperty(i.actionname)?t[i.actionname]():t.hasOwnProperty("_empty")&&t._empty()},function(t){console.error(t)})})})})}),define("require-backend",function(){}),define("../libs/require-css/css.min",[],function(){if("undefined"==typeof window)return{load:function(t,e,i){i()}};var t=document.getElementsByTagName("head")[0],e=window.navigator.userAgent.match(/Trident\/([^ ;]*)|AppleWebKit\/([^ ;]*)|Opera\/([^ ;]*)|rv\:([^ ;]*)(.*?)Gecko\/([^ ;]*)|MSIE\s([^ ;]*)|AndroidWebKit\/([^ ;]*)/)||0,i=!1,n=!0;e[1]||e[7]?i=parseInt(e[1])<6||parseInt(e[7])<=9:e[2]||e[8]?n=!1:e[4]&&(i=parseInt(e[4])<18);var o={};o.pluginBuilder="./css-builder";var a,s,r,l=function(){a=document.createElement("style"),t.appendChild(a),s=a.styleSheet||a.sheet},c=0,d=[],u=function(t){s.addImport(t),a.onload=function(){h()},31==++c&&(l(),c=0)},h=function(){r();var t=d.shift();return t?(r=t[1],void u(t[0])):void(r=null)},p=function(t,e){if(s&&s.addImport||l(),s&&s.addImport)r?d.push([t,e]):(u(t),r=e);else{a.textContent='@import "'+t+'";';var i=setInterval(function(){try{a.sheet.cssRules,clearInterval(i),e()}catch(t){}},10)}},f=function(e,i){var o=document.createElement("link");if(o.type="text/css",o.rel="stylesheet",n)o.onload=function(){o.onload=function(){},setTimeout(i,7)};else var a=setInterval(function(){for(var t=0;t<document.styleSheets.length;t++){if(document.styleSheets[t].href==o.href)return clearInterval(a),i()}},10);o.href=e,t.appendChild(o)};return o.normalize=function(t,e){return".css"==t.substr(t.length-4,4)&&(t=t.substr(0,t.length-4)),e(t)},o.load=function(t,e,n,o){(i?p:f)(e.toUrl(t+".css"),n)},o}),function(t,e){"use strict";var i,n,o=t.layui&&layui.define,a={getPath:function(){var e=document.currentScript?document.currentScript.src:function(){for(var t,e=document.scripts,i=e.length-1,n=i;n>0;n--)if("interactive"===e[n].readyState){t=e[n].src;break}return t||e[i].src}();return(t.LAYUI_GLOBAL||{}).layer_dir||e.substring(0,e.lastIndexOf("/")+1)}(),config:{},end:{},minIndex:0,minLeft:[],btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],type:["dialog","page","iframe","loading","tips"],getStyle:function(e,i){var n=e.currentStyle?e.currentStyle:t.getComputedStyle(e,null);return n[n.getPropertyValue?"getPropertyValue":"getAttribute"](i)},link:function(e,i,n){if(s.path){var o=document.getElementsByTagName("head")[0],r=document.createElement("link");"string"==typeof i&&(n=i);var l=(n||e).replace(/\.|\//g,""),c="layuicss-"+l,d="creating",u=0;r.rel="stylesheet",r.href=s.path+e,r.id=c,document.getElementById(c)||o.appendChild(r),"function"==typeof i&&function e(n){var o=document.getElementById(c);return++u>100?t.console&&console.error(l+".css: Invalid"):void(1989===parseInt(a.getStyle(o,"width"))?(n===d&&o.removeAttribute("lay-status"),o.getAttribute("lay-status")===d?setTimeout(e,100):i()):(o.setAttribute("lay-status",d),setTimeout(function(){e(d)},100)))}()}}},s={v:"3.5.2",ie:function(){var e=navigator.userAgent.toLowerCase();return!!(t.ActiveXObject||"ActiveXObject"in t)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")}(),index:t.layer&&t.layer.v?1e5:0,path:a.getPath,config:function(t,e){return t=t||{},c=s.cache=a.config=i.extend({},a.config,t),s.path=a.config.path||s.path,"string"==typeof t.extend&&(t.extend=[t.extend]),a.config.path&&s.ready(),t.extend?(o?layui.addcss("modules/layer/"+t.extend):a.link("theme/"+t.extend),this):this},ready:function(t){var e="layer",i=(o?"modules/layer/":"theme/")+"default/layer.css?v="+s.v;return o?layui.addcss(i,t,e):a.link(i,t,e),this},alert:function(t,e,n){var o="function"==typeof e;return o&&(n=e),s.open(i.extend({content:t,yes:n},o?{}:e))},confirm:function(t,e,n,o){var r="function"==typeof e;return r&&(o=n,n=e),s.open(i.extend({content:t,btn:a.btn,yes:n,btn2:o},r?{}:e))},msg:function(t,e,n){var o="function"==typeof e,r=a.config.skin,c=(r?r+" "+r+"-msg":"")||"layui-layer-msg",d=l.anim.length-1;return o&&(n=e),s.open(i.extend({content:t,time:3e3,shade:!1,skin:c,title:!1,closeBtn:!1,btn:!1,resize:!1,end:n},o&&!a.config.skin?{skin:c+" layui-layer-hui",anim:d}:function(){return e=e||{},-1!==e.icon&&void 0!==e.icon||(e.skin=c+" "+(e.skin||"layui-layer-hui")),e}()))},load:function(t,e){return s.open(i.extend({type:3,icon:t||0,resize:!1,shade:.01},e))},tips:function(t,e,n){return s.open(i.extend({type:4,content:[t,e],closeBtn:!1,time:3e3,shade:!1,resize:!1,fixed:!1,maxWidth:260},n))}},r=function(t){var e=this,n=function(){e.creat()};e.index=++s.index,e.config=i.extend({},e.config,a.config,t),document.body?n():setTimeout(function(){n()},30)};r.pt=r.prototype;var l=["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"];l.anim=["layer-anim-00","layer-anim-01","layer-anim-02","layer-anim-03","layer-anim-04","layer-anim-05","layer-anim-06"],l.SHADE="layui-layer-shade",l.MOVE="layui-layer-move",r.pt.config={type:0,shade:.3,fixed:!0,move:l[1],title:"&#x4FE1;&#x606F;",offset:"auto",area:"auto",closeBtn:1,time:0,zIndex:19891014,maxWidth:360,anim:0,isOutAnim:!0,minStack:!0,focusBtn:0,icon:-1,moveType:1,resize:!0,scrollbar:!0,tips:2},r.pt.vessel=function(t,e){var n=this,o=n.index,s=n.config,r=s.zIndex+o,c="object"==typeof s.title,d=s.maxmin&&(1===s.type||2===s.type),u=s.title?'<div class="layui-layer-title" style="'+(c?s.title[1]:"")+'">'+(c?s.title[0]:s.title)+"</div>":"";return s.zIndex=r,e([s.shade?'<div class="'+l.SHADE+'" id="'+l.SHADE+o+'" times="'+o+'" style="z-index:'+(r-1)+'; "></div>':"",'<div class="'+l[0]+" layui-layer-"+a.type[s.type]+(0!=s.type&&2!=s.type||s.shade?"":" layui-layer-border")+" "+(s.skin||"")+'" id="'+l[0]+o+'" type="'+a.type[s.type]+'" times="'+o+'" showtime="'+s.time+'" conType="'+(t?"object":"string")+'" style="z-index: '+r+"; width:"+s.area[0]+";height:"+s.area[1]+";position:"+(s.fixed?"fixed;":"absolute;")+'">'+(t&&2!=s.type?"":u)+'<div id="'+(s.id||"")+'" class="layui-layer-content'+(0==s.type&&-1!==s.icon?" layui-layer-padding":"")+(3==s.type?" layui-layer-loading"+s.icon:"")+'">'+(0==s.type&&-1!==s.icon?'<i class="layui-layer-ico layui-layer-ico'+s.icon+'"></i>':"")+(1==s.type&&t?"":s.content||"")+'</div><span class="layui-layer-setwin">'+function(){var t=d?'<a class="layui-layer-min" href="javascript:;"><cite></cite></a><a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>':"";return s.closeBtn&&(t+='<a class="layui-layer-ico '+l[7]+" "+l[7]+(s.title?s.closeBtn:4==s.type?"1":"2")+'" href="javascript:;"></a>'),t}()+"</span>"+(s.btn?function(){var t="";"string"==typeof s.btn&&(s.btn=[s.btn]);for(var e=0,i=s.btn.length;e<i;e++)t+='<a class="'+l[6]+e+'" href="javascript:">'+s.btn[e]+"</a>";return'<div class="'+l[6]+" layui-layer-btn-"+(s.btnAlign||"")+'">'+t+"</div>"}():"")+(s.resize?'<span class="layui-layer-resize"></span>':"")+"</div>"],u,i('<div class="'+l.MOVE+'" id="'+l.MOVE+'"></div>')),n},r.pt.creat=function(){var t=this,e=t.config,o=t.index,r=e.content,c="object"==typeof r,d=i("body");if(!e.id||!i("#"+e.id)[0]){switch("string"==typeof e.area&&(e.area="auto"===e.area?["",""]:[e.area,""]),e.shift&&(e.anim=e.shift),6==s.ie&&(e.fixed=!1),e.type){case 0:e.btn="btn"in e?e.btn:a.btn[0],s.closeAll("dialog");break;case 2:var r=e.content=c?e.content:[e.content||"","auto"];e.content='<iframe scrolling="'+(e.content[1]||"auto")+'" allowtransparency="true" id="'+l[4]+o+'" name="'+l[4]+o+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+e.content[0]+'"></iframe>';break;case 3:delete e.title,delete e.closeBtn,-1===e.icon&&e.icon,s.closeAll("loading");break;case 4:c||(e.content=[e.content,"body"]),e.follow=e.content[1],e.content=e.content[0]+'<i class="layui-layer-TipsG"></i>',delete e.title,e.tips="object"==typeof e.tips?e.tips:[e.tips,!0],e.tipsMore||s.closeAll("tips")}if(t.vessel(c,function(n,s,u){d.append(n[0]),c?function(){2==e.type||4==e.type?function(){i("body").append(n[1])}():function(){r.parents("."+l[0])[0]||(r.data("display",r.css("display")).show().addClass("layui-layer-wrap").wrap(n[1]),i("#"+l[0]+o).find("."+l[5]).before(s))}()}():d.append(n[1]),i("#"+l.MOVE)[0]||d.append(a.moveElem=u),t.layero=i("#"+l[0]+o),t.shadeo=i("#"+l.SHADE+o),e.scrollbar||l.html.css("overflow","hidden").attr("layer-full",o)}).auto(o),t.shadeo.css({"background-color":e.shade[1]||"#000",opacity:e.shade[0]||e.shade}),2==e.type&&6==s.ie&&t.layero.find("iframe").attr("src",r[0]),4==e.type?t.tips():function(){t.offset(),parseInt(a.getStyle(document.getElementById(l.MOVE),"z-index"))||function(){t.layero.css("visibility","hidden"),s.ready(function(){t.offset(),t.layero.css("visibility","visible")})}()}(),e.fixed&&n.on("resize",function(){t.offset(),(/^\d+%$/.test(e.area[0])||/^\d+%$/.test(e.area[1]))&&t.auto(o),4==e.type&&t.tips()}),e.time<=0||setTimeout(function(){s.close(t.index)},e.time),t.move().callback(),l.anim[e.anim]){var u="layer-anim "+l.anim[e.anim];t.layero.addClass(u).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){i(this).removeClass(u)})}e.isOutAnim&&t.layero.data("isOutAnim",!0)}},r.pt.auto=function(t){var e=this,o=e.config,a=i("#"+l[0]+t);""===o.area[0]&&o.maxWidth>0&&(s.ie&&s.ie<8&&o.btn&&a.width(a.innerWidth()),a.outerWidth()>o.maxWidth&&a.width(o.maxWidth));var r=[a.innerWidth(),a.innerHeight()],c=a.find(l[1]).outerHeight()||0,d=a.find("."+l[6]).outerHeight()||0,u=function(t){t=a.find(t),t.height(r[1]-c-d-2*(0|parseFloat(t.css("padding-top"))))};switch(o.type){case 2:u("iframe");break;default:""===o.area[1]?o.maxHeight>0&&a.outerHeight()>o.maxHeight?(r[1]=o.maxHeight,u("."+l[5])):o.fixed&&r[1]>=n.height()&&(r[1]=n.height(),u("."+l[5])):u("."+l[5])}return e},r.pt.offset=function(){var t=this,e=t.config,i=t.layero,o=[i.outerWidth(),i.outerHeight()],a="object"==typeof e.offset;t.offsetTop=(n.height()-o[1])/2,t.offsetLeft=(n.width()-o[0])/2,a?(t.offsetTop=e.offset[0],t.offsetLeft=e.offset[1]||t.offsetLeft):"auto"!==e.offset&&("t"===e.offset?t.offsetTop=0:"r"===e.offset?t.offsetLeft=n.width()-o[0]:"b"===e.offset?t.offsetTop=n.height()-o[1]:"l"===e.offset?t.offsetLeft=0:"lt"===e.offset?(t.offsetTop=0,t.offsetLeft=0):"lb"===e.offset?(t.offsetTop=n.height()-o[1],t.offsetLeft=0):"rt"===e.offset?(t.offsetTop=0,t.offsetLeft=n.width()-o[0]):"rb"===e.offset?(t.offsetTop=n.height()-o[1],t.offsetLeft=n.width()-o[0]):t.offsetTop=e.offset),e.fixed||(t.offsetTop=/%$/.test(t.offsetTop)?n.height()*parseFloat(t.offsetTop)/100:parseFloat(t.offsetTop),t.offsetLeft=/%$/.test(t.offsetLeft)?n.width()*parseFloat(t.offsetLeft)/100:parseFloat(t.offsetLeft),t.offsetTop+=n.scrollTop(),t.offsetLeft+=n.scrollLeft()),i.attr("minLeft")&&(t.offsetTop=n.height()-(i.find(l[1]).outerHeight()||0),t.offsetLeft=i.css("left")),i.css({top:t.offsetTop,left:t.offsetLeft})},r.pt.tips=function(){var t=this,e=t.config,o=t.layero,a=[o.outerWidth(),o.outerHeight()],s=i(e.follow);s[0]||(s=i("body"));var r={width:s.outerWidth(),height:s.outerHeight(),top:s.offset().top,left:s.offset().left},c=o.find(".layui-layer-TipsG"),d=e.tips[0];e.tips[1]||c.remove(),r.autoLeft=function(){r.left+a[0]-n.width()>0?(r.tipLeft=r.left+r.width-a[0],c.css({right:12,left:"auto"})):r.tipLeft=r.left},r.where=[function(){r.autoLeft(),r.tipTop=r.top-a[1]-10,c.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",e.tips[1])},function(){r.tipLeft=r.left+r.width+10,r.tipTop=r.top,c.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",e.tips[1])},function(){r.autoLeft(),r.tipTop=r.top+r.height+10,c.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",e.tips[1])},function(){r.tipLeft=r.left-a[0]-10,r.tipTop=r.top,c.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",e.tips[1])}],r.where[d-1](),1===d?r.top-(n.scrollTop()+a[1]+16)<0&&r.where[2]():2===d?n.width()-(r.left+r.width+a[0]+16)>0||r.where[3]():3===d?r.top-n.scrollTop()+r.height+a[1]+16-n.height()>0&&r.where[0]():4===d&&a[0]+16-r.left>0&&r.where[1](),o.find("."+l[5]).css({"background-color":e.tips[1],"padding-right":e.closeBtn?"30px":""}),o.css({left:r.tipLeft-(e.fixed?n.scrollLeft():0),top:r.tipTop-(e.fixed?n.scrollTop():0)})},r.pt.move=function(){var t=this,e=t.config,o=i(document),r=t.layero,l=r.find(e.move),c=r.find(".layui-layer-resize"),d={};return e.move&&l.css("cursor","move"),l.on("mousedown",function(t){t.preventDefault(),e.move&&(d.moveStart=!0,d.offset=[t.clientX-parseFloat(r.css("left")),t.clientY-parseFloat(r.css("top"))],a.moveElem.css("cursor","move").show())}),c.on("mousedown",function(t){t.preventDefault(),d.resizeStart=!0,d.offset=[t.clientX,t.clientY],d.area=[r.outerWidth(),r.outerHeight()],a.moveElem.css("cursor","se-resize").show()}),o.on("mousemove",function(i){if(d.moveStart){var o=i.clientX-d.offset[0],a=i.clientY-d.offset[1],l="fixed"===r.css("position");if(i.preventDefault(),d.stX=l?0:n.scrollLeft(),d.stY=l?0:n.scrollTop(),!e.moveOut){var c=n.width()-r.outerWidth()+d.stX,u=n.height()-r.outerHeight()+d.stY;o<d.stX&&(o=d.stX),o>c&&(o=c),a<d.stY&&(a=d.stY),a>u&&(a=u)}r.css({left:o,top:a})}if(e.resize&&d.resizeStart){var o=i.clientX-d.offset[0],a=i.clientY-d.offset[1];i.preventDefault(),s.style(t.index,{width:d.area[0]+o,height:d.area[1]+a}),d.isResize=!0,e.resizing&&e.resizing(r)}}).on("mouseup",function(t){d.moveStart&&(delete d.moveStart,a.moveElem.hide(),e.moveEnd&&e.moveEnd(r)),d.resizeStart&&(delete d.resizeStart,a.moveElem.hide())}),t},r.pt.callback=function(){function t(){!1===(o.cancel&&o.cancel(e.index,n))||s.close(e.index)}var e=this,n=e.layero,o=e.config;if(e.openLayer(),o.success&&(2==o.type?n.find("iframe").on("load",function(){o.success(n,e.index)}):o.success(n,e.index)),6==s.ie&&e.IE6(n),n.find("."+l[6]).children("a").on("click",function(){var t=i(this).index();if(0===t)o.yes?o.yes(e.index,n):o.btn1?o.btn1(e.index,n):s.close(e.index);else{!1===(o["btn"+(t+1)]&&o["btn"+(t+1)](e.index,n))||s.close(e.index)}}),"number"==typeof o.focusBtn){n.find("."+l[6]).children("a").each(function(){var t=i(this),e=i("<button type='button' />").addClass("layui-layer-confirm");t.css("position","relative").attr("tabindex",-1).append(e),e.click(function(){return t.trigger("click"),!1})});var r=n.find("."+l[6]).find("button.layui-layer-confirm").eq(o.focusBtn);r.length>0&&r.focus()}n.find("."+l[7]).on("click",t),o.shadeClose&&e.shadeo.on("click",function(){s.close(e.index)}),n.find(".layui-layer-min").on("click",function(){!1===(o.min&&o.min(n,e.index))||s.min(e.index,o)}),n.find(".layui-layer-max").on("click",function(){i(this).hasClass("layui-layer-maxmin")?(s.restore(e.index),o.restore&&o.restore(n,e.index)):(s.full(e.index,o),setTimeout(function(){o.full&&o.full(n,e.index)},100))}),o.end&&(a.end[e.index]=o.end)},a.reselect=function(){i.each(i("select"),function(t,e){var n=i(this);n.parents("."+l[0])[0]||1==n.attr("layer")&&i("."+l[0]).length<1&&n.removeAttr("layer").show(),n=null})},r.pt.IE6=function(t){i("select").each(function(t,e){var n=i(this);n.parents("."+l[0])[0]||"none"===n.css("display")||n.attr({layer:"1"}).hide(),n=null})},r.pt.openLayer=function(){var t=this;s.zIndex=t.config.zIndex,s.setTop=function(t){var e=function(){s.zIndex++,t.css("z-index",s.zIndex+1)};return s.zIndex=parseInt(t[0].style.zIndex),t.on("mousedown",e),s.zIndex}},a.record=function(t){var e=[t.width(),t.height(),t.position().top,t.position().left+parseFloat(t.css("margin-left"))];t.find(".layui-layer-max").addClass("layui-layer-maxmin"),t.attr({area:e})},a.rescollbar=function(t){l.html.attr("layer-full")==t&&(l.html[0].style.removeProperty?l.html[0].style.removeProperty("overflow"):l.html[0].style.removeAttribute("overflow"),l.html.removeAttr("layer-full"))},t.layer=s,s.getChildFrame=function(t,e){return e=e||i("."+l[4]).attr("times"),i("#"+l[0]+e).find("iframe").contents().find(t)},s.getFrameIndex=function(t){return i("#"+t).parents("."+l[4]).attr("times")},s.iframeAuto=function(t){if(t){var e=s.getChildFrame("html",t).outerHeight(),n=i("#"+l[0]+t),o=n.find(l[1]).outerHeight()||0,a=n.find("."+l[6]).outerHeight()||0;n.css({height:e+o+a}),n.find("iframe").css({height:e})}},s.iframeSrc=function(t,e){i("#"+l[0]+t).find("iframe").attr("src",e)},s.style=function(t,e,n){var o=i("#"+l[0]+t),s=o.find(".layui-layer-content"),r=o.attr("type"),c=o.find(l[1]).outerHeight()||0,d=o.find("."+l[6]).outerHeight()||0;if(o.attr("minLeft"),r!==a.type[3]&&r!==a.type[4])if(n||(parseFloat(e.width)<=260&&(e.width=260),parseFloat(e.height)-c-d<=64&&(e.height=64+c+d)),o.css(e),d=o.find("."+l[6]).outerHeight()||0,r===a.type[2])o.find("iframe").css({height:parseFloat(e.height)-c-d});else{var u="border-box"==s.css("box-sizing");s.css({height:parseFloat(e.height)-c-d-parseFloat(u?0:s.css("padding-top"))-parseFloat(u?0:s.css("padding-bottom"))})}},s.min=function(t,e){e=e||{};var o=i("#"+l[0]+t),r=i("#"+l.SHADE+t),c=o.find(l[1]).outerHeight()||0,d=o.attr("minLeft")||181*a.minIndex+"px",u=o.css("position"),h={width:180,height:c,position:"fixed",overflow:"hidden"};a.record(o),a.minLeft[0]&&(d=a.minLeft[0],a.minLeft.shift()),e.minStack&&(h.left=d,h.top=n.height()-c,o.attr("minLeft")||a.minIndex++,o.attr("minLeft",d)),o.attr("position",u),s.style(t,h,!0),o.find(".layui-layer-min").hide(),"page"===o.attr("type")&&o.find(l[4]).hide(),a.rescollbar(t),r.hide()},s.restore=function(t){var e=i("#"+l[0]+t),n=i("#"+l.SHADE+t),o=e.attr("area").split(",");e.attr("type"),s.style(t,{width:parseFloat(o[0]),height:parseFloat(o[1]),top:parseFloat(o[2]),left:parseFloat(o[3]),position:e.attr("position"),overflow:"visible"},!0),e.find(".layui-layer-max").removeClass("layui-layer-maxmin"),e.find(".layui-layer-min").show(),"page"===e.attr("type")&&e.find(l[4]).show(),a.rescollbar(t),n.show()},s.full=function(t){var e,o=i("#"+l[0]+t);a.record(o),l.html.attr("layer-full")||l.html.css("overflow","hidden").attr("layer-full",t),clearTimeout(e),e=setTimeout(function(){var e="fixed"===o.css("position");s.style(t,{top:e?0:n.scrollTop(),left:e?0:n.scrollLeft(),width:n.width(),height:n.height()},!0),o.find(".layui-layer-min").hide()},100)},s.title=function(t,e){i("#"+l[0]+(e||s.index)).find(l[1]).html(t)},s.close=function(t,e){var n=i("#"+l[0]+t),o=n.attr("type");if(n[0]){var r="layui-layer-wrap",c=function(){if(o===a.type[1]&&"object"===n.attr("conType")){n.children(":not(."+l[5]+")").remove();for(var s=n.find("."+r),c=0;c<2;c++)s.unwrap();s.css("display",s.data("display")).removeClass(r)}else{if(o===a.type[2])try{var d=i("#"+l[4]+t)[0];d.contentWindow.document.write(""),d.contentWindow.close(),n.find("."+l[5])[0].removeChild(d)}catch(t){}n[0].innerHTML="",n.remove()}"function"==typeof a.end[t]&&a.end[t](),delete a.end[t],"function"==typeof e&&e()};n.data("isOutAnim")&&n.addClass("layer-anim layer-anim-close"),i("#layui-layer-moves, #"+l.SHADE+t).remove(),6==s.ie&&a.reselect(),a.rescollbar(t),n.attr("minLeft")&&(a.minIndex--,a.minLeft.push(n.attr("minLeft"))),s.ie&&s.ie<10||!n.data("isOutAnim")?c():setTimeout(function(){c()},200)}},s.closeAll=function(t,e){"function"==typeof t&&(e=t,t=null);var n=i("."+l[0]);i.each(n,function(o){var a=i(this),r=t?a.attr("type")===t:1;r&&s.close(a.attr("times"),o===n.length-1?e:null),r=null}),0===n.length&&"function"==typeof e&&e()};var c=s.cache||{},d=function(t){return c.skin?" "+c.skin+" "+c.skin+"-"+t:""};s.prompt=function(t,e){var o="";if(t=t||{},"function"==typeof t&&(e=t),t.area){var a=t.area;o='style="width: '+a[0]+"; height: "+a[1]+';"',delete t.area}var r,l=2==t.formType?'<textarea class="layui-layer-input"'+o+"></textarea>":function(){return'<input type="'+(1==t.formType?"password":"text")+'" class="layui-layer-input">'}(),c=t.success;return delete t.success,s.open(i.extend({type:1,btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],content:l,skin:"layui-layer-prompt"+d("prompt"),maxWidth:n.width(),success:function(e){r=e.find(".layui-layer-input"),r.val(t.value||"").focus(),"function"==typeof c&&c(e)},resize:!1,yes:function(i){var n=r.val();""===n?r.focus():n.length>(t.maxlength||500)?s.tips("&#x6700;&#x591A;&#x8F93;&#x5165;"+(t.maxlength||500)+"&#x4E2A;&#x5B57;&#x6570;",r,{tips:1}):e&&e(n,i,r)}},t))},s.tab=function(t){t=t||{};var e=t.tab||{},n="layui-this",o=t.success;return delete t.success,s.open(i.extend({type:1,skin:"layui-layer-tab"+d("tab"),resize:!1,title:function(){var t=e.length,i=1,o="";if(t>0)for(o='<span class="'+n+'">'+e[0].title+"</span>";i<t;i++)o+="<span>"+e[i].title+"</span>";return o}(),content:'<ul class="layui-layer-tabmain">'+function(){var t=e.length,i=1,o="";if(t>0)for(o='<li class="layui-layer-tabli '+n+'">'+(e[0].content||"no content")+"</li>";i<t;i++)o+='<li class="layui-layer-tabli">'+(e[i].content||"no  content")+"</li>";return o}()+"</ul>",success:function(e){var a=e.find(".layui-layer-title").children(),s=e.find(".layui-layer-tabmain").children();a.on("mousedown",function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0;var o=i(this),a=o.index();o.addClass(n).siblings().removeClass(n),s.eq(a).show().siblings().hide(),"function"==typeof t.change&&t.change(a)}),"function"==typeof o&&o(e)}},t))},s.photos=function(e,n,o){var a={};if(e=e||{},e.photos){e.zoom=void 0===e.zoom||!!e.zoom;var r=!("string"==typeof e.photos||e.photos instanceof i),l=r?e.photos:{},c=l.data||[],u=l.start||0;a.imgIndex=1+(0|u),e.img=e.img||"img";var h=e.success;if(delete e.success,r){if(0===c.length)return s.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")}else{var p=i(e.photos),f=function(){c=[],p.find(e.img).each(function(t){var e=i(this);e.attr("layer-index",t),c.push({alt:e.attr("alt"),pid:e.attr("layer-pid"),src:e.attr("layer-src")||e.attr("src"),thumb:e.attr("src")})})};if(f(),0===c.length)return;if(n||p.on("click",e.img,function(){f();var t=i(this),n=t.attr("layer-index");s.photos(i.extend(e,{photos:{start:n,data:c,tab:e.tab},full:e.full}),!0)}),!n)return}a.imgprev=function(t){a.imgIndex--,a.imgIndex<1&&(a.imgIndex=c.length),a.tabimg(t)},a.imgnext=function(t,e){++a.imgIndex>c.length&&(a.imgIndex=1,e)||a.tabimg(t)},a.keyup=function(t){if(!a.end){var e=t.keyCode;t.preventDefault(),37===e?a.imgprev(!0):39===e?a.imgnext(!0):27===e&&s.close(a.index)}},a.tabimg=function(t){if(!(c.length<=1))return l.start=a.imgIndex-1,s.close(a.index),s.photos(e,!0,t)},a.event=function(){a.bigimg.find(".layui-layer-imgprev").on("click",function(t){t.preventDefault(),a.imgprev(!0)}),a.bigimg.find(".layui-layer-imgnext").on("click",function(t){t.preventDefault(),a.imgnext(!0)}),i(document).on("keyup",a.keyup),e.zoom&&a.bigimg.on("wheel mousewheel",i(">img",a.bigimg),function(t){var e=i(this).offset(),n=t.originalEvent.wheelDelta>0,o=n?"+=":"-=",a=Math.floor(12);return!(!n&&(i(this).width()<50||i(this).height()<50)||(i(this).width(o+24).height(o+24).offset({left:n?e.left-a:e.left+a,top:n?e.top-a:e.top+a}),1))})},a.loadi=s.load(1,{shade:!("shade"in e)&&.9,scrollbar:!1}),function(t,e,i){var n=new Image;return n.src=t,n.complete?e(n):(n.onload=function(){n.onload=null,e(n)},void(n.onerror=function(t){n.onerror=null,i(t)}))}(c[u].src,function(n){s.close(a.loadi),o&&(e.anim=-1),a.index=s.open(i.extend({type:1,id:"layui-layer-photos",area:function(){var o=[n.width,n.height],a=[i(t).width()-100,i(t).height()-100];if(!e.full&&(o[0]>a[0]||o[1]>a[1])){var s=[o[0]/a[0],o[1]/a[1]];s[0]>s[1]?(o[0]=o[0]/s[0],o[1]=o[1]/s[0]):s[0]<s[1]&&(o[0]=o[0]/s[1],o[1]=o[1]/s[1])}return[o[0]+"px",o[1]+"px"]}(),title:!1,shade:.9,shadeClose:!0,closeBtn:!1,move:".layui-layer-phimg img",moveType:1,scrollbar:!1,moveOut:!0,anim:5,isOutAnim:!1,skin:"layui-layer-photos"+d("photos"),content:'<div class="layui-layer-phimg"><img src="'+c[u].src+'" alt="'+(c[u].alt||"")+'" layer-pid="'+c[u].pid+'">'+function(){return c.length>1?'<div class="layui-layer-imgsee"><span class="layui-layer-imguide"><a href="javascript:;" class="layui-layer-iconext layui-layer-imgprev"></a><a href="javascript:;" class="layui-layer-iconext layui-layer-imgnext"></a></span><div class="layui-layer-imgbar" style="display:'+(o?"block":"")+'"><span class="layui-layer-imgtit"><a href="javascript:;">'+(c[u].alt||"")+"</a><em>"+a.imgIndex+" / "+c.length+"</em></span></div></div>":""}()+"</div>",success:function(t,i){a.bigimg=t.find(".layui-layer-phimg"),a.imgsee=t.find(".layui-layer-imgbar"),a.event(t),e.tab&&e.tab(c[u],t),"function"==typeof h&&h(t)},end:function(){a.end=!0,i(document).off("keyup",a.keyup)}},e))},function(){s.close(a.loadi),s.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;",{time:3e4,btn:["&#x4E0B;&#x4E00;&#x5F20;","&#x4E0D;&#x770B;&#x4E86;"],yes:function(){c.length>1&&a.imgnext(!0,!0)}})})}},a.run=function(e){i=e,n=i(t),l.html=i("html"),s.open=function(t){return new r(t).index}},t.layui&&layui.define?(s.ready(),layui.define("jquery",function(e){s.path=layui.cache.dir,a.run(layui.$),t.layer=s,e("layer",s)})):"function"==typeof define&&define.amd?define("layer",["jquery"],function(){return a.run(t.jQuery),s}):function(){s.ready(),a.run(t.jQuery)}()}(window),function(t){t("toastr",["jquery"],function(t){return function(){function e(t,e,i){return f({type:w.error,iconClass:m().iconClasses.error,message:t,optionsOverride:i,title:e})}function i(e,i){return e||(e=m()),v=t("#"+e.containerId),v.length?v:(i&&(v=u(e)),v)}function n(t,e,i){return f({type:w.info,iconClass:m().iconClasses.info,message:t,optionsOverride:i,title:e})}function o(t){y=t}function a(t,e,i){return f({type:w.success,iconClass:m().iconClasses.success,message:t,optionsOverride:i,title:e})}function s(t,e,i){return f({type:w.warning,iconClass:m().iconClasses.warning,message:t,optionsOverride:i,title:e})}function r(t,e){var n=m();v||i(n),d(t,n,e)||c(n)}function l(e){var n=m();if(v||i(n),e&&0===t(":focus",e).length)return void g(e);v.children().length&&v.remove()}function c(e){for(var i=v.children(),n=i.length-1;n>=0;n--)d(t(i[n]),e)}function d(e,i,n){var o=!(!n||!n.force)&&n.force;return!(!e||!o&&0!==t(":focus",e).length)&&(e[i.hideMethod]({duration:i.hideDuration,easing:i.hideEasing,complete:function(){g(e)}}),!0)}function u(e){return v=t("<div/>").attr("id",e.containerId).addClass(e.positionClass),v.appendTo(t(e.target)),v}function h(){return{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1}}function p(t){y&&y(t)}function f(e){function n(t){return null==t&&(t=""),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function o(){var t="";switch(e.iconClass){case"toast-success":case"toast-info":t="polite";break;default:t="assertive"}S.attr("aria-live",t)}function a(){e.iconClass&&S.addClass(k.toastClass).addClass(_)}function s(){k.newestOnTop?v.prepend(S):v.append(S)}function r(){if(e.title){var t=e.title;k.escapeHtml&&(t=n(e.title)),T.append(t).addClass(k.titleClass),S.append(T)}}function l(){if(e.message){var t=e.message;k.escapeHtml&&(t=n(e.message)),D.append(t).addClass(k.messageClass),S.append(D)}}function c(){k.closeButton&&(E.addClass(k.closeClass).attr("role","button"),S.prepend(E))}function d(){k.progressBar&&($.addClass(k.progressClass),S.prepend($))}function u(){k.rtl&&S.addClass("rtl")}function h(e){var i=e&&!1!==k.closeMethod?k.closeMethod:k.hideMethod,n=e&&!1!==k.closeDuration?k.closeDuration:k.hideDuration,o=e&&!1!==k.closeEasing?k.closeEasing:k.hideEasing;if(!t(":focus",S).length||e)return clearTimeout(A.intervalId),S[i]({duration:n,easing:o,complete:function(){g(S),clearTimeout(C),k.onHidden&&"hidden"!==O.state&&k.onHidden(),O.state="hidden",O.endTime=new Date,p(O)}})}function f(){(k.timeOut>0||k.extendedTimeOut>0)&&(C=setTimeout(h,k.extendedTimeOut),A.maxHideTime=parseFloat(k.extendedTimeOut),A.hideEta=(new Date).getTime()+A.maxHideTime)}function y(){clearTimeout(C),A.hideEta=0,S.stop(!0,!0)[k.showMethod]({duration:k.showDuration,easing:k.showEasing})}function w(){var t=(A.hideEta-(new Date).getTime())/A.maxHideTime*100;$.width(t+"%")}var k=m(),_=e.iconClass||k.iconClass;if(void 0!==e.optionsOverride&&(k=t.extend(k,e.optionsOverride),_=e.optionsOverride.iconClass||_),!function(t,e){
if(t.preventDuplicates){if(e.message===b)return!0;b=e.message}return!1}(k,e)){x++,v=i(k,!0);var C=null,S=t("<div/>"),T=t("<div/>"),D=t("<div/>"),$=t("<div/>"),E=t(k.closeHtml),A={intervalId:null,hideEta:null,maxHideTime:null},O={toastId:x,state:"visible",startTime:new Date,options:k,map:e};return function(){a(),r(),l(),c(),d(),u(),s(),o()}(),function(){S.hide(),S[k.showMethod]({duration:k.showDuration,easing:k.showEasing,complete:k.onShown}),k.timeOut>0&&(C=setTimeout(h,k.timeOut),A.maxHideTime=parseFloat(k.timeOut),A.hideEta=(new Date).getTime()+A.maxHideTime,k.progressBar&&(A.intervalId=setInterval(w,10)))}(),function(){k.closeOnHover&&S.hover(y,f),!k.onclick&&k.tapToDismiss&&S.click(h),k.closeButton&&E&&E.click(function(t){t.stopPropagation?t.stopPropagation():void 0!==t.cancelBubble&&!0!==t.cancelBubble&&(t.cancelBubble=!0),k.onCloseClick&&k.onCloseClick(t),h(!0)}),k.onclick&&S.click(function(t){k.onclick(t),h()})}(),p(O),k.debug&&console&&console.log(O),S}}function m(){return t.extend({},h(),k.options)}function g(t){v||(v=i()),t.is(":visible")||(t.remove(),t=null,0===v.children().length&&(v.remove(),b=void 0))}var v,y,b,x=0,w={error:"error",info:"info",success:"success",warning:"warning"},k={clear:r,remove:l,error:e,getContainer:i,info:n,options:{},subscribe:o,success:a,version:"2.1.3",warning:s};return k}()})}("function"==typeof define&&define.amd?define:function(t,e){"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):window.toastr=e(window.jQuery)}),define("fast",["jquery","bootstrap","toastr","layer","lang"],function(t,e,i,n,o){var a={config:{toastr:{closeButton:!0,debug:!1,newestOnTop:!1,progressBar:!1,positionClass:"toast-top-center",preventDuplicates:!1,onclick:null,showDuration:"300",hideDuration:"1000",timeOut:"5000",extendedTimeOut:"1000",showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"}},events:{onAjaxSuccess:function(t,e){var n=void 0!==t.data?t.data:null,o=void 0!==t.msg&&t.msg?t.msg:__("Operation completed");if("function"==typeof e){if(!1===e.call(this,n,t))return}i.success(o)},onAjaxError:function(t,e){var n=void 0!==t.data?t.data:null;if("function"==typeof e){if(!1===e.call(this,n,t))return}i.error(t.msg)},onAjaxResponse:function(e){try{var i="object"==typeof e?e:JSON.parse(e);i.hasOwnProperty("code")||t.extend(i,{code:-2,msg:e,data:null})}catch(t){var i={code:-1,msg:t.message,data:null}}return i}},api:{ajax:function(e,i,o){e="string"==typeof e?{url:e}:e;var s;return(void 0===e.loading||e.loading)&&(s=n.load(e.loading||0)),e=t.extend({type:"POST",dataType:"json",xhrFields:{withCredentials:!0},success:function(t){s&&n.close(s),t=a.events.onAjaxResponse(t),1===t.code?a.events.onAjaxSuccess(t,i):a.events.onAjaxError(t,o)},error:function(t){s&&n.close(s);var e={code:t.status,msg:t.statusText,data:null};a.events.onAjaxError(e,o)}},e),t.ajax(e)},fixurl:function(t){if("/"!==t.substr(0,1)){new RegExp("^(?:[a-z]+:)?//","i").test(t)||(t=Config.moduleurl+"/"+t)}else"/addons/"===t.substr(0,8)&&(t=Config.__PUBLIC__.replace(/(\/*$)/g,"")+t);return t},cdnurl:function(t,e){var i=new RegExp("^((?:[a-z]+:)?\\/\\/|data:image\\/)","i"),n=Config.upload.cdnurl;return void 0!==e&&!0!==e&&0!==n.indexOf("/")||(t=i.test(t)||n&&0===t.indexOf(n)?t:n+t),e&&!i.test(t)&&(e="string"==typeof e?e:location.origin,t=e+t),t},query:function(t,e){if(e||(e=window.location.href),!t)return"";t=t.replace(/[\[\]]/g,"\\$&");var i=new RegExp("[?&/]"+t+"([=/]([^&#/?]*)|&|#|$)"),n=i.exec(e);return n?n[2]?decodeURIComponent(n[2].replace(/\+/g," ")):"":null},open:function(i,o,s){o=s&&s.title?s.title:o||"",i=a.api.fixurl(i),i=i+(i.indexOf("?")>-1?"&":"?")+"dialog=1";var r=a.config.openArea!=e?a.config.openArea:[t(window).width()>800?"800px":"95%",t(window).height()>600?"600px":"95%"],l=s&&"function"==typeof s.success?s.success:t.noop;return s&&"function"==typeof s.success&&delete s.success,s=t.extend({type:2,title:o,shadeClose:!0,shade:!1,maxmin:!0,moveOut:!0,area:r,content:i,zIndex:n.zIndex,success:function(e,i){var o=this;t(e).data("callback",o.callback),n.setTop(e);try{var s=n.getChildFrame("html",i),r=s.find(".layer-footer");if(a.api.layerfooter(e,i,o),r.length>0){var c=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;if(c){var d=r[0],u=new c(function(t){a.api.layerfooter(e,i,o),t.forEach(function(t){})}),h={attributes:!0,childList:!0,characterData:!0,subtree:!0};u.observe(d,h)}}}catch(t){}t(e).height()>t(window).height()&&n.style(i,{top:0,height:t(window).height()}),l.call(this,e,i)}},s||{}),(t(window).width()<480||/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&top.$(".tab-pane.active").length>0)&&(top.$(".tab-pane.active").length>0?(s.area=[top.$(".tab-pane.active").width()+"px",top.$(".tab-pane.active").height()+"px"],s.offset=[top.$(".tab-pane.active").scrollTop()+"px","0px"]):(s.area=[t(window).width()+"px",t(window).height()+"px"],s.offset=["0px","0px"])),n.open(s)},close:function(t){var i=parent.Layer.getFrameIndex(window.name),n=parent.$("#layui-layer"+i).data("callback");parent.Layer.close(i),"function"==typeof n&&n.call(e,t)},layerfooter:function(e,i,o){var a=n.getChildFrame("html",i),s=a.find(".layer-footer");if(s.length>0){t(".layui-layer-footer",e).remove();var r=t("<div />").addClass("layui-layer-btn layui-layer-footer");r.html(s.html()),0===t(".row",r).length&&t(">",r).wrapAll("<div class='row'></div>"),r.insertAfter(e.find(".layui-layer-content")),r.on("click",".btn",function(){if(!t(this).hasClass("disabled")&&!t(this).parent().hasClass("disabled")){var e=r.find(".btn").index(this);t(".btn:eq("+e+")",s).trigger("click")}});var l=e.find(".layui-layer-title").outerHeight()||0,c=e.find(".layui-layer-btn").outerHeight()||0;t("iframe",e).height(e.height()-l-c)}if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var l=e.find(".layui-layer-title").outerHeight()||0,c=e.find(".layui-layer-btn").outerHeight()||0;t("iframe",e).parent().css("height",e.height()-l-c),t("iframe",e).css("height","100%")}},success:function(e,i){var o="function"==typeof e;return o&&(i=e),n.msg(__("Operation completed"),t.extend({offset:0,icon:1},o?{}:e),i)},error:function(e,i){var o="function"==typeof e;return o&&(i=e),n.msg(__("Operation failed"),t.extend({offset:0,icon:2},o?{}:e),i)},msg:function(t,e){var i="function"==typeof e?e:function(){void 0!==e&&e&&(location.href=e)};n.msg(t,{time:2e3},i)},escape:function(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t},toastr:i,layer:n},lang:function(){var t=arguments,e=t[0],i=1;if(e=e.toLowerCase(),void 0!==o&&void 0!==o[e]){if("object"==typeof o[e])return o[e];e=o[e]}else if(e.indexOf("."),1)e=t[0];else{for(var n=e.split("."),a=o[n[0]],i=1;i<n.length&&"object"==typeof(a=void 0!==a[n[i]]?a[n[i]]:"");i++);if("object"==typeof a)return a;e=a}return e.replace(/%((%)|s|d)/g,function(e){var n=null;if(e[2])n=e[2];else{switch(n=t[i],e){case"%d":n=parseFloat(n),isNaN(n)&&(n=0)}i++}return n})},init:function(){t.fn.extend({size:function(){return t(this).length}}),t.ajaxSetup({beforeSend:function(t,e){e.url=a.api.fixurl(e.url)}}),n.config({skin:"layui-layer-fast"}),t(window).keyup(function(e){if(27==e.keyCode&&t(".layui-layer").length>0){var i=0;t(".layui-layer").each(function(){i=Math.max(i,parseInt(t(this).attr("times")))}),i&&n.close(i)}}),i.options=a.config.toastr}};return window.Layer=n,window.Toastr=i,window.__=a.lang,window.Fast=a,a.init(),a}),function(){function t(t){return t.replace(b,"").replace(x,",").replace(w,"").replace(k,"").replace(_,"").split(C)}function e(t){return"'"+t.replace(/('|\\)/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n")+"'"}function i(i,n){function o(t){return h+=t.split(/\n/).length-1,d&&(t=t.replace(/\s+/g," ").replace(/<!--[\w\W]*?-->/g,"")),t&&(t=y[1]+e(t)+y[2]+"\n"),t}function a(e){var i=h;if(c?e=c(e,n):s&&(e=e.replace(/\n/g,function(){return"$line="+ ++h+";"})),0===e.indexOf("=")){var o=u&&!/^=[=#]/.test(e);if(e=e.replace(/^=[=#]?|[\s;]*$/g,""),o){var a=e.replace(/\s*\([^\)]+\)/,"");p[a]||/^(include|print)$/.test(a)||(e="$escape("+e+")")}else e="$string("+e+")";e=y[1]+e+y[2]}return s&&(e="$line="+i+";"+e),v(t(e),function(t){if(t&&!m[t]){var e;e="print"===t?x:"include"===t?w:p[t]?"$utils."+t:f[t]?"$helpers."+t:"$data."+t,k+=t+"="+e+",",m[t]=!0}}),e+"\n"}var s=n.debug,r=n.openTag,l=n.closeTag,c=n.parser,d=n.compress,u=n.escape,h=1,m={$data:1,$filename:1,$utils:1,$helpers:1,$out:1,$line:1},g="".trim,y=g?["$out='';","$out+=",";","$out"]:["$out=[];","$out.push(",");","$out.join('')"],b=g?"$out+=text;return $out;":"$out.push(text);",x="function(){var text=''.concat.apply('',arguments);"+b+"}",w="function(filename,data){data=data||$data;var text=$utils.$include(filename,data,$filename);"+b+"}",k="'use strict';var $utils=this,$helpers=$utils.$helpers,"+(s?"$line=0,":""),_=y[0],C="return new String("+y[3]+");";v(i.split(r),function(t){t=t.split(l);var e=t[0],i=t[1];1===t.length?_+=o(e):(_+=a(e),i&&(_+=o(i)))});var S=k+_+C;s&&(S="try{"+S+"}catch(e){throw {filename:$filename,name:'Render Error',message:e.message,line:$line,source:"+e(i)+".split(/\\n/)[$line-1].replace(/^\\s+/,'')};}");try{var T=new Function("$data","$filename",S);return T.prototype=p,T}catch(t){throw t.temp="function anonymous($data,$filename) {"+S+"}",t}}var n=function(t,e){return"string"==typeof e?g(e,{filename:t}):s(t,e)};n.version="3.0.0",n.config=function(t,e){o[t]=e};var o=n.defaults={openTag:"<%",closeTag:"%>",escape:!0,cache:!0,compress:!1,parser:null},a=n.cache={};n.render=function(t,e){return g(t)(e)};var s=n.renderFile=function(t,e){var i=n.get(t)||m({filename:t,name:"Render Error",message:"Template not found"});return e?i(e):i};n.get=function(t){var e;if(a[t])e=a[t];else if("object"==typeof document){var i=document.getElementById(t);if(i){var n=(i.value||i.innerHTML).replace(/^\s*|\s*$/g,"");e=g(n,{filename:t})}}return e};var r=function(t,e){return"string"!=typeof t&&(e=typeof t,"number"===e?t+="":t="function"===e?r(t.call(t)):""),t},l={"<":"&#60;",">":"&#62;",'"':"&#34;","'":"&#39;","&":"&#38;"},c=function(t){return l[t]},d=function(t){return r(t).replace(/&(?![\w#]+;)|[<>"']/g,c)},u=Array.isArray||function(t){return"[object Array]"==={}.toString.call(t)},h=function(t,e){var i,n;if(u(t))for(i=0,n=t.length;i<n;i++)e.call(t,t[i],i,t);else for(i in t)e.call(t,t[i],i)},p=n.utils={$helpers:{},$include:s,$string:r,$escape:d,$each:h};n.helper=function(t,e){f[t]=e};var f=n.helpers=p.$helpers;n.onerror=function(t){var e="Template Error\n\n";for(var i in t)e+="<"+i+">\n"+t[i]+"\n\n";"object"==typeof console&&console.error(e)};var m=function(t){return n.onerror(t),function(){return"{Template Error}"}},g=n.compile=function(t,e){function n(i){try{return new l(i,r)+""}catch(n){return e.debug?m(n)():(e.debug=!0,g(t,e)(i))}}e=e||{};for(var s in o)void 0===e[s]&&(e[s]=o[s]);var r=e.filename;try{var l=i(t,e)}catch(t){return t.filename=r||"anonymous",t.name="Syntax Error",m(t)}return n.prototype=l.prototype,n.toString=function(){return l.toString()},r&&e.cache&&(a[r]=n),n},v=p.$each,y="break,case,catch,continue,debugger,default,delete,do,else,false,finally,for,function,if,in,instanceof,new,null,return,switch,this,throw,true,try,typeof,var,void,while,with,abstract,boolean,byte,char,class,const,double,enum,export,extends,final,float,goto,implements,import,int,interface,long,native,package,private,protected,public,short,static,super,synchronized,throws,transient,volatile,arguments,let,yield,undefined",b=/\/\*[\w\W]*?\*\/|\/\/[^\n]*\n|\/\/[^\n]*$|"(?:[^"\\]|\\[\w\W])*"|'(?:[^'\\]|\\[\w\W])*'|\s*\.\s*[$\w\.]+/g,x=/[^\w$]+/g,w=new RegExp(["\\b"+y.replace(/,/g,"\\b|\\b")+"\\b"].join("|"),"g"),k=/^\d[^,]*|,\d[^,]*/g,_=/^,+|,+$/g,C=/^$|,+/;"object"==typeof exports&&"undefined"!=typeof module?module.exports=n:"function"==typeof define?define("template",[],function(){return n}):this.template=n}(),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("moment/moment",e):t.moment=e()}(this,function(){"use strict";function t(){return to.apply(null,arguments)}function e(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function i(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function o(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;var e;for(e in t)if(n(t,e))return!1;return!0}function a(t){return void 0===t}function s(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function r(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function l(t,e){var i,n=[],o=t.length;for(i=0;i<o;++i)n.push(e(t[i],i));return n}function c(t,e){for(var i in e)n(e,i)&&(t[i]=e[i]);return n(e,"toString")&&(t.toString=e.toString),n(e,"valueOf")&&(t.valueOf=e.valueOf),t}function d(t,e,i,n){return Se(t,e,i,n,!0).utc()}function u(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function h(t){return null==t._pf&&(t._pf=u()),t._pf}function p(t){if(null==t._isValid){var e=h(t),i=eo.call(e.parsedDateParts,function(t){return null!=t}),n=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidEra&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&i);if(t._strict&&(n=n&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return n;t._isValid=n}return t._isValid}function f(t){var e=d(NaN);return null!=t?c(h(e),t):h(e).userInvalidated=!0,e}function m(t,e){var i,n,o,s=io.length;if(a(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),a(e._i)||(t._i=e._i),a(e._f)||(t._f=e._f),a(e._l)||(t._l=e._l),a(e._strict)||(t._strict=e._strict),a(e._tzm)||(t._tzm=e._tzm),a(e._isUTC)||(t._isUTC=e._isUTC),a(e._offset)||(t._offset=e._offset),a(e._pf)||(t._pf=h(e)),a(e._locale)||(t._locale=e._locale),s>0)for(i=0;i<s;i++)n=io[i],o=e[n],a(o)||(t[n]=o);return t}function g(e){m(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===no&&(no=!0,t.updateOffset(this),no=!1)}function v(t){return t instanceof g||null!=t&&null!=t._isAMomentObject}function y(e){!1===t.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function b(e,i){var o=!0;return c(function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),o){var a,s,r,l=[],c=arguments.length;for(s=0;s<c;s++){if(a="","object"==typeof arguments[s]){a+="\n["+s+"] ";for(r in arguments[0])n(arguments[0],r)&&(a+=r+": "+arguments[0][r]+", ");a=a.slice(0,-2)}else a=arguments[s];l.push(a)}y(e+"\nArguments: "+Array.prototype.slice.call(l).join("")+"\n"+(new Error).stack),o=!1}return i.apply(this,arguments)},i)}function x(e,i){null!=t.deprecationHandler&&t.deprecationHandler(e,i),oo[e]||(y(i),oo[e]=!0)}function w(t){return"undefined"!=typeof Function&&t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function k(t){var e,i;for(i in t)n(t,i)&&(e=t[i],w(e)?this[i]=e:this["_"+i]=e);this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function _(t,e){var o,a=c({},t);for(o in e)n(e,o)&&(i(t[o])&&i(e[o])?(a[o]={},c(a[o],t[o]),c(a[o],e[o])):null!=e[o]?a[o]=e[o]:delete a[o]);for(o in t)n(t,o)&&!n(e,o)&&i(t[o])&&(a[o]=c({},a[o]));return a}function C(t){null!=t&&this.set(t)}function S(t,e,i){var n=this._calendar[t]||this._calendar.sameElse;return w(n)?n.call(e,i):n}function T(t,e,i){var n=""+Math.abs(t),o=e-n.length;return(t>=0?i?"+":"":"-")+Math.pow(10,Math.max(0,o)).toString().substr(1)+n}function D(t,e,i,n){var o=n;"string"==typeof n&&(o=function(){return this[n]()}),t&&(ho[t]=o),e&&(ho[e[0]]=function(){return T(o.apply(this,arguments),e[1],e[2])}),i&&(ho[i]=function(){return this.localeData().ordinal(o.apply(this,arguments),t)})}function $(t){return t.match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"")}function E(t){var e,i,n=t.match(lo);for(e=0,i=n.length;e<i;e++)ho[n[e]]?n[e]=ho[n[e]]:n[e]=$(n[e]);return function(e){var o,a="";for(o=0;o<i;o++)a+=w(n[o])?n[o].call(e,t):n[o];return a}}function A(t,e){return t.isValid()?(e=O(e,t.localeData()),uo[e]=uo[e]||E(e),uo[e](t)):t.localeData().invalidDate()}function O(t,e){function i(t){return e.longDateFormat(t)||t}var n=5;for(co.lastIndex=0;n>=0&&co.test(t);)t=t.replace(co,i),co.lastIndex=0,n-=1;return t}function F(t){var e=this._longDateFormat[t],i=this._longDateFormat[t.toUpperCase()];return e||!i?e:(this._longDateFormat[t]=i.match(lo).map(function(t){return"MMMM"===t||"MM"===t||"DD"===t||"dddd"===t?t.slice(1):t}).join(""),this._longDateFormat[t])}function P(){return this._invalidDate}function M(t){return this._ordinal.replace("%d",t)}function L(t,e,i,n){var o=this._relativeTime[i];return w(o)?o(t,e,i,n):o.replace(/%d/i,t)}function I(t,e){var i=this._relativeTime[t>0?"future":"past"];return w(i)?i(e):i.replace(/%s/i,e)}function N(t,e){var i=t.toLowerCase();go[i]=go[i+"s"]=go[e]=t}function R(t){return"string"==typeof t?go[t]||go[t.toLowerCase()]:void 0}function j(t){var e,i,o={};for(i in t)n(t,i)&&(e=R(i))&&(o[e]=t[i]);return o}function Y(t,e){vo[t]=e}function H(t){var e,i=[];for(e in t)n(t,e)&&i.push({unit:e,priority:vo[e]});return i.sort(function(t,e){return t.priority-e.priority}),i}function z(t){return t%4==0&&t%100!=0||t%400==0}function B(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function U(t){var e=+t,i=0;return 0!==e&&isFinite(e)&&(i=B(e)),i}function W(e,i){return function(n){return null!=n?(V(this,e,n),t.updateOffset(this,i),this):q(this,e)}}function q(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function V(t,e,i){t.isValid()&&!isNaN(i)&&("FullYear"===e&&z(t.year())&&1===t.month()&&29===t.date()?(i=U(i),t._d["set"+(t._isUTC?"UTC":"")+e](i,t.month(),ot(i,t.month()))):t._d["set"+(t._isUTC?"UTC":"")+e](i))}function G(t){return t=R(t),w(this[t])?this[t]():this}function X(t,e){if("object"==typeof t){t=j(t);var i,n=H(t),o=n.length;for(i=0;i<o;i++)this[n[i].unit](t[n[i].unit])}else if(t=R(t),w(this[t]))return this[t](e);return this}function Q(t,e,i){so[t]=w(e)?e:function(t,n){return t&&i?i:e}}function K(t,e){return n(so,t)?so[t](e._strict,e._locale):new RegExp(Z(t))}function Z(t){return J(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,i,n,o){return e||i||n||o}))}function J(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function tt(t,e){var i,n,o=e;for("string"==typeof t&&(t=[t]),s(e)&&(o=function(t,i){i[e]=U(t)}),n=t.length,i=0;i<n;i++)Io[t[i]]=o}function et(t,e){tt(t,function(t,i,n,o){n._w=n._w||{},e(t,n._w,n,o)})}function it(t,e,i){null!=e&&n(Io,t)&&Io[t](e,i._a,i,t)}function nt(t,e){return(t%e+e)%e}function ot(t,e){if(isNaN(t)||isNaN(e))return NaN;var i=nt(e,12);return t+=(e-i)/12,1===i?z(t)?29:28:31-i%7%2}function at(t,i){return t?e(this._months)?this._months[t.month()]:this._months[(this._months.isFormat||Go).test(i)?"format":"standalone"][t.month()]:e(this._months)?this._months:this._months.standalone}function st(t,i){return t?e(this._monthsShort)?this._monthsShort[t.month()]:this._monthsShort[Go.test(i)?"format":"standalone"][t.month()]:e(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function rt(t,e,i){var n,o,a,s=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],n=0;n<12;++n)a=d([2e3,n]),this._shortMonthsParse[n]=this.monthsShort(a,"").toLocaleLowerCase(),this._longMonthsParse[n]=this.months(a,"").toLocaleLowerCase();return i?"MMM"===e?(o=Lo.call(this._shortMonthsParse,s),-1!==o?o:null):(o=Lo.call(this._longMonthsParse,s),-1!==o?o:null):"MMM"===e?-1!==(o=Lo.call(this._shortMonthsParse,s))?o:(o=Lo.call(this._longMonthsParse,s),-1!==o?o:null):-1!==(o=Lo.call(this._longMonthsParse,s))?o:(o=Lo.call(this._shortMonthsParse,s),-1!==o?o:null)}function lt(t,e,i){var n,o,a;if(this._monthsParseExact)return rt.call(this,t,e,i);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),n=0;n<12;n++){if(o=d([2e3,n]),i&&!this._longMonthsParse[n]&&(this._longMonthsParse[n]=new RegExp("^"+this.months(o,"").replace(".","")+"$","i"),this._shortMonthsParse[n]=new RegExp("^"+this.monthsShort(o,"").replace(".","")+"$","i")),i||this._monthsParse[n]||(a="^"+this.months(o,"")+"|^"+this.monthsShort(o,""),this._monthsParse[n]=new RegExp(a.replace(".",""),"i")),i&&"MMMM"===e&&this._longMonthsParse[n].test(t))return n;if(i&&"MMM"===e&&this._shortMonthsParse[n].test(t))return n;if(!i&&this._monthsParse[n].test(t))return n}}function ct(t,e){var i;if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=U(e);else if(e=t.localeData().monthsParse(e),!s(e))return t;return i=Math.min(t.date(),ot(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,i),t}function dt(e){return null!=e?(ct(this,e),t.updateOffset(this,!0),this):q(this,"Month")}function ut(){return ot(this.year(),this.month())}function ht(t){return this._monthsParseExact?(n(this,"_monthsRegex")||ft.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(n(this,"_monthsShortRegex")||(this._monthsShortRegex=Xo),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)}function pt(t){return this._monthsParseExact?(n(this,"_monthsRegex")||ft.call(this),t?this._monthsStrictRegex:this._monthsRegex):(n(this,"_monthsRegex")||(this._monthsRegex=Qo),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)}function ft(){function t(t,e){return e.length-t.length}var e,i,n=[],o=[],a=[];for(e=0;e<12;e++)i=d([2e3,e]),n.push(this.monthsShort(i,"")),o.push(this.months(i,"")),a.push(this.months(i,"")),a.push(this.monthsShort(i,""));for(n.sort(t),o.sort(t),a.sort(t),e=0;e<12;e++)n[e]=J(n[e]),o[e]=J(o[e]);for(e=0;e<24;e++)a[e]=J(a[e]);this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function mt(t){return z(t)?366:365}function gt(){return z(this.year())}function vt(t,e,i,n,o,a,s){var r;return t<100&&t>=0?(r=new Date(t+400,e,i,n,o,a,s),isFinite(r.getFullYear())&&r.setFullYear(t)):r=new Date(t,e,i,n,o,a,s),r}function yt(t){var e,i;return t<100&&t>=0?(i=Array.prototype.slice.call(arguments),i[0]=t+400,e=new Date(Date.UTC.apply(null,i)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function bt(t,e,i){var n=7+e-i;return-(7+yt(t,0,n).getUTCDay()-e)%7+n-1}function xt(t,e,i,n,o){var a,s,r=(7+i-n)%7,l=bt(t,n,o),c=1+7*(e-1)+r+l;return c<=0?(a=t-1,s=mt(a)+c):c>mt(t)?(a=t+1,s=c-mt(t)):(a=t,s=c),{year:a,dayOfYear:s}}function wt(t,e,i){var n,o,a=bt(t.year(),e,i),s=Math.floor((t.dayOfYear()-a-1)/7)+1;return s<1?(o=t.year()-1,n=s+kt(o,e,i)):s>kt(t.year(),e,i)?(n=s-kt(t.year(),e,i),o=t.year()+1):(o=t.year(),n=s),{week:n,year:o}}function kt(t,e,i){var n=bt(t,e,i),o=bt(t+1,e,i);return(mt(t)-n+o)/7}function _t(t){return wt(t,this._week.dow,this._week.doy).week}function Ct(){return this._week.dow}function St(){return this._week.doy}function Tt(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")}function Dt(t){var e=wt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")}function $t(t,e){return"string"!=typeof t?t:isNaN(t)?(t=e.weekdaysParse(t),"number"==typeof t?t:null):parseInt(t,10)}function Et(t,e){return"string"==typeof t?e.weekdaysParse(t)%7||7:isNaN(t)?null:t}function At(t,e){return t.slice(e,7).concat(t.slice(0,e))}function Ot(t,i){var n=e(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(i)?"format":"standalone"];return!0===t?At(n,this._week.dow):t?n[t.day()]:n}function Ft(t){return!0===t?At(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort}function Pt(t){return!0===t?At(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin}function Mt(t,e,i){var n,o,a,s=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],n=0;n<7;++n)a=d([2e3,1]).day(n),this._minWeekdaysParse[n]=this.weekdaysMin(a,"").toLocaleLowerCase(),this._shortWeekdaysParse[n]=this.weekdaysShort(a,"").toLocaleLowerCase(),this._weekdaysParse[n]=this.weekdays(a,"").toLocaleLowerCase();return i?"dddd"===e?(o=Lo.call(this._weekdaysParse,s),-1!==o?o:null):"ddd"===e?(o=Lo.call(this._shortWeekdaysParse,s),-1!==o?o:null):(o=Lo.call(this._minWeekdaysParse,s),-1!==o?o:null):"dddd"===e?-1!==(o=Lo.call(this._weekdaysParse,s))?o:-1!==(o=Lo.call(this._shortWeekdaysParse,s))?o:(o=Lo.call(this._minWeekdaysParse,s),-1!==o?o:null):"ddd"===e?-1!==(o=Lo.call(this._shortWeekdaysParse,s))?o:-1!==(o=Lo.call(this._weekdaysParse,s))?o:(o=Lo.call(this._minWeekdaysParse,s),-1!==o?o:null):-1!==(o=Lo.call(this._minWeekdaysParse,s))?o:-1!==(o=Lo.call(this._weekdaysParse,s))?o:(o=Lo.call(this._shortWeekdaysParse,s),-1!==o?o:null)}function Lt(t,e,i){var n,o,a;if(this._weekdaysParseExact)return Mt.call(this,t,e,i);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),n=0;n<7;n++){if(o=d([2e3,1]).day(n),i&&!this._fullWeekdaysParse[n]&&(this._fullWeekdaysParse[n]=new RegExp("^"+this.weekdays(o,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[n]=new RegExp("^"+this.weekdaysShort(o,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[n]=new RegExp("^"+this.weekdaysMin(o,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[n]||(a="^"+this.weekdays(o,"")+"|^"+this.weekdaysShort(o,"")+"|^"+this.weekdaysMin(o,""),this._weekdaysParse[n]=new RegExp(a.replace(".",""),"i")),i&&"dddd"===e&&this._fullWeekdaysParse[n].test(t))return n;if(i&&"ddd"===e&&this._shortWeekdaysParse[n].test(t))return n;if(i&&"dd"===e&&this._minWeekdaysParse[n].test(t))return n;if(!i&&this._weekdaysParse[n].test(t))return n}}function It(t){if(!this.isValid())return null!=t?this:NaN;var e=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(t=$t(t,this.localeData()),this.add(t-e,"d")):e}function Nt(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")}function Rt(t){if(!this.isValid())return null!=t?this:NaN;if(null!=t){var e=Et(t,this.localeData());return this.day(this.day()%7?e:e-7)}return this.day()||7}function jt(t){return this._weekdaysParseExact?(n(this,"_weekdaysRegex")||zt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(n(this,"_weekdaysRegex")||(this._weekdaysRegex=ia),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)}function Yt(t){return this._weekdaysParseExact?(n(this,"_weekdaysRegex")||zt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(n(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=na),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function Ht(t){return this._weekdaysParseExact?(n(this,"_weekdaysRegex")||zt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(n(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=oa),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function zt(){function t(t,e){return e.length-t.length}var e,i,n,o,a,s=[],r=[],l=[],c=[];for(e=0;e<7;e++)i=d([2e3,1]).day(e),n=J(this.weekdaysMin(i,"")),o=J(this.weekdaysShort(i,"")),a=J(this.weekdays(i,"")),s.push(n),r.push(o),l.push(a),c.push(n),c.push(o),c.push(a);s.sort(t),r.sort(t),l.sort(t),c.sort(t),this._weekdaysRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Bt(){return this.hours()%12||12}function Ut(){return this.hours()||24}function Wt(t,e){D(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function qt(t,e){return e._meridiemParse}function Vt(t){return"p"===(t+"").toLowerCase().charAt(0)}function Gt(t,e,i){return t>11?i?"pm":"PM":i?"am":"AM"}function Xt(t,e){var i,n=Math.min(t.length,e.length);for(i=0;i<n;i+=1)if(t[i]!==e[i])return i;return n}function Qt(t){return t?t.toLowerCase().replace("_","-"):t}function Kt(t){for(var e,i,n,o,a=0;a<t.length;){for(o=Qt(t[a]).split("-"),e=o.length,i=Qt(t[a+1]),i=i?i.split("-"):null;e>0;){if(n=Jt(o.slice(0,e).join("-")))return n;if(i&&i.length>=e&&Xt(o,i)>=e-1)break;e--}a++}return aa}function Zt(t){return null!=t.match("^[^/\\\\]*$")}function Jt(t){var e,i=null;if(void 0===ca[t]&&"undefined"!=typeof module&&module&&module.exports&&Zt(t))try{i=aa._abbr,e=require,e("./locale/"+t),te(i)}catch(e){ca[t]=null}return ca[t]}function te(t,e){var i;return t&&(i=a(e)?ne(t):ee(t,e),i?aa=i:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),aa._abbr}function ee(t,e){if(null!==e){var i,n=la;if(e.abbr=t,null!=ca[t])x("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),n=ca[t]._config;else if(null!=e.parentLocale)if(null!=ca[e.parentLocale])n=ca[e.parentLocale]._config;else{if(null==(i=Jt(e.parentLocale)))return da[e.parentLocale]||(da[e.parentLocale]=[]),da[e.parentLocale].push({name:t,config:e}),null;n=i._config}return ca[t]=new C(_(n,e)),da[t]&&da[t].forEach(function(t){ee(t.name,t.config)}),te(t),ca[t]}return delete ca[t],null}function ie(t,e){if(null!=e){var i,n,o=la;null!=ca[t]&&null!=ca[t].parentLocale?ca[t].set(_(ca[t]._config,e)):(n=Jt(t),null!=n&&(o=n._config),e=_(o,e),null==n&&(e.abbr=t),i=new C(e),i.parentLocale=ca[t],ca[t]=i),te(t)}else null!=ca[t]&&(null!=ca[t].parentLocale?(ca[t]=ca[t].parentLocale,t===te()&&te(t)):null!=ca[t]&&delete ca[t]);return ca[t]}function ne(t){var i;if(t&&t._locale&&t._locale._abbr&&(t=t._locale._abbr),!t)return aa;if(!e(t)){if(i=Jt(t))return i;t=[t]}return Kt(t)}function oe(){return ao(ca)}function ae(t){var e,i=t._a;return i&&-2===h(t).overflow&&(e=i[Ro]<0||i[Ro]>11?Ro:i[jo]<1||i[jo]>ot(i[No],i[Ro])?jo:i[Yo]<0||i[Yo]>24||24===i[Yo]&&(0!==i[Ho]||0!==i[zo]||0!==i[Bo])?Yo:i[Ho]<0||i[Ho]>59?Ho:i[zo]<0||i[zo]>59?zo:i[Bo]<0||i[Bo]>999?Bo:-1,h(t)._overflowDayOfYear&&(e<No||e>jo)&&(e=jo),h(t)._overflowWeeks&&-1===e&&(e=Uo),h(t)._overflowWeekday&&-1===e&&(e=Wo),h(t).overflow=e),t}function se(t){var e,i,n,o,a,s,r=t._i,l=ua.exec(r)||ha.exec(r),c=fa.length,d=ma.length;if(l){for(h(t).iso=!0,e=0,i=c;e<i;e++)if(fa[e][1].exec(l[1])){o=fa[e][0],n=!1!==fa[e][2];break}if(null==o)return void(t._isValid=!1);if(l[3]){for(e=0,i=d;e<i;e++)if(ma[e][1].exec(l[3])){a=(l[2]||" ")+ma[e][0];break}if(null==a)return void(t._isValid=!1)}if(!n&&null!=a)return void(t._isValid=!1);if(l[4]){if(!pa.exec(l[4]))return void(t._isValid=!1);s="Z"}t._f=o+(a||"")+(s||""),ye(t)}else t._isValid=!1}function re(t,e,i,n,o,a){var s=[le(t),Vo.indexOf(e),parseInt(i,10),parseInt(n,10),parseInt(o,10)];return a&&s.push(parseInt(a,10)),s}function le(t){var e=parseInt(t,10);return e<=49?2e3+e:e<=999?1900+e:e}function ce(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function de(t,e,i){if(t){
if(ta.indexOf(t)!==new Date(e[0],e[1],e[2]).getDay())return h(i).weekdayMismatch=!0,i._isValid=!1,!1}return!0}function ue(t,e,i){if(t)return ya[t];if(e)return 0;var n=parseInt(i,10),o=n%100;return(n-o)/100*60+o}function he(t){var e,i=va.exec(ce(t._i));if(i){if(e=re(i[4],i[3],i[2],i[5],i[6],i[7]),!de(i[1],e,t))return;t._a=e,t._tzm=ue(i[8],i[9],i[10]),t._d=yt.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),h(t).rfc2822=!0}else t._isValid=!1}function pe(e){var i=ga.exec(e._i);if(null!==i)return void(e._d=new Date(+i[1]));se(e),!1===e._isValid&&(delete e._isValid,he(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:t.createFromInputFallback(e)))}function fe(t,e,i){return null!=t?t:null!=e?e:i}function me(e){var i=new Date(t.now());return e._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()]}function ge(t){var e,i,n,o,a,s=[];if(!t._d){for(n=me(t),t._w&&null==t._a[jo]&&null==t._a[Ro]&&ve(t),null!=t._dayOfYear&&(a=fe(t._a[No],n[No]),(t._dayOfYear>mt(a)||0===t._dayOfYear)&&(h(t)._overflowDayOfYear=!0),i=yt(a,0,t._dayOfYear),t._a[Ro]=i.getUTCMonth(),t._a[jo]=i.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=s[e]=n[e];for(;e<7;e++)t._a[e]=s[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[Yo]&&0===t._a[Ho]&&0===t._a[zo]&&0===t._a[Bo]&&(t._nextDay=!0,t._a[Yo]=0),t._d=(t._useUTC?yt:vt).apply(null,s),o=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[Yo]=24),t._w&&void 0!==t._w.d&&t._w.d!==o&&(h(t).weekdayMismatch=!0)}}function ve(t){var e,i,n,o,a,s,r,l,c;e=t._w,null!=e.GG||null!=e.W||null!=e.E?(a=1,s=4,i=fe(e.GG,t._a[No],wt(Te(),1,4).year),n=fe(e.W,1),((o=fe(e.E,1))<1||o>7)&&(l=!0)):(a=t._locale._week.dow,s=t._locale._week.doy,c=wt(Te(),a,s),i=fe(e.gg,t._a[No],c.year),n=fe(e.w,c.week),null!=e.d?((o=e.d)<0||o>6)&&(l=!0):null!=e.e?(o=e.e+a,(e.e<0||e.e>6)&&(l=!0)):o=a),n<1||n>kt(i,a,s)?h(t)._overflowWeeks=!0:null!=l?h(t)._overflowWeekday=!0:(r=xt(i,n,o,a,s),t._a[No]=r.year,t._dayOfYear=r.dayOfYear)}function ye(e){if(e._f===t.ISO_8601)return void se(e);if(e._f===t.RFC_2822)return void he(e);e._a=[],h(e).empty=!0;var i,n,o,a,s,r,l,c=""+e._i,d=c.length,u=0;for(o=O(e._f,e._locale).match(lo)||[],l=o.length,i=0;i<l;i++)a=o[i],n=(c.match(K(a,e))||[])[0],n&&(s=c.substr(0,c.indexOf(n)),s.length>0&&h(e).unusedInput.push(s),c=c.slice(c.indexOf(n)+n.length),u+=n.length),ho[a]?(n?h(e).empty=!1:h(e).unusedTokens.push(a),it(a,n,e)):e._strict&&!n&&h(e).unusedTokens.push(a);h(e).charsLeftOver=d-u,c.length>0&&h(e).unusedInput.push(c),e._a[Yo]<=12&&!0===h(e).bigHour&&e._a[Yo]>0&&(h(e).bigHour=void 0),h(e).parsedDateParts=e._a.slice(0),h(e).meridiem=e._meridiem,e._a[Yo]=be(e._locale,e._a[Yo],e._meridiem),r=h(e).era,null!==r&&(e._a[No]=e._locale.erasConvertYear(r,e._a[No])),ge(e),ae(e)}function be(t,e,i){var n;return null==i?e:null!=t.meridiemHour?t.meridiemHour(e,i):null!=t.isPM?(n=t.isPM(i),n&&e<12&&(e+=12),n||12!==e||(e=0),e):e}function xe(t){var e,i,n,o,a,s,r=!1,l=t._f.length;if(0===l)return h(t).invalidFormat=!0,void(t._d=new Date(NaN));for(o=0;o<l;o++)a=0,s=!1,e=m({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[o],ye(e),p(e)&&(s=!0),a+=h(e).charsLeftOver,a+=10*h(e).unusedTokens.length,h(e).score=a,r?a<n&&(n=a,i=e):(null==n||a<n||s)&&(n=a,i=e,s&&(r=!0));c(t,i||e)}function we(t){if(!t._d){var e=j(t._i),i=void 0===e.day?e.date:e.day;t._a=l([e.year,e.month,i,e.hour,e.minute,e.second,e.millisecond],function(t){return t&&parseInt(t,10)}),ge(t)}}function ke(t){var e=new g(ae(_e(t)));return e._nextDay&&(e.add(1,"d"),e._nextDay=void 0),e}function _e(t){var i=t._i,n=t._f;return t._locale=t._locale||ne(t._l),null===i||void 0===n&&""===i?f({nullInput:!0}):("string"==typeof i&&(t._i=i=t._locale.preparse(i)),v(i)?new g(ae(i)):(r(i)?t._d=i:e(n)?xe(t):n?ye(t):Ce(t),p(t)||(t._d=null),t))}function Ce(n){var o=n._i;a(o)?n._d=new Date(t.now()):r(o)?n._d=new Date(o.valueOf()):"string"==typeof o?pe(n):e(o)?(n._a=l(o.slice(0),function(t){return parseInt(t,10)}),ge(n)):i(o)?we(n):s(o)?n._d=new Date(o):t.createFromInputFallback(n)}function Se(t,n,a,s,r){var l={};return!0!==n&&!1!==n||(s=n,n=void 0),!0!==a&&!1!==a||(s=a,a=void 0),(i(t)&&o(t)||e(t)&&0===t.length)&&(t=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=r,l._l=a,l._i=t,l._f=n,l._strict=s,ke(l)}function Te(t,e,i,n){return Se(t,e,i,n,!1)}function De(t,i){var n,o;if(1===i.length&&e(i[0])&&(i=i[0]),!i.length)return Te();for(n=i[0],o=1;o<i.length;++o)i[o].isValid()&&!i[o][t](n)||(n=i[o]);return n}function $e(){return De("isBefore",[].slice.call(arguments,0))}function Ee(){return De("isAfter",[].slice.call(arguments,0))}function Ae(t){var e,i,o=!1,a=ka.length;for(e in t)if(n(t,e)&&(-1===Lo.call(ka,e)||null!=t[e]&&isNaN(t[e])))return!1;for(i=0;i<a;++i)if(t[ka[i]]){if(o)return!1;parseFloat(t[ka[i]])!==U(t[ka[i]])&&(o=!0)}return!0}function Oe(){return this._isValid}function Fe(){return Ze(NaN)}function Pe(t){var e=j(t),i=e.year||0,n=e.quarter||0,o=e.month||0,a=e.week||e.isoWeek||0,s=e.day||0,r=e.hour||0,l=e.minute||0,c=e.second||0,d=e.millisecond||0;this._isValid=Ae(e),this._milliseconds=+d+1e3*c+6e4*l+1e3*r*60*60,this._days=+s+7*a,this._months=+o+3*n+12*i,this._data={},this._locale=ne(),this._bubble()}function Me(t){return t instanceof Pe}function Le(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Ie(t,e,i){var n,o=Math.min(t.length,e.length),a=Math.abs(t.length-e.length),s=0;for(n=0;n<o;n++)(i&&t[n]!==e[n]||!i&&U(t[n])!==U(e[n]))&&s++;return s+a}function Ne(t,e){D(t,0,0,function(){var t=this.utcOffset(),i="+";return t<0&&(t=-t,i="-"),i+T(~~(t/60),2)+e+T(~~t%60,2)})}function Re(t,e){var i,n,o,a=(e||"").match(t);return null===a?null:(i=a[a.length-1]||[],n=(i+"").match(_a)||["-",0,0],o=60*n[1]+U(n[2]),0===o?0:"+"===n[0]?o:-o)}function je(e,i){var n,o;return i._isUTC?(n=i.clone(),o=(v(e)||r(e)?e.valueOf():Te(e).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+o),t.updateOffset(n,!1),n):Te(e).local()}function Ye(t){return-Math.round(t._d.getTimezoneOffset())}function He(e,i,n){var o,a=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(null===(e=Re(Fo,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&i&&(o=Ye(this)),this._offset=e,this._isUTC=!0,null!=o&&this.add(o,"m"),a!==e&&(!i||this._changeInProgress?ni(this,Ze(e-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?a:Ye(this)}function ze(t,e){return null!=t?("string"!=typeof t&&(t=-t),this.utcOffset(t,e),this):-this.utcOffset()}function Be(t){return this.utcOffset(0,t)}function Ue(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Ye(this),"m")),this}function We(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var t=Re(Oo,this._i);null!=t?this.utcOffset(t):this.utcOffset(0,!0)}return this}function qe(t){return!!this.isValid()&&(t=t?Te(t).utcOffset():0,(this.utcOffset()-t)%60==0)}function Ve(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Ge(){if(!a(this._isDSTShifted))return this._isDSTShifted;var t,e={};return m(e,this),e=_e(e),e._a?(t=e._isUTC?d(e._a):Te(e._a),this._isDSTShifted=this.isValid()&&Ie(e._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function Xe(){return!!this.isValid()&&!this._isUTC}function Qe(){return!!this.isValid()&&this._isUTC}function Ke(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}function Ze(t,e){var i,o,a,r=t,l=null;return Me(t)?r={ms:t._milliseconds,d:t._days,M:t._months}:s(t)||!isNaN(+t)?(r={},e?r[e]=+t:r.milliseconds=+t):(l=Ca.exec(t))?(i="-"===l[1]?-1:1,r={y:0,d:U(l[jo])*i,h:U(l[Yo])*i,m:U(l[Ho])*i,s:U(l[zo])*i,ms:U(Le(1e3*l[Bo]))*i}):(l=Sa.exec(t))?(i="-"===l[1]?-1:1,r={y:Je(l[2],i),M:Je(l[3],i),w:Je(l[4],i),d:Je(l[5],i),h:Je(l[6],i),m:Je(l[7],i),s:Je(l[8],i)}):null==r?r={}:"object"==typeof r&&("from"in r||"to"in r)&&(a=ei(Te(r.from),Te(r.to)),r={},r.ms=a.milliseconds,r.M=a.months),o=new Pe(r),Me(t)&&n(t,"_locale")&&(o._locale=t._locale),Me(t)&&n(t,"_isValid")&&(o._isValid=t._isValid),o}function Je(t,e){var i=t&&parseFloat(t.replace(",","."));return(isNaN(i)?0:i)*e}function ti(t,e){var i={};return i.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(i.months,"M").isAfter(e)&&--i.months,i.milliseconds=+e-+t.clone().add(i.months,"M"),i}function ei(t,e){var i;return t.isValid()&&e.isValid()?(e=je(e,t),t.isBefore(e)?i=ti(t,e):(i=ti(e,t),i.milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0}}function ii(t,e){return function(i,n){var o,a;return null===n||isNaN(+n)||(x(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),a=i,i=n,n=a),o=Ze(i,n),ni(this,o,t),this}}function ni(e,i,n,o){var a=i._milliseconds,s=Le(i._days),r=Le(i._months);e.isValid()&&(o=null==o||o,r&&ct(e,q(e,"Month")+r*n),s&&V(e,"Date",q(e,"Date")+s*n),a&&e._d.setTime(e._d.valueOf()+a*n),o&&t.updateOffset(e,s||r))}function oi(t){return"string"==typeof t||t instanceof String}function ai(t){return v(t)||r(t)||oi(t)||s(t)||ri(t)||si(t)||null===t||void 0===t}function si(t){var e,a,s=i(t)&&!o(t),r=!1,l=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],c=l.length;for(e=0;e<c;e+=1)a=l[e],r=r||n(t,a);return s&&r}function ri(t){var i=e(t),n=!1;return i&&(n=0===t.filter(function(e){return!s(e)&&oi(t)}).length),i&&n}function li(t){var e,a,s=i(t)&&!o(t),r=!1,l=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(e=0;e<l.length;e+=1)a=l[e],r=r||n(t,a);return s&&r}function ci(t,e){var i=t.diff(e,"days",!0);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"}function di(e,i){1===arguments.length&&(arguments[0]?ai(arguments[0])?(e=arguments[0],i=void 0):li(arguments[0])&&(i=arguments[0],e=void 0):(e=void 0,i=void 0));var n=e||Te(),o=je(n,this).startOf("day"),a=t.calendarFormat(this,o)||"sameElse",s=i&&(w(i[a])?i[a].call(this,n):i[a]);return this.format(s||this.localeData().calendar(a,this,Te(n)))}function ui(){return new g(this)}function hi(t,e){var i=v(t)?t:Te(t);return!(!this.isValid()||!i.isValid())&&(e=R(e)||"millisecond","millisecond"===e?this.valueOf()>i.valueOf():i.valueOf()<this.clone().startOf(e).valueOf())}function pi(t,e){var i=v(t)?t:Te(t);return!(!this.isValid()||!i.isValid())&&(e=R(e)||"millisecond","millisecond"===e?this.valueOf()<i.valueOf():this.clone().endOf(e).valueOf()<i.valueOf())}function fi(t,e,i,n){var o=v(t)?t:Te(t),a=v(e)?e:Te(e);return!!(this.isValid()&&o.isValid()&&a.isValid())&&(n=n||"()",("("===n[0]?this.isAfter(o,i):!this.isBefore(o,i))&&(")"===n[1]?this.isBefore(a,i):!this.isAfter(a,i)))}function mi(t,e){var i,n=v(t)?t:Te(t);return!(!this.isValid()||!n.isValid())&&(e=R(e)||"millisecond","millisecond"===e?this.valueOf()===n.valueOf():(i=n.valueOf(),this.clone().startOf(e).valueOf()<=i&&i<=this.clone().endOf(e).valueOf()))}function gi(t,e){return this.isSame(t,e)||this.isAfter(t,e)}function vi(t,e){return this.isSame(t,e)||this.isBefore(t,e)}function yi(t,e,i){var n,o,a;if(!this.isValid())return NaN;if(n=je(t,this),!n.isValid())return NaN;switch(o=6e4*(n.utcOffset()-this.utcOffset()),e=R(e)){case"year":a=bi(this,n)/12;break;case"month":a=bi(this,n);break;case"quarter":a=bi(this,n)/3;break;case"second":a=(this-n)/1e3;break;case"minute":a=(this-n)/6e4;break;case"hour":a=(this-n)/36e5;break;case"day":a=(this-n-o)/864e5;break;case"week":a=(this-n-o)/6048e5;break;default:a=this-n}return i?a:B(a)}function bi(t,e){if(t.date()<e.date())return-bi(e,t);var i,n,o=12*(e.year()-t.year())+(e.month()-t.month()),a=t.clone().add(o,"months");return e-a<0?(i=t.clone().add(o-1,"months"),n=(e-a)/(a-i)):(i=t.clone().add(o+1,"months"),n=(e-a)/(i-a)),-(o+n)||0}function xi(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function wi(t){if(!this.isValid())return null;var e=!0!==t,i=e?this.clone().utc():this;return i.year()<0||i.year()>9999?A(i,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):w(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",A(i,"Z")):A(i,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function ki(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t,e,i,n,o="moment",a="";return this.isLocal()||(o=0===this.utcOffset()?"moment.utc":"moment.parseZone",a="Z"),t="["+o+'("]',e=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",i="-MM-DD[T]HH:mm:ss.SSS",n=a+'[")]',this.format(t+e+i+n)}function _i(e){e||(e=this.isUtc()?t.defaultFormatUtc:t.defaultFormat);var i=A(this,e);return this.localeData().postformat(i)}function Ci(t,e){return this.isValid()&&(v(t)&&t.isValid()||Te(t).isValid())?Ze({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function Si(t){return this.from(Te(),t)}function Ti(t,e){return this.isValid()&&(v(t)&&t.isValid()||Te(t).isValid())?Ze({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function Di(t){return this.to(Te(),t)}function $i(t){var e;return void 0===t?this._locale._abbr:(e=ne(t),null!=e&&(this._locale=e),this)}function Ei(){return this._locale}function Ai(t,e){return(t%e+e)%e}function Oi(t,e,i){return t<100&&t>=0?new Date(t+400,e,i)-Fa:new Date(t,e,i).valueOf()}function Fi(t,e,i){return t<100&&t>=0?Date.UTC(t+400,e,i)-Fa:Date.UTC(t,e,i)}function Pi(e){var i,n;if(void 0===(e=R(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?Fi:Oi,e){case"year":i=n(this.year(),0,1);break;case"quarter":i=n(this.year(),this.month()-this.month()%3,1);break;case"month":i=n(this.year(),this.month(),1);break;case"week":i=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":i=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":i=n(this.year(),this.month(),this.date());break;case"hour":i=this._d.valueOf(),i-=Ai(i+(this._isUTC?0:this.utcOffset()*Aa),Oa);break;case"minute":i=this._d.valueOf(),i-=Ai(i,Aa);break;case"second":i=this._d.valueOf(),i-=Ai(i,Ea)}return this._d.setTime(i),t.updateOffset(this,!0),this}function Mi(e){var i,n;if(void 0===(e=R(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?Fi:Oi,e){case"year":i=n(this.year()+1,0,1)-1;break;case"quarter":i=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":i=n(this.year(),this.month()+1,1)-1;break;case"week":i=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":i=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":i=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":i=this._d.valueOf(),i+=Oa-Ai(i+(this._isUTC?0:this.utcOffset()*Aa),Oa)-1;break;case"minute":i=this._d.valueOf(),i+=Aa-Ai(i,Aa)-1;break;case"second":i=this._d.valueOf(),i+=Ea-Ai(i,Ea)-1}return this._d.setTime(i),t.updateOffset(this,!0),this}function Li(){return this._d.valueOf()-6e4*(this._offset||0)}function Ii(){return Math.floor(this.valueOf()/1e3)}function Ni(){return new Date(this.valueOf())}function Ri(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]}function ji(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}}function Yi(){return this.isValid()?this.toISOString():null}function Hi(){return p(this)}function zi(){return c({},h(this))}function Bi(){return h(this).overflow}function Ui(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function Wi(e,i){var n,o,a,s=this._eras||ne("en")._eras;for(n=0,o=s.length;n<o;++n){switch(typeof s[n].since){case"string":a=t(s[n].since).startOf("day"),s[n].since=a.valueOf()}switch(typeof s[n].until){case"undefined":s[n].until=1/0;break;case"string":a=t(s[n].until).startOf("day").valueOf(),s[n].until=a.valueOf()}}return s}function qi(t,e,i){var n,o,a,s,r,l=this.eras();for(t=t.toUpperCase(),n=0,o=l.length;n<o;++n)if(a=l[n].name.toUpperCase(),s=l[n].abbr.toUpperCase(),r=l[n].narrow.toUpperCase(),i)switch(e){case"N":case"NN":case"NNN":if(s===t)return l[n];break;case"NNNN":if(a===t)return l[n];break;case"NNNNN":if(r===t)return l[n]}else if([a,s,r].indexOf(t)>=0)return l[n]}function Vi(e,i){var n=e.since<=e.until?1:-1;return void 0===i?t(e.since).year():t(e.since).year()+(i-e.offset)*n}function Gi(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].name;if(n[t].until<=i&&i<=n[t].since)return n[t].name}return""}function Xi(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].narrow;if(n[t].until<=i&&i<=n[t].since)return n[t].narrow}return""}function Qi(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].abbr;if(n[t].until<=i&&i<=n[t].since)return n[t].abbr}return""}function Ki(){var e,i,n,o,a=this.localeData().eras();for(e=0,i=a.length;e<i;++e)if(n=a[e].since<=a[e].until?1:-1,o=this.clone().startOf("day").valueOf(),a[e].since<=o&&o<=a[e].until||a[e].until<=o&&o<=a[e].since)return(this.year()-t(a[e].since).year())*n+a[e].offset;return this.year()}function Zi(t){return n(this,"_erasNameRegex")||sn.call(this),t?this._erasNameRegex:this._erasRegex}function Ji(t){return n(this,"_erasAbbrRegex")||sn.call(this),t?this._erasAbbrRegex:this._erasRegex}function tn(t){return n(this,"_erasNarrowRegex")||sn.call(this),t?this._erasNarrowRegex:this._erasRegex}function en(t,e){return e.erasAbbrRegex(t)}function nn(t,e){return e.erasNameRegex(t)}function on(t,e){return e.erasNarrowRegex(t)}function an(t,e){return e._eraYearOrdinalRegex||Eo}function sn(){var t,e,i=[],n=[],o=[],a=[],s=this.eras();for(t=0,e=s.length;t<e;++t)n.push(J(s[t].name)),i.push(J(s[t].abbr)),o.push(J(s[t].narrow)),a.push(J(s[t].name)),a.push(J(s[t].abbr)),a.push(J(s[t].narrow));this._erasRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+n.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+i.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+o.join("|")+")","i")}function rn(t,e){D(0,[t,t.length],0,e)}function ln(t){return fn.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function cn(t){return fn.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)}function dn(){return kt(this.year(),1,4)}function un(){return kt(this.isoWeekYear(),1,4)}function hn(){var t=this.localeData()._week;return kt(this.year(),t.dow,t.doy)}function pn(){var t=this.localeData()._week;return kt(this.weekYear(),t.dow,t.doy)}function fn(t,e,i,n,o){var a;return null==t?wt(this,n,o).year:(a=kt(t,n,o),e>a&&(e=a),mn.call(this,t,e,i,n,o))}function mn(t,e,i,n,o){var a=xt(t,e,i,n,o),s=yt(a.year,0,a.dayOfYear);return this.year(s.getUTCFullYear()),this.month(s.getUTCMonth()),this.date(s.getUTCDate()),this}function gn(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)}function vn(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")}function yn(t,e){e[Bo]=U(1e3*("0."+t))}function bn(){return this._isUTC?"UTC":""}function xn(){return this._isUTC?"Coordinated Universal Time":""}function wn(t){return Te(1e3*t)}function kn(){return Te.apply(null,arguments).parseZone()}function _n(t){return t}function Cn(t,e,i,n){var o=ne(),a=d().set(n,e);return o[i](a,t)}function Sn(t,e,i){if(s(t)&&(e=t,t=void 0),t=t||"",null!=e)return Cn(t,e,i,"month");var n,o=[];for(n=0;n<12;n++)o[n]=Cn(t,n,i,"month");return o}function Tn(t,e,i,n){"boolean"==typeof t?(s(e)&&(i=e,e=void 0),e=e||""):(e=t,i=e,t=!1,s(e)&&(i=e,e=void 0),e=e||"");var o,a=ne(),r=t?a._week.dow:0,l=[];if(null!=i)return Cn(e,(i+r)%7,n,"day");for(o=0;o<7;o++)l[o]=Cn(e,(o+r)%7,n,"day");return l}function Dn(t,e){return Sn(t,e,"months")}function $n(t,e){return Sn(t,e,"monthsShort")}function En(t,e,i){return Tn(t,e,i,"weekdays")}function An(t,e,i){return Tn(t,e,i,"weekdaysShort")}function On(t,e,i){return Tn(t,e,i,"weekdaysMin")}function Fn(){var t=this._data;return this._milliseconds=Ya(this._milliseconds),this._days=Ya(this._days),this._months=Ya(this._months),t.milliseconds=Ya(t.milliseconds),t.seconds=Ya(t.seconds),t.minutes=Ya(t.minutes),t.hours=Ya(t.hours),t.months=Ya(t.months),t.years=Ya(t.years),this}function Pn(t,e,i,n){var o=Ze(e,i);return t._milliseconds+=n*o._milliseconds,t._days+=n*o._days,t._months+=n*o._months,t._bubble()}function Mn(t,e){return Pn(this,t,e,1)}function Ln(t,e){return Pn(this,t,e,-1)}function In(t){return t<0?Math.floor(t):Math.ceil(t)}function Nn(){var t,e,i,n,o,a=this._milliseconds,s=this._days,r=this._months,l=this._data;return a>=0&&s>=0&&r>=0||a<=0&&s<=0&&r<=0||(a+=864e5*In(jn(r)+s),s=0,r=0),l.milliseconds=a%1e3,t=B(a/1e3),l.seconds=t%60,e=B(t/60),l.minutes=e%60,i=B(e/60),l.hours=i%24,s+=B(i/24),o=B(Rn(s)),r+=o,s-=In(jn(o)),n=B(r/12),r%=12,l.days=s,l.months=r,l.years=n,this}function Rn(t){return 4800*t/146097}function jn(t){return 146097*t/4800}function Yn(t){if(!this.isValid())return NaN;var e,i,n=this._milliseconds;if("month"===(t=R(t))||"quarter"===t||"year"===t)switch(e=this._days+n/864e5,i=this._months+Rn(e),t){case"month":return i;case"quarter":return i/3;case"year":return i/12}else switch(e=this._days+Math.round(jn(this._months)),t){case"week":return e/7+n/6048e5;case"day":return e+n/864e5;case"hour":return 24*e+n/36e5;case"minute":return 1440*e+n/6e4;case"second":return 86400*e+n/1e3;case"millisecond":return Math.floor(864e5*e)+n;default:throw new Error("Unknown unit "+t)}}function Hn(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*U(this._months/12):NaN}function zn(t){return function(){return this.as(t)}}function Bn(){return Ze(this)}function Un(t){return t=R(t),this.isValid()?this[t+"s"]():NaN}function Wn(t){return function(){return this.isValid()?this._data[t]:NaN}}function qn(){return B(this.days()/7)}function Vn(t,e,i,n,o){return o.relativeTime(e||1,!!i,t,n)}function Gn(t,e,i,n){var o=Ze(t).abs(),a=ns(o.as("s")),s=ns(o.as("m")),r=ns(o.as("h")),l=ns(o.as("d")),c=ns(o.as("M")),d=ns(o.as("w")),u=ns(o.as("y")),h=a<=i.ss&&["s",a]||a<i.s&&["ss",a]||s<=1&&["m"]||s<i.m&&["mm",s]||r<=1&&["h"]||r<i.h&&["hh",r]||l<=1&&["d"]||l<i.d&&["dd",l];return null!=i.w&&(h=h||d<=1&&["w"]||d<i.w&&["ww",d]),h=h||c<=1&&["M"]||c<i.M&&["MM",c]||u<=1&&["y"]||["yy",u],h[2]=e,h[3]=+t>0,h[4]=n,Vn.apply(null,h)}function Xn(t){return void 0===t?ns:"function"==typeof t&&(ns=t,!0)}function Qn(t,e){return void 0!==os[t]&&(void 0===e?os[t]:(os[t]=e,"s"===t&&(os.ss=e-1),!0))}function Kn(t,e){if(!this.isValid())return this.localeData().invalidDate();var i,n,o=!1,a=os;return"object"==typeof t&&(e=t,t=!1),"boolean"==typeof t&&(o=t),"object"==typeof e&&(a=Object.assign({},os,e),null!=e.s&&null==e.ss&&(a.ss=e.s-1)),i=this.localeData(),n=Gn(this,!o,a,i),o&&(n=i.pastFuture(+this,n)),i.postformat(n)}function Zn(t){return(t>0)-(t<0)||+t}function Jn(){if(!this.isValid())return this.localeData().invalidDate();var t,e,i,n,o,a,s,r,l=as(this._milliseconds)/1e3,c=as(this._days),d=as(this._months),u=this.asSeconds();return u?(t=B(l/60),e=B(t/60),l%=60,t%=60,i=B(d/12),d%=12,n=l?l.toFixed(3).replace(/\.?0+$/,""):"",o=u<0?"-":"",a=Zn(this._months)!==Zn(u)?"-":"",s=Zn(this._days)!==Zn(u)?"-":"",r=Zn(this._milliseconds)!==Zn(u)?"-":"",o+"P"+(i?a+i+"Y":"")+(d?a+d+"M":"")+(c?s+c+"D":"")+(e||t||l?"T":"")+(e?r+e+"H":"")+(t?r+t+"M":"")+(l?r+n+"S":"")):"P0D"}var to,eo;eo=Array.prototype.some?Array.prototype.some:function(t){var e,i=Object(this),n=i.length>>>0;for(e=0;e<n;e++)if(e in i&&t.call(this,i[e],e,i))return!0;return!1};var io=t.momentProperties=[],no=!1,oo={};t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var ao;ao=Object.keys?Object.keys:function(t){var e,i=[];for(e in t)n(t,e)&&i.push(e);return i};var so,ro={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},lo=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,co=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,uo={},ho={},po={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},fo=/\d{1,2}/,mo={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},go={},vo={},yo=/\d/,bo=/\d\d/,xo=/\d{3}/,wo=/\d{4}/,ko=/[+-]?\d{6}/,_o=/\d\d?/,Co=/\d\d\d\d?/,So=/\d\d\d\d\d\d?/,To=/\d{1,3}/,Do=/\d{1,4}/,$o=/[+-]?\d{1,6}/,Eo=/\d+/,Ao=/[+-]?\d+/,Oo=/Z|[+-]\d\d:?\d\d/gi,Fo=/Z|[+-]\d\d(?::?\d\d)?/gi,Po=/[+-]?\d+(\.\d{1,3})?/,Mo=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;so={};var Lo,Io={},No=0,Ro=1,jo=2,Yo=3,Ho=4,zo=5,Bo=6,Uo=7,Wo=8;Lo=Array.prototype.indexOf?Array.prototype.indexOf:function(t){var e;for(e=0;e<this.length;++e)if(this[e]===t)return e;return-1},D("M",["MM",2],"Mo",function(){return this.month()+1}),D("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),D("MMMM",0,0,function(t){return this.localeData().months(this,t)}),N("month","M"),Y("month",8),Q("M",_o),Q("MM",_o,bo),Q("MMM",function(t,e){return e.monthsShortRegex(t)}),Q("MMMM",function(t,e){return e.monthsRegex(t)}),tt(["M","MM"],function(t,e){e[Ro]=U(t)-1}),tt(["MMM","MMMM"],function(t,e,i,n){var o=i._locale.monthsParse(t,n,i._strict);null!=o?e[Ro]=o:h(i).invalidMonth=t});var qo="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Vo="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Go=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Xo=Mo,Qo=Mo;D("Y",0,0,function(){var t=this.year();return t<=9999?T(t,4):"+"+t}),D(0,["YY",2],0,function(){return this.year()%100}),D(0,["YYYY",4],0,"year"),D(0,["YYYYY",5],0,"year"),D(0,["YYYYYY",6,!0],0,"year"),N("year","y"),Y("year",1),Q("Y",Ao),Q("YY",_o,bo),Q("YYYY",Do,wo),Q("YYYYY",$o,ko),Q("YYYYYY",$o,ko),tt(["YYYYY","YYYYYY"],No),tt("YYYY",function(e,i){i[No]=2===e.length?t.parseTwoDigitYear(e):U(e)}),tt("YY",function(e,i){i[No]=t.parseTwoDigitYear(e)}),tt("Y",function(t,e){e[No]=parseInt(t,10)}),t.parseTwoDigitYear=function(t){return U(t)+(U(t)>68?1900:2e3)};var Ko=W("FullYear",!0);D("w",["ww",2],"wo","week"),D("W",["WW",2],"Wo","isoWeek"),N("week","w"),N("isoWeek","W"),Y("week",5),Y("isoWeek",5),Q("w",_o),Q("ww",_o,bo),Q("W",_o),Q("WW",_o,bo),et(["w","ww","W","WW"],function(t,e,i,n){e[n.substr(0,1)]=U(t)});var Zo={dow:0,doy:6};D("d",0,"do","day"),D("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),D("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),D("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),D("e",0,0,"weekday"),D("E",0,0,"isoWeekday"),N("day","d"),N("weekday","e"),N("isoWeekday","E"),Y("day",11),Y("weekday",11),Y("isoWeekday",11),Q("d",_o),Q("e",_o),Q("E",_o),Q("dd",function(t,e){return e.weekdaysMinRegex(t)}),Q("ddd",function(t,e){return e.weekdaysShortRegex(t)}),Q("dddd",function(t,e){return e.weekdaysRegex(t)}),et(["dd","ddd","dddd"],function(t,e,i,n){var o=i._locale.weekdaysParse(t,n,i._strict);null!=o?e.d=o:h(i).invalidWeekday=t}),et(["d","e","E"],function(t,e,i,n){e[n]=U(t)});var Jo="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),ta="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),ea="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),ia=Mo,na=Mo,oa=Mo;D("H",["HH",2],0,"hour"),D("h",["hh",2],0,Bt),D("k",["kk",2],0,Ut),D("hmm",0,0,function(){return""+Bt.apply(this)+T(this.minutes(),2)}),D("hmmss",0,0,function(){return""+Bt.apply(this)+T(this.minutes(),2)+T(this.seconds(),2)}),D("Hmm",0,0,function(){return""+this.hours()+T(this.minutes(),2)}),D("Hmmss",0,0,function(){return""+this.hours()+T(this.minutes(),2)+T(this.seconds(),2)}),Wt("a",!0),Wt("A",!1),N("hour","h"),Y("hour",13),Q("a",qt),Q("A",qt),Q("H",_o),Q("h",_o),Q("k",_o),Q("HH",_o,bo),Q("hh",_o,bo),Q("kk",_o,bo),Q("hmm",Co),Q("hmmss",So),Q("Hmm",Co),Q("Hmmss",So),tt(["H","HH"],Yo),tt(["k","kk"],function(t,e,i){var n=U(t);e[Yo]=24===n?0:n}),tt(["a","A"],function(t,e,i){i._isPm=i._locale.isPM(t),i._meridiem=t}),tt(["h","hh"],function(t,e,i){e[Yo]=U(t),h(i).bigHour=!0}),tt("hmm",function(t,e,i){var n=t.length-2;e[Yo]=U(t.substr(0,n)),e[Ho]=U(t.substr(n)),h(i).bigHour=!0}),tt("hmmss",function(t,e,i){var n=t.length-4,o=t.length-2;e[Yo]=U(t.substr(0,n)),e[Ho]=U(t.substr(n,2)),e[zo]=U(t.substr(o)),h(i).bigHour=!0}),tt("Hmm",function(t,e,i){var n=t.length-2;e[Yo]=U(t.substr(0,n)),e[Ho]=U(t.substr(n))}),tt("Hmmss",function(t,e,i){var n=t.length-4,o=t.length-2;e[Yo]=U(t.substr(0,n)),e[Ho]=U(t.substr(n,2)),e[zo]=U(t.substr(o))});var aa,sa=/[ap]\.?m?\.?/i,ra=W("Hours",!0),la={calendar:ro,longDateFormat:po,invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:fo,relativeTime:mo,months:qo,monthsShort:Vo,week:Zo,weekdays:Jo,weekdaysMin:ea,weekdaysShort:ta,meridiemParse:sa},ca={},da={},ua=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ha=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pa=/Z|[+-]\d\d(?::?\d\d)?/,fa=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ma=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ga=/^\/?Date\((-?\d+)/i,va=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,ya={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};t.createFromInputFallback=b("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),t.ISO_8601=function(){},t.RFC_2822=function(){};var ba=b("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Te.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:f()}),xa=b("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Te.apply(null,arguments);return this.isValid()&&t.isValid()?t>this?this:t:f()}),wa=function(){return Date.now?Date.now():+new Date},ka=["year","quarter","month","week","day","hour","minute","second","millisecond"];Ne("Z",":"),Ne("ZZ",""),Q("Z",Fo),Q("ZZ",Fo),
tt(["Z","ZZ"],function(t,e,i){i._useUTC=!0,i._tzm=Re(Fo,t)});var _a=/([\+\-]|\d\d)/gi;t.updateOffset=function(){};var Ca=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Sa=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;Ze.fn=Pe.prototype,Ze.invalid=Fe;var Ta=ii(1,"add"),Da=ii(-1,"subtract");t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var $a=b("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)}),Ea=1e3,Aa=60*Ea,Oa=60*Aa,Fa=3506328*Oa;D("N",0,0,"eraAbbr"),D("NN",0,0,"eraAbbr"),D("NNN",0,0,"eraAbbr"),D("NNNN",0,0,"eraName"),D("NNNNN",0,0,"eraNarrow"),D("y",["y",1],"yo","eraYear"),D("y",["yy",2],0,"eraYear"),D("y",["yyy",3],0,"eraYear"),D("y",["yyyy",4],0,"eraYear"),Q("N",en),Q("NN",en),Q("NNN",en),Q("NNNN",nn),Q("NNNNN",on),tt(["N","NN","NNN","NNNN","NNNNN"],function(t,e,i,n){var o=i._locale.erasParse(t,n,i._strict);o?h(i).era=o:h(i).invalidEra=t}),Q("y",Eo),Q("yy",Eo),Q("yyy",Eo),Q("yyyy",Eo),Q("yo",an),tt(["y","yy","yyy","yyyy"],No),tt(["yo"],function(t,e,i,n){var o;i._locale._eraYearOrdinalRegex&&(o=t.match(i._locale._eraYearOrdinalRegex)),i._locale.eraYearOrdinalParse?e[No]=i._locale.eraYearOrdinalParse(t,o):e[No]=parseInt(t,10)}),D(0,["gg",2],0,function(){return this.weekYear()%100}),D(0,["GG",2],0,function(){return this.isoWeekYear()%100}),rn("gggg","weekYear"),rn("ggggg","weekYear"),rn("GGGG","isoWeekYear"),rn("GGGGG","isoWeekYear"),N("weekYear","gg"),N("isoWeekYear","GG"),Y("weekYear",1),Y("isoWeekYear",1),Q("G",Ao),Q("g",Ao),Q("GG",_o,bo),Q("gg",_o,bo),Q("GGGG",Do,wo),Q("gggg",Do,wo),Q("GGGGG",$o,ko),Q("ggggg",$o,ko),et(["gggg","ggggg","GGGG","GGGGG"],function(t,e,i,n){e[n.substr(0,2)]=U(t)}),et(["gg","GG"],function(e,i,n,o){i[o]=t.parseTwoDigitYear(e)}),D("Q",0,"Qo","quarter"),N("quarter","Q"),Y("quarter",7),Q("Q",yo),tt("Q",function(t,e){e[Ro]=3*(U(t)-1)}),D("D",["DD",2],"Do","date"),N("date","D"),Y("date",9),Q("D",_o),Q("DD",_o,bo),Q("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),tt(["D","DD"],jo),tt("Do",function(t,e){e[jo]=U(t.match(_o)[0])});var Pa=W("Date",!0);D("DDD",["DDDD",3],"DDDo","dayOfYear"),N("dayOfYear","DDD"),Y("dayOfYear",4),Q("DDD",To),Q("DDDD",xo),tt(["DDD","DDDD"],function(t,e,i){i._dayOfYear=U(t)}),D("m",["mm",2],0,"minute"),N("minute","m"),Y("minute",14),Q("m",_o),Q("mm",_o,bo),tt(["m","mm"],Ho);var Ma=W("Minutes",!1);D("s",["ss",2],0,"second"),N("second","s"),Y("second",15),Q("s",_o),Q("ss",_o,bo),tt(["s","ss"],zo);var La=W("Seconds",!1);D("S",0,0,function(){return~~(this.millisecond()/100)}),D(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),D(0,["SSS",3],0,"millisecond"),D(0,["SSSS",4],0,function(){return 10*this.millisecond()}),D(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),D(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),D(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),D(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),D(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),N("millisecond","ms"),Y("millisecond",16),Q("S",To,yo),Q("SS",To,bo),Q("SSS",To,xo);var Ia,Na;for(Ia="SSSS";Ia.length<=9;Ia+="S")Q(Ia,Eo);for(Ia="S";Ia.length<=9;Ia+="S")tt(Ia,yn);Na=W("Milliseconds",!1),D("z",0,0,"zoneAbbr"),D("zz",0,0,"zoneName");var Ra=g.prototype;Ra.add=Ta,Ra.calendar=di,Ra.clone=ui,Ra.diff=yi,Ra.endOf=Mi,Ra.format=_i,Ra.from=Ci,Ra.fromNow=Si,Ra.to=Ti,Ra.toNow=Di,Ra.get=G,Ra.invalidAt=Bi,Ra.isAfter=hi,Ra.isBefore=pi,Ra.isBetween=fi,Ra.isSame=mi,Ra.isSameOrAfter=gi,Ra.isSameOrBefore=vi,Ra.isValid=Hi,Ra.lang=$a,Ra.locale=$i,Ra.localeData=Ei,Ra.max=xa,Ra.min=ba,Ra.parsingFlags=zi,Ra.set=X,Ra.startOf=Pi,Ra.subtract=Da,Ra.toArray=Ri,Ra.toObject=ji,Ra.toDate=Ni,Ra.toISOString=wi,Ra.inspect=ki,"undefined"!=typeof Symbol&&null!=Symbol.for&&(Ra[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),Ra.toJSON=Yi,Ra.toString=xi,Ra.unix=Ii,Ra.valueOf=Li,Ra.creationData=Ui,Ra.eraName=Gi,Ra.eraNarrow=Xi,Ra.eraAbbr=Qi,Ra.eraYear=Ki,Ra.year=Ko,Ra.isLeapYear=gt,Ra.weekYear=ln,Ra.isoWeekYear=cn,Ra.quarter=Ra.quarters=gn,Ra.month=dt,Ra.daysInMonth=ut,Ra.week=Ra.weeks=Tt,Ra.isoWeek=Ra.isoWeeks=Dt,Ra.weeksInYear=hn,Ra.weeksInWeekYear=pn,Ra.isoWeeksInYear=dn,Ra.isoWeeksInISOWeekYear=un,Ra.date=Pa,Ra.day=Ra.days=It,Ra.weekday=Nt,Ra.isoWeekday=Rt,Ra.dayOfYear=vn,Ra.hour=Ra.hours=ra,Ra.minute=Ra.minutes=Ma,Ra.second=Ra.seconds=La,Ra.millisecond=Ra.milliseconds=Na,Ra.utcOffset=He,Ra.utc=Be,Ra.local=Ue,Ra.parseZone=We,Ra.hasAlignedHourOffset=qe,Ra.isDST=Ve,Ra.isLocal=Xe,Ra.isUtcOffset=Qe,Ra.isUtc=Ke,Ra.isUTC=Ke,Ra.zoneAbbr=bn,Ra.zoneName=xn,Ra.dates=b("dates accessor is deprecated. Use date instead.",Pa),Ra.months=b("months accessor is deprecated. Use month instead",dt),Ra.years=b("years accessor is deprecated. Use year instead",Ko),Ra.zone=b("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",ze),Ra.isDSTShifted=b("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",Ge);var ja=C.prototype;ja.calendar=S,ja.longDateFormat=F,ja.invalidDate=P,ja.ordinal=M,ja.preparse=_n,ja.postformat=_n,ja.relativeTime=L,ja.pastFuture=I,ja.set=k,ja.eras=Wi,ja.erasParse=qi,ja.erasConvertYear=Vi,ja.erasAbbrRegex=Ji,ja.erasNameRegex=Zi,ja.erasNarrowRegex=tn,ja.months=at,ja.monthsShort=st,ja.monthsParse=lt,ja.monthsRegex=pt,ja.monthsShortRegex=ht,ja.week=_t,ja.firstDayOfYear=St,ja.firstDayOfWeek=Ct,ja.weekdays=Ot,ja.weekdaysMin=Pt,ja.weekdaysShort=Ft,ja.weekdaysParse=Lt,ja.weekdaysRegex=jt,ja.weekdaysShortRegex=Yt,ja.weekdaysMinRegex=Ht,ja.isPM=Vt,ja.meridiem=Gt,te("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===U(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th")}}),t.lang=b("moment.lang is deprecated. Use moment.locale instead.",te),t.langData=b("moment.langData is deprecated. Use moment.localeData instead.",ne);var Ya=Math.abs,Ha=zn("ms"),za=zn("s"),Ba=zn("m"),Ua=zn("h"),Wa=zn("d"),qa=zn("w"),Va=zn("M"),Ga=zn("Q"),Xa=zn("y"),Qa=Wn("milliseconds"),Ka=Wn("seconds"),Za=Wn("minutes"),Ja=Wn("hours"),ts=Wn("days"),es=Wn("months"),is=Wn("years"),ns=Math.round,os={ss:44,s:45,m:45,h:22,d:26,w:null,M:11},as=Math.abs,ss=Pe.prototype;return ss.isValid=Oe,ss.abs=Fn,ss.add=Mn,ss.subtract=Ln,ss.as=Yn,ss.asMilliseconds=Ha,ss.asSeconds=za,ss.asMinutes=Ba,ss.asHours=Ua,ss.asDays=Wa,ss.asWeeks=qa,ss.asMonths=Va,ss.asQuarters=Ga,ss.asYears=Xa,ss.valueOf=Hn,ss._bubble=Nn,ss.clone=Bn,ss.get=Un,ss.milliseconds=Qa,ss.seconds=Ka,ss.minutes=Za,ss.hours=Ja,ss.days=ts,ss.weeks=qn,ss.months=es,ss.years=is,ss.humanize=Kn,ss.toISOString=Jn,ss.toString=Jn,ss.toJSON=Jn,ss.locale=$i,ss.localeData=Ei,ss.toIsoString=b("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Jn),ss.lang=$a,D("X",0,0,"unix"),D("x",0,0,"valueOf"),Q("x",Ao),Q("X",Po),tt("X",function(t,e,i){i._d=new Date(1e3*parseFloat(t))}),tt("x",function(t,e,i){i._d=new Date(U(t))}),t.version="2.29.4",function(t){to=t}(Te),t.fn=Ra,t.min=$e,t.max=Ee,t.now=wa,t.utc=d,t.unix=wn,t.months=Dn,t.isDate=r,t.locale=te,t.invalid=f,t.duration=Ze,t.isMoment=v,t.weekdays=En,t.parseZone=kn,t.localeData=ne,t.isDuration=Me,t.monthsShort=$n,t.weekdaysMin=On,t.defineLocale=ee,t.updateLocale=ie,t.locales=oe,t.weekdaysShort=An,t.normalizeUnits=R,t.relativeTimeRounding=Xn,t.relativeTimeThreshold=Qn,t.calendarFormat=ci,t.prototype=Ra,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t}),define("moment",["moment/moment"],function(t){return t}),define("backend",["fast","template","moment"],function(t,e,i){var n={api:{sidebar:function(e){colorArr=["red","green","yellow","blue","teal","orange","purple"],$colorNums=colorArr.length,badgeList={},$.each(e,function(e,i){$url=t.api.fixurl(e),$.isArray(i)?($nums=void 0!==i[0]?i[0]:0,$color=void 0!==i[1]?i[1]:colorArr[(isNaN($nums)?$nums.length:$nums)%$colorNums],$class=void 0!==i[2]?i[2]:"label"):($nums=i,$color=colorArr[(isNaN($nums)?$nums.length:$nums)%$colorNums],$class="label"),badgeList[$url]=$nums>0?'<small class="'+$class+" pull-right bg-"+$color+'">'+$nums+"</small>":""}),$.each(badgeList,function(t,e){var i=top.window.$("li a[addtabs][url='"+t+"']");i&&(top.window.$(".pull-right-container",i).html(e),top.window.$(".nav-addtabs li a[node-id='"+i.attr("addtabs")+"'] .pull-right-container").html(e))})},addtabs:function(e,i,n){var o="a[url='{url}']",a=top.window.$(o.replace(/\{url\}/,e));if(a.length>0)a.trigger("click");else if(e=t.api.fixurl(e),a=top.window.$(o.replace(/\{url\}/,e)),a.length>0){var s=a.parent().hasClass("active")?"dblclick":"click";a.trigger(s)}else{var r=e.substr(0,e.indexOf("?")>-1?e.indexOf("?"):e.length);a=top.window.$(o.replace(/\{url\}/,r)),a.length>0&&(n=void 0!==n?n:a.find("i").attr("class"),i=void 0!==i?i:a.find("span:first").text(),a.trigger("fa.event.toggleitem"));var l=top.window.$(".nav-tabs ul li a[node-url='"+e+"']");if(l.length>0)l.trigger("click");else{var c=Math.floor((new Date).valueOf()*Math.random());n=void 0!==n?n:"fa fa-circle-o",i=void 0!==i?i:"",top.window.$("<a />").append('<i class="'+n+'"></i> <span>'+i+"</span>").prop("href",e).attr({url:e,addtabs:c}).addClass("hide").appendTo(top.window.document.body).trigger("click")}}},closetabs:function(e){if(void 0===e)top.window.$("ul.nav-addtabs li.active .close-tab").trigger("click");else{var i="a[url='{url}']",n=top.window.$(i.replace(/\{url\}/,e));if(0===n.length)if(e=t.api.fixurl(e),n=top.window.$(i.replace(/\{url\}/,e)),0===n.length);else{var o=e.substr(0,e.indexOf("?")>-1?e.indexOf("?"):e.length);n=top.window.$(i.replace(/\{url\}/,o)),0===n.length&&(n=top.window.$(".nav-tabs ul li a[node-url='"+e+"']"))}n.length>0&&n.attr("addtabs")&&top.window.$("ul.nav-addtabs li#tab_"+n.attr("addtabs")+" .close-tab").trigger("click")}},replaceids:function(t,e){if(e.indexOf("{ids}")>-1){var i=0,n=$(t).data("table-id");if(n&&$("#"+n).length>0&&$("#"+n).data("bootstrap.table")){i=require("table").api.selectedids($("#"+n)).join(",")}e=e.replace(/\{ids\}/g,i)}return e},refreshmenu:function(){top.window.$(".sidebar-menu").trigger("refresh")},gettablecolumnbutton:function(t){if(void 0!==t.tableId&&void 0!==t.fieldIndex&&void 0!==t.buttonIndex){var e=$("#"+t.tableId).bootstrapTable("getOptions");if(e){var i=null;if($.each(e.columns,function(e,n){if($.each(n,function(e,n){if(void 0!==n.fieldIndex&&n.fieldIndex===t.fieldIndex)return i=n,!1}),i)return!1}),i)return i.buttons[t.buttonIndex]}}return null}},init:function(){/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&$("html").addClass("ios-fix"),Toastr.options.positionClass="index"===Config.controllername?"toast-top-right-index":"toast-top-right",$(document).on("click",".btn-dialog,.dialogit",function(t){var e=this,i=$.extend({},$(e).data()||{}),o=n.api.replaceids(e,$(e).data("url")||$(e).attr("href")),a=$(e).attr("title")||$(e).data("title")||$(e).data("original-title"),s=n.api.gettablecolumnbutton(i);return s&&"function"==typeof s.callback&&(i.callback=s.callback),void 0!==i.confirm?Layer.confirm(i.confirm,function(t){n.api.open(o,a,i),Layer.close(t)}):window[$(e).data("window")||"self"].Backend.api.open(o,a,i),!1}),$(document).on("click",".btn-addtabs,.addtabsit",function(t){var e=this,i=$.extend({},$(e).data()||{}),o=n.api.replaceids(e,$(e).data("url")||$(e).attr("href")),a=$(e).attr("title")||$(e).data("title")||$(e).data("original-title"),s=$(e).attr("icon")||$(e).data("icon");return void 0!==i.confirm?Layer.confirm(i.confirm,function(t){n.api.addtabs(o,a,s),Layer.close(t)}):n.api.addtabs(o,a,s),!1}),$(document).on("click",".btn-ajax,.ajaxit",function(t){var e=this,i=$.extend({},$(e).data()||{});void 0===i.url&&$(e).attr("href")&&(i.url=$(e).attr("href")),i.url=n.api.replaceids(this,i.url);var o="function"==typeof i.success?i.success:null,a="function"==typeof i.error?i.error:null;delete i.success,delete i.error;var s=n.api.gettablecolumnbutton(i);return s&&("function"==typeof s.success&&(o=s.success),"function"==typeof s.error&&(a=s.error)),!o&&void 0!==i.tableId&&void 0!==i.refresh&&i.refresh&&(o=function(){$("#"+i.tableId).bootstrapTable("refresh")}),void 0!==i.confirm?Layer.confirm(i.confirm,function(t){n.api.ajax(i,o,a),Layer.close(t)}):n.api.ajax(i,o,a),!1}),$(document).on("click",".btn-click,.clickit",function(t){var e=this,i=$.extend({},$(e).data()||{}),o={};if(void 0!==i.tableId){var a=parseInt(i.rowIndex),s=$("#"+i.tableId).bootstrapTable("getData");o=void 0!==s[a]?s[a]:{}}var r=n.api.gettablecolumnbutton(i),l="function"==typeof r.click?r.click:$.noop;return void 0!==i.confirm?Layer.confirm(i.confirm,function(t){l.apply(e,[i,o,r]),Layer.close(t)}):l.apply(e,[i,o,r]),!1}),$(".fixed-footer").length>0&&$(document.body).css("padding-bottom",$(".fixed-footer").outerHeight()),$(".layer-footer").length>0&&self===top&&$(".layer-footer").show(),"ontouchstart"in document.documentElement||$("body").tooltip({selector:'[data-toggle="tooltip"]',trigger:"hover"}),$("body").popover({selector:'[data-toggle="popover"]'})}};return n.api=$.extend(t.api,n.api),window.Template=e,window.Moment=i,window.Backend=n,n.init(),n}),define("backend-init",["backend"],function(t){}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?e(require("../moment")):"function"==typeof define&&define.amd?define("moment/locale/zh-cn",["../moment"],e):e(t.moment)}(this,function(t){"use strict";return t.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(t,e){return 12===t&&(t=0),"凌晨"===e||"早上"===e||"上午"===e?t:"下午"===e||"晚上"===e?t+12:t>=11?t:t+12},meridiem:function(t,e,i){var n=100*t+e;return n<600?"凌晨":n<900?"早上":n<1130?"上午":n<1230?"中午":n<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(t){return t.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(t){return this.week()!==t.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(t,e){switch(e){case"d":case"D":case"DDD":return t+"日";case"M":return t+"月";case"w":case"W":return t+"周";default:return t}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}),function(t){"use strict";function e(t){var e=arguments,i=!0,n=1;return t=t.replace(/%s/g,function(){var t=e[n++];return void 0===t?(i=!1,""):t}),i?t:""}function i(e,i){var n=-1;return t.each(e,function(t,e){return e.field!==i||(n=t,!1)}),n}function n(){var e,i,n;return null===u&&(n=t("<p/>").addClass("fixed-table-scroll-inner"),(e=t("<div/>").addClass("fixed-table-scroll-outer")).append(n),t("body").append(e),i=n[0].offsetWidth,e.css("overflow","scroll"),i===(n=n[0].offsetWidth)&&(n=e[0].clientWidth),e.remove(),u=i-n),u}function o(i,n,o,a){var s,r=n;return"string"==typeof n&&(1<(s=n.split(".")).length?(r=window,t.each(s,function(t,e){r=r[e]})):r=window[n]),"object"==typeof r?r:"function"==typeof r?r.apply(i,o||[]):!r&&"string"==typeof n&&e.apply(this,[n].concat(o))?e.apply(this,[n].concat(o)):a}function a(e,i,n){var o,a=Object.getOwnPropertyNames(e),s=Object.getOwnPropertyNames(i);if(n&&a.length!==s.length)return!1;for(var r=0;r<a.length;r++)if(o=a[r],-1<t.inArray(o,s)&&e[o]!==i[o])return!1;return!0}function s(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t}function r(t){for(var e in t){var i=e.split(/(?=[A-Z])/).join("-").toLowerCase();i!==e&&(t[i]=t[e],delete t[e])}return t}function l(t,e,i){var n=t;if("string"!=typeof e||t.hasOwnProperty(e))return i?s(t[e]):t[e];var o,a=e.split(".");for(o in a)a.hasOwnProperty(o)&&(n=n&&n[a[o]]);return i?s(n):n}function c(){return!!(0<navigator.userAgent.indexOf("MSIE ")||navigator.userAgent.match(/Trident.*rv\:11\./))}function d(e,i){this.options=i,this.$el=t(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0,this.init()}var u=null,h=(d.DEFAULTS={classes:"table table-hover",sortClass:void 0,locale:void 0,height:void 0,undefinedText:"-",sortName:void 0,sortOrder:"asc",sortStable:!1,striped:!1,columns:[[]],data:[],totalField:"total",dataField:"rows",method:"get",url:void 0,ajax:void 0,cache:!0,contentType:"application/json",dataType:"json",ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},pagination:!1,onlyInfoPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",search:!1,searchOnEnterKey:!1,strictSearch:!1,searchAlign:"right",selectItemName:"btSelectItem",showHeader:!0,showFooter:!1,showColumns:!1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,buttonsAlign:"right",smartDisplay:!0,escape:!1,minimumCountColumns:1,idField:void 0,uniqueId:void 0,cardView:!1,detailView:!1,detailFormatter:function(t,e){return""},trimOnSearch:!0,clickToSelect:!1,singleSelect:!1,toolbar:void 0,toolbarAlign:"left",checkboxHeader:!0,sortable:!0,silentSort:!0,maintainSelected:!1,searchTimeOut:500,searchText:"",iconSize:void 0,buttonsClass:"default",iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggle:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus"},customSearch:t.noop,customSort:t.noop,rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},footerStyle:function(t,e){return{}},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1}},(d.LOCALES={})["en-US"]=d.LOCALES.en={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(t){return e("%s rows per page",t)},formatShowingRows:function(t,i,n){return e("Showing %s to %s of %s rows",t,i,n)},formatDetailPagination:function(t){return e("Showing %s rows",t)},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"}},t.extend(d.DEFAULTS,d.LOCALES["en-US"]),d.COLUMN_DEFAULTS={radio:!1,checkbox:!1,checkboxEnabled:!0,field:void 0,title:void 0,titleTooltip:void 0,class:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,width:void 0,sortable:!1,order:"asc",visible:!0,switchable:!0,clickToSelect:!0,formatter:void 0,footerFormatter:void 0,events:void 0,sorter:void 0,sortName:void 0,cellStyle:void 0,searchable:!0,searchFormatter:!0,cardVisible:!0,escape:!1},d.EVENTS={"all.bs.table":"onAll","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh"},d.prototype.init=function(){this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initFooter(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()},d.prototype.initLocale=function(){var e;this.options.locale&&((e=this.options.locale.split(/-|_/))[0].toLowerCase(),e[1]&&e[1].toUpperCase(),t.fn.bootstrapTable.locales[this.options.locale]?t.extend(this.options,t.fn.bootstrapTable.locales[this.options.locale]):t.fn.bootstrapTable.locales[e.join("-")]?t.extend(this.options,t.fn.bootstrapTable.locales[e.join("-")]):t.fn.bootstrapTable.locales[e[0]]&&t.extend(this.options,t.fn.bootstrapTable.locales[e[0]]))},d.prototype.initContainer=function(){this.$container=t(['<div class="bootstrap-table">','<div class="fixed-table-toolbar"></div>',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination" style="clear: both;"></div>':"",'<div class="fixed-table-container">','<div class="fixed-table-header"><table></table></div>','<div class="fixed-table-body">','<div class="fixed-table-loading">',this.options.formatLoadingMessage(),"</div>","</div>",'<div class="fixed-table-footer"><table><tr></tr></table></div>',"bottom"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination"></div>':"","</div>","</div>"].join("")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$container.find(".fixed-table-footer"),this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),-1!==t.inArray("table-no-bordered",this.options.classes.split(" "))&&this.$tableContainer.addClass("table-no-bordered")},d.prototype.initTable=function(){for(var e,i,n,o=this,a=[],s=[],l=(this.$header=this.$el.find(">thead"),this.$header.length||(this.$header=t("<thead></thead>").appendTo(this.$el)),this.$header.find("tr").each(function(){var e=[];t(this).find("th").each(function(){void 0!==t(this).data("field")&&t(this).data("field",t(this).data("field")+""),e.push(t.extend({},{title:t(this).html(),class:t(this).attr("class"),titleTooltip:t(this).attr("title"),rowspan:t(this).attr("rowspan")?+t(this).attr("rowspan"):void 0,colspan:t(this).attr("colspan")?+t(this).attr("colspan"):void 0},t(this).data()))}),a.push(e)}),t.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=t.extend(!0,[],a,this.options.columns),this.columns=[],this.options.columns),c=0,u=[],h=0;h<l[0].length;h++)c+=l[0][h].colspan||1;for(h=0;h<l.length;h++)for(u[h]=[],i=0;i<c;i++)u[h][i]=!1;for(h=0;h<l.length;h++)for(i=0;i<l[h].length;i++){var p=l[h][i],f=p.rowspan||1,m=p.colspan||1,g=t.inArray(!1,u[h]);for(1===m&&(p.fieldIndex=g,void 0===p.field)&&(p.field=g),n=0;n<f;n++)u[h+n][g]=!0;for(n=0;n<m;n++)u[h][g+n]=!0}t.each(this.options.columns,function(e,i){t.each(i,function(i,n){void 0!==(n=t.extend({},d.COLUMN_DEFAULTS,n)).fieldIndex&&(o.columns[n.fieldIndex]=n),o.options.columns[e][i]=n})}),this.options.data.length||(e=[],this.$el.find(">tbody>tr").each(function(i){var n={};n._id=t(this).attr("id"),n._class=t(this).attr("class"),n._data=r(t(this).data()),t(this).find(">td").each(function(a){for(var s,l,c=t(this),d=+c.attr("colspan")||1,u=+c.attr("rowspan")||1;e[i]&&e[i][a];a++);for(s=a;s<a+d;s++)for(l=i;l<i+u;l++)e[l]||(e[l]=[]),e[l][s]=!0;c=o.columns[a].field,n[c]=t(this).html(),n["_"+c+"_id"]=t(this).attr("id"),n["_"+c+"_class"]=t(this).attr("class"),n["_"+c+"_rowspan"]=t(this).attr("rowspan"),n["_"+c+"_colspan"]=t(this).attr("colspan"),n["_"+c+"_title"]=t(this).attr("title"),n["_"+c+"_data"]=r(t(this).data())}),s.push(n)}),(this.options.data=s).length&&(this.fromHtml=!0))},d.prototype.initHeader=function(){var i=this,n={},o=[];this.header={fields:[],styles:[],classes:[],formatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},t.each(this.options.columns,function(a,r){o.push("<tr>"),0===a&&!i.options.cardView&&i.options.detailView&&o.push(e('<th class="detail" rowspan="%s"><div class="fht-cell"></div></th>',i.options.columns.length)),t.each(r,function(t,a){var r,l,c,d="",u=e(' class="%s"',a.class),h=(i.options.sortOrder||a.order,"px"),p=a.width;if(void 0===a.width||i.options.cardView||"string"==typeof a.width&&-1!==a.width.indexOf("%")&&(h="%"),a.width&&"string"==typeof a.width&&(p=a.width.replace("%","").replace("px","")),r=e("text-align: %s; ",a.halign||a.align),l=e("text-align: %s; ",a.align),c=e("vertical-align: %s; ",a.valign),c+=e("width: %s; ",!a.checkbox&&!a.radio||p?p?p+h:void 0:"36px"),void 0!==a.fieldIndex){if(i.header.fields[a.fieldIndex]=a.field,i.header.styles[a.fieldIndex]=l+c,i.header.classes[a.fieldIndex]=u,i.header.formatters[a.fieldIndex]=a.formatter,i.header.events[a.fieldIndex]=a.events,i.header.sorters[a.fieldIndex]=a.sorter,i.header.sortNames[a.fieldIndex]=a.sortName,i.header.cellStyles[a.fieldIndex]=a.cellStyle,i.header.searchables[a.fieldIndex]=a.searchable,!a.visible)return;if(i.options.cardView&&!a.cardVisible)return;n[a.field]=a}o.push("<th"+e(' title="%s"',a.titleTooltip),a.checkbox||a.radio?e(' class="bs-checkbox %s"',a.class||""):u,e(' style="%s"',r+c),e(' rowspan="%s"',a.rowspan),e(' colspan="%s"',a.colspan),e(' data-field="%s"',a.field),">"),o.push(e('<div class="th-inner %s">',i.options.sortable&&a.sortable?"sortable both":"")),d=i.options.escape?s(a.title):a.title,a.checkbox&&(!i.options.singleSelect&&i.options.checkboxHeader&&(d='<input name="btSelectAll" type="checkbox" />'),i.header.stateField=a.field),a.radio&&(d="",i.header.stateField=a.field,i.options.singleSelect=!0),o.push(d),o.push("</div>"),o.push('<div class="fht-cell"></div>'),o.push("</div>"),o.push("</th>")}),o.push("</tr>")}),this.$header.html(o.join("")),this.$header.find("th[data-field]").each(function(e){t(this).data(n[t(this).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(e){var n=t(this);if(i.options.detailView&&n.closest(".bootstrap-table")[0]!==i.$container[0])return!1;i.options.sortable&&n.parent().data().sortable&&i.onSort(e)}),this.$header.children().children().off("keypress").on("keypress",function(e){i.options.sortable&&t(this).data().sortable&&13==(e.keyCode||e.which)&&i.onSort(e)}),t(window).off("resize.bootstrap-table"),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),t(window).on("resize.bootstrap-table",t.proxy(this.resetWidth,this))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(){var e=t(this).prop("checked");i[e?"checkAll":"uncheckAll"](),i.updateSelected()})},d.prototype.initFooter=function(){!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()},d.prototype.initData=function(t,e){this.data="append"===e?this.data.concat(t):"prepend"===e?[].concat(t).concat(this.data):t||this.options.data,this.options.data="append"===e?this.options.data.concat(t):"prepend"===e?[].concat(t).concat(this.options.data):this.data,"server"!==this.options.sidePagination&&this.initSort()},d.prototype.initSort=function(){var i=this,n=this.options.sortName,a="desc"===this.options.sortOrder?-1:1,s=t.inArray(this.options.sortName,this.header.fields);this.options.customSort!==t.noop?this.options.customSort.apply(this,[this.options.sortName,this.options.sortOrder]):-1!==s&&(this.options.sortStable&&t.each(this.data,function(t,e){e.hasOwnProperty("_position")||(e._position=t)}),this.data.sort(function(e,r){i.header.sortNames[s]&&(n=i.header.sortNames[s]);var c=l(e,n,i.options.escape),d=l(r,n,i.options.escape),u=o(i.header,i.header.sorters[s],[c,d]);return void 0!==u?a*u:(null==c&&(c=""),null==d&&(d=""),i.options.sortStable&&c===d&&(c=e._position,d=r._position),t.isNumeric(c)&&t.isNumeric(d)?(c=parseFloat(c))<(d=parseFloat(d))?-1*a:a:c===d?0:-1===(c="string"!=typeof c?c.toString():c).localeCompare(d)?-1*a:a)}),void 0!==this.options.sortClass)&&(clearTimeout(0),setTimeout(function(){i.$el.removeClass(i.options.sortClass);var t=i.$header.find(e('[data-field="%s"]',i.options.sortName).index()+1);i.$el.find(e("tr td:nth-child(%s)",t)).addClass(i.options.sortClass)},250))},d.prototype.onSort=function(e){var e="keypress"===e.type?t(e.currentTarget):t(e.currentTarget).parent(),i=this.$header.find("th").eq(e.index());this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===e.data("field")?this.options.sortOrder="asc"===this.options.sortOrder?"desc":"asc":(this.options.sortName=e.data("field"),this.options.sortOrder="asc"===e.data("order")?"desc":"asc"),this.trigger("sort",this.options.sortName,this.options.sortOrder),e.add(i).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination?this.initServer(this.options.silentSort):(this.initSort(),this.initBody())},d.prototype.initToolbar=function(){var i,n=this,a=[],s=0,r=0;this.$toolbar.find(".bs-bars").children().length&&t("body").append(t(this.options.toolbar)),this.$toolbar.html(""),"string"!=typeof this.options.toolbar&&"object"!=typeof this.options.toolbar||t(e('<div class="bs-bars pull-%s"></div>',this.options.toolbarAlign)).appendTo(this.$toolbar).append(t(this.options.toolbar)),a=[e('<div class="columns columns-%s btn-group pull-%s">',this.options.buttonsAlign,this.options.buttonsAlign)],"string"==typeof this.options.icons&&(this.options.icons=o(null,this.options.icons)),
this.options.showPaginationSwitch&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="paginationSwitch" aria-label="pagination Switch" title="%s">',this.options.formatPaginationSwitch()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.paginationSwitchDown),"</button>"),this.options.showRefresh&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="refresh" aria-label="refresh" title="%s">',this.options.formatRefresh()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.refresh),"</button>"),this.options.showToggle&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="toggle" aria-label="toggle" title="%s">',this.options.formatToggle()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.toggle),"</button>"),this.options.showColumns&&(a.push(e('<div class="keep-open btn-group" title="%s">',this.options.formatColumns()),'<button type="button" aria-label="columns" class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">',e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.columns),' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'),t.each(this.columns,function(t,i){var o;i.radio||i.checkbox||n.options.cardView&&!i.cardVisible||(o=i.visible?' checked="checked"':"",i.switchable&&(a.push(e('<li role="menuitem"><label><input type="checkbox" data-field="%s" value="%s"%s> %s</label></li>',i.field,t,o,i.title)),r++))}),a.push("</ul>","</div>")),a.push("</div>"),(this.showToolbar||2<a.length)&&this.$toolbar.append(a.join("")),this.options.showPaginationSwitch&&this.$toolbar.find('button[name="paginationSwitch"]').off("click").on("click",t.proxy(this.togglePagination,this)),this.options.showRefresh&&this.$toolbar.find('button[name="refresh"]').off("click").on("click",t.proxy(this.refresh,this)),this.options.showToggle&&this.$toolbar.find('button[name="toggle"]').off("click").on("click",function(){n.toggleView()}),this.options.showColumns&&(i=this.$toolbar.find(".keep-open"),r<=this.options.minimumCountColumns&&i.find("input").prop("disabled",!0),i.find("li").off("click").on("click",function(t){t.stopImmediatePropagation()}),i.find("input").off("click").on("click",function(){var e=t(this);n.toggleColumn(t(this).val(),e.prop("checked"),!1),n.trigger("column-switch",t(this).data("field"),e.prop("checked"))})),this.options.search&&((a=[]).push('<div class="pull-'+this.options.searchAlign+' search">',e('<input class="form-control'+e(" input-%s",this.options.iconSize)+'" type="text" placeholder="%s">',this.options.formatSearch()),"</div>"),this.$toolbar.append(a.join("")),(i=this.$toolbar.find(".search input")).off("keyup drop blur").on("keyup drop blur",function(e){n.options.searchOnEnterKey&&13!==e.keyCode||-1<t.inArray(e.keyCode,[37,38,39,40])||(clearTimeout(s),s=setTimeout(function(){n.onSearch(e)},n.options.searchTimeOut))}),c())&&i.off("mouseup").on("mouseup",function(t){clearTimeout(s),s=setTimeout(function(){n.onSearch(t)},n.options.searchTimeOut)})},d.prototype.onSearch=function(e){var i=t.trim(t(e.currentTarget).val());this.options.trimOnSearch&&t(e.currentTarget).val()!==i&&t(e.currentTarget).val(i),i===this.searchText||""===i&&void 0===this.searchText||(this.searchText=i,this.options.searchText=i,this.options.pageNumber=1,this.initSearch(),this.updatePagination(),this.trigger("search",i))},d.prototype.initSearch=function(){var e,n,a=this;"server"!==this.options.sidePagination&&(this.options.customSearch!==t.noop?this.options.customSearch.apply(this,[this.searchText]):(e=this.searchText&&(this.options.escape?s(this.searchText):this.searchText).toLowerCase(),n=t.isEmptyObject(this.filterColumns)?null:this.filterColumns,this.data=n?t.grep(this.options.data,function(e,i){for(var o in n)if(t.isArray(n[o])&&-1===t.inArray(e[o],n[o])||!t.isArray(n[o])&&e[o]!==n[o])return!1;return!0}):this.options.data,this.data=e?t.grep(this.data,function(n,s){for(var r=0;r<a.header.fields.length;r++)if(a.header.searchables[r]){var l=t.isNumeric(a.header.fields[r])?parseInt(a.header.fields[r],10):a.header.fields[r],c=a.columns[i(a.columns,l)];if("string"==typeof l){for(var d=n,u=l.split("."),h=0;h<u.length;h++)d=d[u[h]];c&&c.searchFormatter&&(d=o(c,a.header.formatters[r],[d,n,s],d))}else d=n[l];if("string"==typeof d||"number"==typeof d)if(a.options.strictSearch){if((d+"").toLowerCase()===e)return!0}else if(-1!==(d+"").toLowerCase().indexOf(e))return!0}return!1}):this.data))},d.prototype.initPagination=function(){if(this.options.pagination){this.$pagination.show();var i,n,o,a,s,r,l,c=this,d=[],u=!1,h=this.getData(),p=this.options.pageList;if("server"!==this.options.sidePagination&&(this.options.totalRows=h.length),this.totalPages=0,this.options.totalRows&&(this.options.pageSize===this.options.formatAllRows()?(this.options.pageSize=this.options.totalRows,u=!0):this.options.pageSize===this.options.totalRows&&(h="string"==typeof this.options.pageList?this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.pageList,-1<t.inArray(this.options.formatAllRows().toLowerCase(),h))&&(u=!0),this.totalPages=1+~~((this.options.totalRows-1)/this.options.pageSize),this.options.totalPages=this.totalPages),0<this.totalPages&&this.options.pageNumber>this.totalPages&&(this.options.pageNumber=this.totalPages),this.pageFrom=(this.options.pageNumber-1)*this.options.pageSize+1,this.pageTo=this.options.pageNumber*this.options.pageSize,this.pageTo>this.options.totalRows&&(this.pageTo=this.options.totalRows),d.push('<div class="pull-'+this.options.paginationDetailHAlign+' pagination-detail">','<span class="pagination-info">',this.options.onlyInfoPagination?this.options.formatDetailPagination(this.options.totalRows):this.options.formatShowingRows(this.pageFrom,this.pageTo,this.options.totalRows),"</span>"),!this.options.onlyInfoPagination){d.push('<span class="page-list">');var f=[e('<span class="btn-group %s">',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?"dropdown":"dropup"),'<button type="button" class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">','<span class="page-size">',u?this.options.formatAllRows():this.options.pageSize,"</span>",' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'];for("string"==typeof this.options.pageList&&(h=this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").split(","),p=[],t.each(h,function(t,e){p.push(e.toUpperCase()===c.options.formatAllRows().toUpperCase()?c.options.formatAllRows():+e)})),t.each(p,function(t,i){(!c.options.smartDisplay||0===t||p[t-1]<c.options.totalRows)&&(t=u?i===c.options.formatAllRows()?' class="active"':"":i===c.options.pageSize?' class="active"':"",f.push(e('<li role="menuitem"%s><a href="#">%s</a></li>',t,i)))}),f.push("</ul></span>"),d.push(this.options.formatRecordsPerPage(f.join(""))),d.push("</span>"),d.push("</div>",'<div class="pull-'+this.options.paginationHAlign+' pagination">','<ul class="pagination'+e(" pagination-%s",this.options.iconSize)+'">','<li class="page-pre"><a href="#">'+this.options.paginationPreText+"</a></li>"),this.totalPages<5?(o=1,n=this.totalPages):(n=4+(o=this.options.pageNumber-2),o<1&&(o=1,n=5),n>this.totalPages&&(o=(n=this.totalPages)-4)),6<=this.totalPages&&(3<=this.options.pageNumber&&(d.push('<li class="page-first'+(1===this.options.pageNumber?" active":"")+'">','<a href="#">',1,"</a>","</li>"),o++),4<=this.options.pageNumber)&&(4==this.options.pageNumber||6==this.totalPages||7==this.totalPages?o--:d.push('<li class="page-first-separator disabled">','<a href="#">...</a>',"</li>"),n--),7<=this.totalPages&&this.options.pageNumber>=this.totalPages-2&&o--,6==this.totalPages?this.options.pageNumber>=this.totalPages-2&&n++:7<=this.totalPages&&(7==this.totalPages||this.options.pageNumber>=this.totalPages-3)&&n++,i=o;i<=n;i++)d.push('<li class="page-number'+(i===this.options.pageNumber?" active":"")+'">','<a href="#">',i,"</a>","</li>");8<=this.totalPages&&this.options.pageNumber<=this.totalPages-4&&d.push('<li class="page-last-separator disabled">','<a href="#">...</a>',"</li>"),6<=this.totalPages&&this.options.pageNumber<=this.totalPages-3&&d.push('<li class="page-last'+(this.totalPages===this.options.pageNumber?" active":"")+'">','<a href="#">',this.totalPages,"</a>","</li>"),d.push('<li class="page-next"><a href="#">'+this.options.paginationNextText+"</a></li>","</ul>","</div>")}this.$pagination.html(d.join("")),this.options.onlyInfoPagination||(h=this.$pagination.find(".page-list a"),o=this.$pagination.find(".page-first"),a=this.$pagination.find(".page-pre"),s=this.$pagination.find(".page-next"),r=this.$pagination.find(".page-last"),l=this.$pagination.find(".page-number"),this.options.smartDisplay&&(this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),(p.length<2||this.options.totalRows<=p[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"]()),this.options.paginationLoop||(1===this.options.pageNumber&&a.addClass("disabled"),this.options.pageNumber===this.totalPages&&s.addClass("disabled")),u&&(this.options.pageSize=this.options.formatAllRows()),h.off("click").on("click",t.proxy(this.onPageListChange,this)),o.off("click").on("click",t.proxy(this.onPageFirst,this)),a.off("click").on("click",t.proxy(this.onPagePre,this)),s.off("click").on("click",t.proxy(this.onPageNext,this)),r.off("click").on("click",t.proxy(this.onPageLast,this)),l.off("click").on("click",t.proxy(this.onPageNumber,this)))}else this.$pagination.hide()},d.prototype.updatePagination=function(e){e&&t(e.currentTarget).hasClass("disabled")||(this.options.maintainSelected||this.resetRows(),this.initPagination(),"server"===this.options.sidePagination?this.initServer():this.initBody(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize))},d.prototype.onPageListChange=function(e){var i=t(e.currentTarget);return i.parent().addClass("active").siblings().removeClass("active"),this.options.pageSize=i.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+i.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(e),!1},d.prototype.onPageFirst=function(t){return this.options.pageNumber=1,this.updatePagination(t),!1},d.prototype.onPagePre=function(t){return this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1},d.prototype.onPageNext=function(t){return this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1},d.prototype.onPageLast=function(t){return this.options.pageNumber=this.totalPages,this.updatePagination(t),!1},d.prototype.onPageNumber=function(e){if(this.options.pageNumber!==+t(e.currentTarget).text())return this.options.pageNumber=+t(e.currentTarget).text(),this.updatePagination(e),!1},d.prototype.initRow=function(i,n,a,r){var c,d=this,u=[],h={},p=[],f="",m={},g=[];if(!(-1<t.inArray(i,this.hiddenRows))){if((h=o(this.options,this.options.rowStyle,[i,n],h))&&h.css)for(c in h.css)p.push(c+": "+h.css[c]);if(m=o(this.options,this.options.rowAttributes,[i,n],m))for(c in m)g.push(e('%s="%s"',c,s(m[c])));return i._data&&!t.isEmptyObject(i._data)&&t.each(i._data,function(t,i){"index"!==t&&(f+=e(' data-%s="%s"',t,i))}),u.push("<tr",e(" %s",g.join(" ")),e(' id="%s"',t.isArray(i)?void 0:i._id),e(' class="%s"',h.classes||(t.isArray(i)?void 0:i._class)),e(' data-index="%s"',n),e(' data-uniqueid="%s"',i[this.options.uniqueId]),e("%s",f),">"),this.options.cardView&&u.push(e('<td colspan="%s"><div class="card-views">',this.header.fields.length)),!this.options.cardView&&this.options.detailView&&u.push("<td>",'<a class="detail-icon" href="#">',e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.detailOpen),"</a>","</td>"),t.each(this.header.fields,function(a,r){var c="",f=l(i,r,d.options.escape),m="",g={},v="",y=d.header.classes[a],b="",x="",w="",k="",_=d.columns[a];if((!d.fromHtml||void 0!==f)&&_.visible&&(!d.options.cardView||_.cardVisible)){if(_.escape&&(f=s(f)),h=e('style="%s"',p.concat(d.header.styles[a]).join("; ")),i["_"+r+"_id"]&&(v=e(' id="%s"',i["_"+r+"_id"])),i["_"+r+"_class"]&&(y=e(' class="%s"',i["_"+r+"_class"])),i["_"+r+"_rowspan"]&&(x=e(' rowspan="%s"',i["_"+r+"_rowspan"])),i["_"+r+"_colspan"]&&(w=e(' colspan="%s"',i["_"+r+"_colspan"])),i["_"+r+"_title"]&&(k=e(' title="%s"',i["_"+r+"_title"])),(g=o(d.header,d.header.cellStyles[a],[f,i,n,r],g)).classes&&(y=e(' class="%s"',g.classes)),g.css){var C,S=[];for(C in g.css)S.push(C+": "+g.css[C]);h=e('style="%s"',S.concat(d.header.styles[a]).join("; "))}var T,D,$,E,A,m=o(_,d.header.formatters[a],[f,i,n],f);i["_"+r+"_data"]&&!t.isEmptyObject(i["_"+r+"_data"])&&t.each(i["_"+r+"_data"],function(t,i){"index"!==t&&(b+=e(' data-%s="%s"',t,i))}),_.checkbox||_.radio?(T=_.checkbox?"checkbox":"",T=_.radio?"radio":T,c=[e(d.options.cardView?'<div class="card-view %s">':'<td class="bs-checkbox %s">',_.class||""),"<input"+e(' data-index="%s"',n)+e(' name="%s"',d.options.selectItemName)+e(' type="%s"',T)+e(' value="%s"',i[d.options.idField])+e(' checked="%s"',!0===m||f||m&&m.checked?"checked":void 0)+e(' disabled="%s"',!_.checkboxEnabled||m&&m.disabled?"disabled":void 0)+" />",d.header.formatters[a]&&"string"==typeof m?m:"",d.options.cardView?"</div>":"</td>"].join(""),i[d.header.stateField]=!0===m||m&&m.checked):(m=null==m?d.options.undefinedText:m,c=(d.options.cardView?['<div class="card-view">',d.options.showHeader?e('<span class="title" %s>%s</span>',h,(T=d.columns,D="field",$="title",E=r,A="",t.each(T,function(t,e){return e[D]!==E||(A=e[$],!1)}),A)):"",e('<span class="value">%s</span>',m),"</div>"]:[e("<td%s %s %s %s %s %s %s>",v,y,h,b,x,w,k),m,"</td>"]).join(""),d.options.cardView&&d.options.smartDisplay&&""===m&&(c='<div class="card-view"></div>')),u.push(c)}}),this.options.cardView&&u.push("</div></td>"),u.push("</tr>"),u.join(" ")}},d.prototype.initBody=function(n){for(var a=this,s=this.getData(),r=(this.trigger("pre-body",s),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=t("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=s.length),t(document.createDocumentFragment())),c=this.pageFrom-1;c<this.pageTo;c++){var d=s[c],d=this.initRow(d,c,s,r),u=u||!!d;d&&!0!==d&&r.append(d)}u||r.append('<tr class="no-records-found">'+e('<td colspan="%s">%s</td>',this.$header.find("th").length,this.options.formatNoMatches())+"</tr>"),this.$body.html(r),n||this.scrollTo(0),this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(n){var o=t(this),s=o.parent(),r=a.data[s.data("index")],c=o[0].cellIndex,c=a.getVisibleFields()[a.options.detailView&&!a.options.cardView?c-1:c],d=a.columns[i(a.columns,c)],u=l(r,c,a.options.escape);o.find(".detail-icon").length||(a.trigger("click"===n.type?"click-cell":"dbl-click-cell",c,u,r,o),a.trigger("click"===n.type?"click-row":"dbl-click-row",r,s,c),"click"===n.type&&a.options.clickToSelect&&d.clickToSelect&&(u=s.find(e('[name="%s"]',a.options.selectItemName))).length&&u[0].click())}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(){var i=t(this),n=i.parent().parent(),r=n.data("index"),l=s[r];return n.next().is("tr.detail-view")?(i.find("i").attr("class",e("%s %s",a.options.iconsPrefix,a.options.icons.detailOpen)),a.trigger("collapse-row",r,l),n.next().remove()):(i.find("i").attr("class",e("%s %s",a.options.iconsPrefix,a.options.icons.detailClose)),n.after(e('<tr class="detail-view"><td colspan="%s"></td></tr>',n.find("td").length)),i=n.next().find("td"),n=o(a.options,a.options.detailFormatter,[r,l,i],""),1===i.length&&i.append(n),a.trigger("expand-row",r,l,i)),a.resetView(),!1}),this.$selectItem=this.$body.find(e('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(e){e.stopImmediatePropagation();var e=t(this),i=e.prop("checked"),n=a.data[e.data("index")];a.options.maintainSelected&&t(this).is(":radio")&&t.each(a.options.data,function(t,e){e[a.header.stateField]=!1}),n[a.header.stateField]=i,a.options.singleSelect&&(a.$selectItem.not(this).each(function(){a.data[t(this).data("index")][a.header.stateField]=!1}),a.$selectItem.filter(":checked").not(this).prop("checked",!1)),a.updateSelected(),a.trigger(i?"check":"uncheck",n,e)}),t.each(this.header.events,function(e,i){if(i){"string"==typeof i&&(i=o(null,i));var n,s=a.header.fields[e],r=t.inArray(s,a.getVisibleFields());for(n in a.options.detailView&&!a.options.cardView&&(r+=1),i)a.$body.find(">tr:not(.no-records-found)").each(function(){var e=t(this),o=e.find(a.options.cardView?".card-view":">td").eq(r),l=n.indexOf(" "),c=n.substring(0,l),l=n.substring(l+1),d=i[n];o.find(l).off(c).on(c,function(t){var i=e.data("index"),n=a.data[i],o=n[s],r=s.split(".");if(1<r.length)for(var o=n,l=0;l<r.length;l++)o=o[r[l]];d.apply(this,[t,o,n,i])})})}}),this.updateSelected(),this.resetView(),this.trigger("post-body",s)},d.prototype.initServer=function(e,i,n){var a=this,s={},r={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};this.options.pagination&&(r.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,r.pageNumber=this.options.pageNumber),(n||this.options.url||this.options.ajax)&&("limit"===this.options.queryParamsType&&(r={search:r.searchText,sort:r.sortName,order:r.sortOrder},this.options.pagination)&&(r.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),r.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize),t.isEmptyObject(this.filterColumnsPartial)||(r.filter=JSON.stringify(this.filterColumnsPartial,null)),s=o(this.options,this.options.queryParams,[r],s),t.extend(s,i||{}),!1!==s)&&(e||this.$tableLoading.show(),r=t.extend({},o(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(s):s,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t){t=o(a.options,a.options.responseHandler,[t],t),a.load(t),a.trigger("load-success",t),e||a.$tableLoading.hide()},error:function(t){a.trigger("load-error",t.status,t),e||a.$tableLoading.hide()}}),this.options.ajax?o(this,this.options.ajax,[r],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=t.ajax(r)))},d.prototype.initSearchText=function(){var t;this.options.search&&""!==this.options.searchText&&((t=this.$toolbar.find(".search input")).val(this.options.searchText),this.onSearch({currentTarget:t}))},d.prototype.getCaret=function(){var e=this;t.each(this.$header.find("th"),function(i,n){t(n).find(".sortable").removeClass("desc asc").addClass(t(n).data("field")===e.options.sortName?e.options.sortOrder:"both")})},d.prototype.updateSelected=function(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e),this.$selectItem.each(function(){t(this).closest("tr")[t(this).prop("checked")?"addClass":"removeClass"]("selected")})},d.prototype.updateRows=function(){var e=this;this.$selectItem.each(function(){e.data[t(this).data("index")][e.header.stateField]=t(this).prop("checked")})},d.prototype.resetRows=function(){var e=this;t.each(this.data,function(t,i){e.$selectAll.prop("checked",!1),e.$selectItem.prop("checked",!1),e.header.stateField&&(i[e.header.stateField]=!1)}),this.initHiddenRows()},d.prototype.trigger=function(e){var i=Array.prototype.slice.call(arguments,1);this.options[d.EVENTS[e+=".bs.table"]].apply(this.options,i),this.$el.trigger(t.Event(e),i),this.options.onAll(e,i),this.$el.trigger(t.Event("all.bs.table"),[e,i])},d.prototype.resetHeader=function(){clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(t.proxy(this.fitHeader,this),this.$el.is(":hidden")?100:0)},d.prototype.fitHeader=function(){var i,o,a,s,r=this;r.$el.is(":hidden")?r.timeoutId_=setTimeout(t.proxy(r.fitHeader,r),100):(o=(o=this.$tableBody.get(0)).scrollWidth>o.clientWidth&&o.scrollHeight>o.clientHeight+this.$header.outerHeight()?n():0,this.$el.css("margin-top",-this.$header.outerHeight()),0<(i=t(":focus")).length&&0<(i=i.parents("th")).length&&void 0!==(i=i.attr("data-field"))&&0<(i=this.$header.find("[data-field='"+i+"']")).length&&i.find(":input").addClass("focus-temp"),this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css({"margin-right":o}).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),0<(i=t(".focus-temp:visible:eq(0)")).length&&(i.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(i){r.$header_.find(e('th[data-field="%s"]',t(this).data("field"))).data(t(this).data())}),a=this.getVisibleFields(),s=this.$header_.find("th"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(i){var n=t(this),o=i,i=(r.options.detailView&&!r.options.cardView&&(0===i&&r.$header_.find("th.detail").find(".fht-cell").width(n.innerWidth()),o=i-1),r.$header_.find(e('th[data-field="%s"]',a[o])));(i=1<i.length?t(s[n[0].cellIndex]):i).find(".fht-cell").width(n.innerWidth())}),this.$tableBody.off("scroll").on("scroll",function(){r.$tableHeader.scrollLeft(t(this).scrollLeft()),r.options.showFooter&&!r.options.cardView&&r.$tableFooter.scrollLeft(t(this).scrollLeft())}),r.trigger("post-header"))},d.prototype.resetFooter=function(){var i=this,n=i.getData(),a=[];this.options.showFooter&&!this.options.cardView&&(!this.options.cardView&&this.options.detailView&&a.push('<td><div class="th-inner">&nbsp;</div><div class="fht-cell"></div></td>'),t.each(this.columns,function(t,s){var r,l,c,d,u=[],h=e(' class="%s"',s.class);if(s.visible&&(!i.options.cardView||s.cardVisible)){if(l=e("text-align: %s; ",s.falign||s.align),c=e("vertical-align: %s; ",s.valign),(d=o(null,i.options.footerStyle))&&d.css)for(r in d.css)u.push(r+": "+d.css[r]);a.push("<td",h,e(' style="%s"',l+c+u.concat().join("; ")),">"),a.push('<div class="th-inner">'),a.push(o(s,s.footerFormatter,[n],"&nbsp;")||"&nbsp;"),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</td>")}}),this.$tableFooter.find("tr").html(a.join("")),this.$tableFooter.show(),clearTimeout(this.timeoutFooter_),this.timeoutFooter_=setTimeout(t.proxy(this.fitFooter,this),this.$el.is(":hidden")?100:0))},d.prototype.fitFooter=function(){var e,i,o;clearTimeout(this.timeoutFooter_),this.$el.is(":hidden")?this.timeoutFooter_=setTimeout(t.proxy(this.fitFooter,this),100):(o=(i=this.$el.css("width"))>this.$tableBody.width()?n():0,this.$tableFooter.css({"margin-right":o}).find("table").css("width",i).attr("class",this.$el.attr("class")),e=this.$tableFooter.find("td"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(i){var n=t(this);e.eq(i).find(".fht-cell").width(n.innerWidth())}))},d.prototype.toggleColumn=function(t,i,n){var o;-1!==t&&(this.columns[t].visible=i,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)&&(o=this.$toolbar.find(".keep-open input").prop("disabled",!1),n&&o.filter(e('[value="%s"]',t)).prop("checked",i),o.filter(":checked").length<=this.options.minimumCountColumns)&&o.filter(":checked").prop("disabled",!0)},d.prototype.getVisibleFields=function(){var e=this,n=[];return t.each(this.header.fields,function(t,o){e.columns[i(e.columns,o)].visible&&n.push(o)}),n},d.prototype.resetView=function(t){var e,i=0;t&&t.height&&(this.options.height=t.height),this.$selectAll.prop("checked",0<this.$selectItem.length&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.options.height&&(t=this.$toolbar.outerHeight(!0),e=this.$pagination.outerHeight(!0),t=this.options.height-t-e,this.$tableContainer.css("height",t+"px")),this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),i+=this.$header.outerHeight()):(this.$tableHeader.hide(),this.trigger("post-header")),this.options.showFooter&&(this.resetFooter(),this.options.height)&&(i+=this.$tableFooter.outerHeight()+1),this.getCaret(),this.$tableContainer.css("padding-bottom",i+"px"),this.trigger("reset-view"))},d.prototype.getData=function(e){return!this.searchText&&t.isEmptyObject(this.filterColumns)&&t.isEmptyObject(this.filterColumnsPartial)?e?this.options.data.slice(this.pageFrom-1,this.pageTo):this.options.data:e?this.data.slice(this.pageFrom-1,this.pageTo):this.data},d.prototype.load=function(e){var i=!1;"server"===this.options.sidePagination?(this.options.totalRows=e[this.options.totalField],i=e.fixedScroll,e=e[this.options.dataField]):t.isArray(e)||(i=e.fixedScroll,e=e.data),this.initData(e),this.initSearch(),this.initPagination(),this.initBody(i)},d.prototype.append=function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.prepend=function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.remove=function(e){var i,n,o=this.options.data.length;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){for(i=o-1;0<=i;i--)(n=this.options.data[i]).hasOwnProperty(e.field)&&-1!==t.inArray(n[e.field],e.values)&&(this.options.data.splice(i,1),"server"===this.options.sidePagination)&&--this.options.totalRows;o!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},d.prototype.removeAll=function(){0<this.options.data.length&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))},d.prototype.getRowByUniqueId=function(t){for(var e,i,n=this.options.uniqueId,o=null,a=this.options.data.length-1;0<=a;a--){if((e=this.options.data[a]).hasOwnProperty(n))i=e[n];else{if(!e._data.hasOwnProperty(n))continue;i=e._data[n]}if("string"==typeof i?t=t.toString():"number"==typeof i&&(Number(i)===i&&i%1==0?t=parseInt(t):i===Number(i)&&0!==i&&(t=parseFloat(t))),i===t){o=e;break}}return o},d.prototype.removeByUniqueId=function(t){var e=this.options.data.length,t=this.getRowByUniqueId(t);t&&this.options.data.splice(this.options.data.indexOf(t),1),e!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))},d.prototype.updateByUniqueId=function(e){var i=this,e=t.isArray(e)?e:[e];t.each(e,function(e,n){var o;n.hasOwnProperty("id")&&n.hasOwnProperty("row")&&-1!==(o=t.inArray(i.getRowByUniqueId(n.id),i.options.data))&&t.extend(i.options.data[o],n.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.insertRow=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))},d.prototype.updateRow=function(e){var i=this,e=t.isArray(e)?e:[e];t.each(e,function(e,n){n.hasOwnProperty("index")&&n.hasOwnProperty("row")&&t.extend(i.options.data[n.index],n.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.initHiddenRows=function(){this.hiddenRows=[]},d.prototype.showRow=function(t){this.toggleRow(t,!0)},d.prototype.hideRow=function(t){this.toggleRow(t,!1)},d.prototype.toggleRow=function(e,i){var n;e.hasOwnProperty("index")?n=this.getData()[e.index]:e.hasOwnProperty("uniqueId")&&(n=this.getRowByUniqueId(e.uniqueId)),n&&(e=t.inArray(n,this.hiddenRows),i||-1!==e?i&&-1<e&&this.hiddenRows.splice(e,1):this.hiddenRows.push(n),this.initBody(!0))},d.prototype.getHiddenRows=function(e){var i=this,n=this.getData(),o=[];return t.each(n,function(e,n){-1<t.inArray(n,i.hiddenRows)&&o.push(n)}),this.hiddenRows=o},d.prototype.mergeCells=function(e){var i,n,o=e.index,a=t.inArray(e.field,this.getVisibleFields()),s=e.rowspan||1,r=e.colspan||1,l=this.$body.find(">tr");if(this.options.detailView&&!this.options.cardView&&(a+=1),e=l.eq(o).find(">td").eq(a),!(o<0||a<0||o>=this.data.length)){for(i=o;i<o+s;i++)for(n=a;n<a+r;n++)l.eq(i).find(">td").eq(n).hide();e.attr("rowspan",s).attr("colspan",r).show()}},d.prototype.updateCell=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,!1!==t.reinit)&&(this.initSort(),this.initBody(!0))},d.prototype.getOptions=function(){return this.options},d.prototype.getSelections=function(){var e=this;return t.grep(this.options.data,function(t){return!0===t[e.header.stateField]})},d.prototype.getAllSelections=function(){var e=this;return t.grep(this.options.data,function(t){return t[e.header.stateField]})},d.prototype.checkAll=function(){this.checkAll_(!0)},d.prototype.uncheckAll=function(){this.checkAll_(!1)},d.prototype.checkInvert=function(){var e=this,i=e.$selectItem.filter(":enabled"),n=i.filter(":checked");i.each(function(){t(this).prop("checked",!t(this).prop("checked"))}),e.updateRows(),e.updateSelected(),e.trigger("uncheck-some",n),n=e.getSelections(),e.trigger("check-some",n)},d.prototype.checkAll_=function(t){var e;t||(e=this.getSelections()),this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),t&&(e=this.getSelections()),this.trigger(t?"check-all":"uncheck-all",e)},d.prototype.check=function(t){this.check_(!0,t)},d.prototype.uncheck=function(t){this.check_(!1,t)},d.prototype.check_=function(t,i){var n=this.$selectItem.filter(e('[data-index="%s"]',i)).prop("checked",t);this.data[i][this.header.stateField]=t,this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[i],n)},d.prototype.checkBy=function(t){this.checkBy_(!0,t)},d.prototype.uncheckBy=function(t){this.checkBy_(!1,t)},d.prototype.checkBy_=function(i,n){var o,a;n.hasOwnProperty("field")&&n.hasOwnProperty("values")&&(a=[],t.each((o=this).options.data,function(s,r){if(!r.hasOwnProperty(n.field))return!1;-1!==t.inArray(r[n.field],n.values)&&(s=o.$selectItem.filter(":enabled").filter(e('[data-index="%s"]',s)).prop("checked",i),r[o.header.stateField]=i,a.push(r),o.trigger(i?"check":"uncheck",r,s))}),this.updateSelected(),this.trigger(i?"check-some":"uncheck-some",a))},d.prototype.destroy=function(){this.$el.insertBefore(this.$container),t(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")},d.prototype.showLoading=function(){this.$tableLoading.show()},d.prototype.hideLoading=function(){this.$tableLoading.hide()},d.prototype.togglePagination=function(){this.options.pagination=!this.options.pagination;var t=this.$toolbar.find('button[name="paginationSwitch"] i');this.options.pagination?t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchDown):t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchUp),this.updatePagination()},d.prototype.refresh=function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),
this.initServer(t&&t.silent,t&&t.query,t&&t.url),this.trigger("refresh",t)},d.prototype.resetWidth=function(){this.options.showHeader&&this.options.height&&this.fitHeader(),this.options.showFooter&&this.fitFooter()},d.prototype.showColumn=function(t){this.toggleColumn(i(this.columns,t),!0,!0)},d.prototype.hideColumn=function(t){this.toggleColumn(i(this.columns,t),!1,!0)},d.prototype.getHiddenColumns=function(){return t.grep(this.columns,function(t){return!t.visible})},d.prototype.getVisibleColumns=function(){return t.grep(this.columns,function(t){return t.visible})},d.prototype.toggleAllColumns=function(e){var i;t.each(this.columns,function(t,i){this.columns[t].visible=e}),this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns&&(i=this.$toolbar.find(".keep-open input").prop("disabled",!1)).filter(":checked").length<=this.options.minimumCountColumns&&i.filter(":checked").prop("disabled",!0)},d.prototype.showAllColumns=function(){this.toggleAllColumns(!0)},d.prototype.hideAllColumns=function(){this.toggleAllColumns(!1)},d.prototype.filterBy=function(e){this.filterColumns=t.isEmptyObject(e)?{}:e,this.options.pageNumber=1,this.initSearch(),this.updatePagination()},d.prototype.scrollTo=function(t){if("number"==typeof(t="string"==typeof t?"bottom"===t?this.$tableBody[0].scrollHeight:0:t)&&this.$tableBody.scrollTop(t),void 0===t)return this.$tableBody.scrollTop()},d.prototype.getScrollPosition=function(){return this.scrollTo()},d.prototype.selectPage=function(t){0<t&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())},d.prototype.prevPage=function(){1<this.options.pageNumber&&(this.options.pageNumber--,this.updatePagination())},d.prototype.nextPage=function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())},d.prototype.toggleView=function(){this.options.cardView=!this.options.cardView,this.initHeader(),this.initBody(),this.trigger("toggle",this.options.cardView)},d.prototype.refreshOptions=function(e){a(this.options,e,!0)||(this.options=t.extend(this.options,e),this.trigger("refresh-options",this.options),this.destroy(),this.init())},d.prototype.resetSearch=function(t){var e=this.$toolbar.find(".search input");e.val(t||""),this.onSearch({currentTarget:e})},d.prototype.expandRow_=function(t,i){i=this.$body.find(e('> tr[data-index="%s"]',i)),i.next().is("tr.detail-view")===!t&&i.find("> td > .detail-icon").click()},d.prototype.expandRow=function(t){this.expandRow_(!0,t)},d.prototype.collapseRow=function(t){this.expandRow_(!1,t)},d.prototype.expandAllRows=function(i){if(i){var i=this.$body.find(e('> tr[data-index="%s"]',0)),n=this,o=null,a=!1,s=-1;if(i.next().is("tr.detail-view")?i.next().next().is("tr.detail-view")||(i.next().find(".detail-icon").click(),a=!0):(i.find("> td > .detail-icon").click(),a=!0),a)try{s=setInterval(function(){0<(o=n.$body.find("tr.detail-view").last().find(".detail-icon")).length?o.click():clearInterval(s)},1)}catch(i){clearInterval(s)}}else for(var r=this.$body.children(),l=0;l<r.length;l++)this.expandRow_(!0,t(r[l]).data("index"))},d.prototype.collapseAllRows=function(e){if(e)this.expandRow_(!1,0);else for(var i=this.$body.children(),n=0;n<i.length;n++)this.expandRow_(!1,t(i[n]).data("index"))},d.prototype.updateFormatText=function(t,i){this.options[e("format%s",t)]&&("string"==typeof i?this.options[e("format%s",t)]=function(){return i}:"function"==typeof i&&(this.options[e("format%s",t)]=i)),this.initToolbar(),this.initPagination(),this.initBody()},["getOptions","getSelections","getAllSelections","getData","load","append","prepend","remove","removeAll","insertRow","updateRow","updateCell","updateByUniqueId","removeByUniqueId","getRowByUniqueId","showRow","hideRow","getHiddenRows","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","resetView","resetWidth","destroy","showLoading","hideLoading","showColumn","hideColumn","getHiddenColumns","getVisibleColumns","showAllColumns","hideAllColumns","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","togglePagination","toggleView","refreshOptions","resetSearch","expandRow","collapseRow","expandAllRows","collapseAllRows","updateFormatText"]);t.fn.bootstrapTable=function(e){var i,n=Array.prototype.slice.call(arguments,1);return this.each(function(){var o=t(this),a=o.data("bootstrap.table"),s=t.extend({},d.DEFAULTS,o.data(),"object"==typeof e&&e);if("string"==typeof e){if(t.inArray(e,h)<0)throw new Error("Unknown method: "+e);if(!a)return;i=a[e].apply(a,n),"destroy"===e&&o.removeData("bootstrap.table")}a||o.data("bootstrap.table",a=new d(this,s))}),void 0===i?this:i},t.fn.bootstrapTable.Constructor=d,t.fn.bootstrapTable.defaults=d.DEFAULTS,t.fn.bootstrapTable.columnDefaults=d.COLUMN_DEFAULTS,t.fn.bootstrapTable.locales=d.LOCALES,t.fn.bootstrapTable.methods=h,t.fn.bootstrapTable.utils={sprintf:e,getFieldIndex:i,compareObjects:a,calculateObjectValue:o,getItemField:l,objectKeys:function(){var t,e,i,n;Object.keys||(Object.keys=(t=Object.prototype.hasOwnProperty,e=!{toString:null}.propertyIsEnumerable("toString"),n=(i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"]).length,function(o){if("object"!=typeof o&&("function"!=typeof o||null===o))throw new TypeError("Object.keys called on non-object");var a,s,r=[];for(a in o)t.call(o,a)&&r.push(a);if(e)for(s=0;s<n;s++)t.call(o,i[s])&&r.push(i[s]);return r}))},isIEBrowser:c},t(function(){t('[data-toggle="table"]').bootstrapTable()})}(jQuery),define("bootstrap-table",["bootstrap"],function(t){return function(){return t.$.fn.bootstrapTable}}(this)),function(t){"use strict";t.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候……"},formatRecordsPerPage:function(t){return"每页显示 "+t+" 条记录"},formatShowingRows:function(t,e,i){return"显示第 "+t+" 到第 "+e+" 条记录，总共 "+i+" 条记录"},formatDetailPagination:function(t){return"总共 "+t+" 条记录"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatColumns:function(){return"列"},formatExport:function(){return"导出数据"},formatClearFilters:function(){return"清空过滤"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-CN"])}(jQuery),define("bootstrap-table-lang",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";var e=t.fn.bootstrapTable.utils.sprintf,i={json:"JSON",xml:"XML",png:"PNG",csv:"CSV",txt:"TXT",sql:"SQL",doc:"MS-Word",excel:"MS-Excel",xlsx:"MS-Excel (OpenXML)",powerpoint:"MS-Powerpoint",pdf:"PDF"},n=(t.extend(t.fn.bootstrapTable.defaults,{showExport:!1,exportDataType:"basic",exportTypes:["json","xml","csv","txt","sql","excel"],exportOptions:{}}),t.extend(t.fn.bootstrapTable.defaults.icons,{export:"glyphicon-export icon-share"}),t.extend(t.fn.bootstrapTable.locales,{formatExport:function(){return"Export data"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales),t.fn.bootstrapTable.Constructor),o=n.prototype.initToolbar;n.prototype.initToolbar=function(){var n,a,s,r;this.showToolbar=this.options.showExport,o.apply(this,Array.prototype.slice.apply(arguments)),this.options.showExport&&!(s=(n=this).$toolbar.find(">.btn-group")).find("div.export").length&&(a=t(['<div class="export btn-group">','<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" aria-label="export type" title="'+this.options.formatExport()+'" data-toggle="dropdown" type="button">',e('<i class="%s %s"></i> ',this.options.iconsPrefix,this.options.icons.export),'<span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">',"</ul>","</div>"].join("")).appendTo(s).find(".dropdown-menu"),r=this.options.exportTypes,"string"==typeof this.options.exportTypes&&(s=this.options.exportTypes.slice(1,-1).replace(/ /g,"").split(","),r=[],t.each(s,function(t,e){r.push(e.slice(1,-1))})),t.each(r,function(t,e){i.hasOwnProperty(e)&&a.append(['<li role="menuitem" data-type="'+e+'">','<a href="javascript:void(0)">',i[e],"</a>","</li>"].join(""))}),a.find("li").click(function(){var e=this;if("function"!=typeof require)throw new Error("RequireJS not found");require(["tableexport"],function(){function i(){n.$el.tableExport(t.extend({},n.options.exportOptions,{type:r,escape:!1}))}var o,a,s,r=t(e).data("type");"all"===n.options.exportDataType&&n.options.pagination?(n.$el.one("server"===n.options.sidePagination?"post-body.bs.table":"page-change.bs.table",function(){i(),n.togglePagination()}),n.togglePagination()):"selected"===n.options.exportDataType?(o=n.getData(),s=n.getAllSelections(),"server"===n.options.sidePagination&&((o={total:n.options.totalRows})[n.options.dataField]=n.getData(),a="function"==typeof require?require("table"):null,(s={total:n.options.totalRows})[n.options.dataField]=a&&n.options.maintainSelected?a.api.selecteddata(n.$el):n.getAllSelections()),n.load(s),i(),n.load(o)):i()})}))}}(jQuery),define("bootstrap-table-export",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"function"==typeof define&&define.amd?define("dropzone",["jquery"],t):t(jQuery)}(function(t){function e(t){"@babel/helpers - typeof";return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,i){return!i||"object"!==e(i)&&"function"!=typeof i?o(t):i}function n(t){return(n=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function o(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&s(t,e)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function c(t,e,i){return e&&l(t.prototype,e),i&&l(t,i),t}function d(t,e){return void 0!==t&&null!==t?e(t):void 0}function u(t,e,i){return void 0!==t&&null!==t&&"function"==typeof t[e]?i(t,e):void 0}var h={exports:{}},p=function(){function t(){r(this,t)}return c(t,[{key:"on",value:function(t,e){return this._callbacks=this._callbacks||{},this._callbacks[t]||(this._callbacks[t]=[]),this._callbacks[t].push(e),this}},{key:"emit",value:function(t){this._callbacks=this._callbacks||{};var e=this._callbacks[t];if(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];var a=!0,s=!1,r=void 0;try{for(var l,c=e[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){l.value.apply(this,n)}}catch(t){s=!0,r=t}finally{try{a||null==c.return||c.return()}finally{if(s)throw r}}}return this}},{key:"off",value:function(t,e){if(!this._callbacks||0===arguments.length)return this._callbacks={},this;var i=this._callbacks[t];if(!i)return this;if(1===arguments.length)return delete this._callbacks[t],this;for(var n=0;n<i.length;n++){if(i[n]===e){i.splice(n,1);break}}return this}}]),t}(),f=function(t){function e(t,a){var s;r(this,e),s=i(this,n(e).call(this));var l,c;if(s.element=t,s.version=e.version,s.defaultOptions.previewTemplate=s.defaultOptions.previewTemplate.replace(/\n*/g,""),s.clickableElements=[],s.listeners=[],s.files=[],"string"==typeof s.element&&(s.element=document.querySelector(s.element)),!s.element||null==s.element.nodeType)throw new Error("Invalid dropzone element.");if(s.element.dropzone)throw new Error("Dropzone already attached.");e.instances.push(o(s)),s.element.dropzone=o(s);var d=null!=(c=e.optionsForElement(s.element))?c:{};if(s.options=e.extend({},s.defaultOptions,d,null!=a?a:{}),s.options.forceFallback||!e.isBrowserSupported())return i(s,s.options.fallback.call(o(s)));if(null==s.options.url&&(s.options.url=s.element.getAttribute("action")),!s.options.url)throw new Error("No URL provided.");if(s.options.acceptedFiles&&s.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(s.options.uploadMultiple&&s.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");return s.options.acceptedMimeTypes&&(s.options.acceptedFiles=s.options.acceptedMimeTypes,delete s.options.acceptedMimeTypes),null!=s.options.renameFilename&&(s.options.renameFile=function(t){return s.options.renameFilename.call(o(s),t.name,t)}),s.options.method="function"!=typeof s.options.method?s.options.method.toUpperCase():s.options.method,(l=s.getExistingFallback())&&l.parentNode&&l.parentNode.removeChild(l),!1!==s.options.previewsContainer&&(s.options.previewsContainer?s.previewsContainer=e.getElement(s.options.previewsContainer,"previewsContainer"):s.previewsContainer=s.element),s.options.clickable&&(!0===s.options.clickable?s.clickableElements=[s.element]:s.clickableElements=e.getElements(s.options.clickable,"clickable")),s.init(),s}return a(e,t),c(e,null,[{key:"initClass",value:function(){this.prototype.Emitter=p,this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"],this.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,timeout:3e4,parallelUploads:2,uploadMultiple:!1,chunking:!1,forceChunking:!1,chunkSize:2e6,parallelChunkUploads:!1,retryChunks:!1,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,autoQueue:!0,addRemoveLinks:!1,previewsContainer:null,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:!1,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function(){},params:function(t,e,i){if(i)return{dzuuid:i.file.upload.uuid,dzchunkindex:i.index,dztotalfilesize:i.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:i.file.upload.totalChunkCount,dzchunkbyteoffset:i.index*this.options.chunkSize}},accept:function(t,e){return e()},chunkSuccess:function(t,e,i){},chunksUploaded:function(t,e){e()},fallback:function(){var t;this.element.className="".concat(this.element.className," dz-browser-not-supported");var i=!0,n=!1,o=void 0;try{for(var a,s=this.element.getElementsByTagName("div")[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var r=a.value;if(/(^| )dz-message($| )/.test(r.className)){t=r,r.className="dz-message";break}}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}t||(t=e.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(t));var l=t.getElementsByTagName("span")[0];return l&&(null!=l.textContent?l.textContent=this.options.dictFallbackMessage:null!=l.innerText&&(l.innerText=this.options.dictFallbackMessage)),this.element.appendChild(this.getFallbackForm())},resize:function(t,e,i,n){var o={srcX:0,srcY:0,srcWidth:t.width,srcHeight:t.height},a=t.width/t.height;null==e&&null==i?(e=o.srcWidth,i=o.srcHeight):null==e?e=i*a:null==i&&(i=e/a),e=Math.min(e,o.srcWidth),i=Math.min(i,o.srcHeight);var s=e/i;if(o.srcWidth>e||o.srcHeight>i)if("crop"===n)a>s?(o.srcHeight=t.height,o.srcWidth=o.srcHeight*s):(o.srcWidth=t.width,o.srcHeight=o.srcWidth/s);else{if("contain"!==n)throw new Error("Unknown resizeMethod '".concat(n,"'"));a>s?i=e/a:e=i*a}return o.srcX=(t.width-o.srcWidth)/2,o.srcY=(t.height-o.srcHeight)/2,o.trgWidth=e,o.trgHeight=i,o},transformFile:function(t,e){return(this.options.resizeWidth||this.options.resizeHeight)&&t.type.match(/image.*/)?this.resizeImage(t,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,e):e(t)},previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail /></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size></span></div>\n    <div class="dz-filename"><span data-dz-name></span></div>\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n  <div class="dz-success-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Check</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475" fill="#FFFFFF"></path>\n      </g>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Error</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g stroke="#747474" stroke-opacity="0.198794158" fill="#FFFFFF" fill-opacity="0.816519475">\n          <path d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>',drop:function(t){return this.element.classList.remove("dz-drag-hover")},dragstart:function(t){},dragend:function(t){return this.element.classList.remove("dz-drag-hover")},dragenter:function(t){return this.element.classList.add("dz-drag-hover")},dragover:function(t){return this.element.classList.add("dz-drag-hover")},dragleave:function(t){return this.element.classList.remove("dz-drag-hover")},paste:function(t){},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(t){var i=this;if(this.element===this.previewsContainer&&this.element.classList.add("dz-started"),this.previewsContainer){t.previewElement=e.createElement(this.options.previewTemplate.trim()),t.previewTemplate=t.previewElement,this.previewsContainer.appendChild(t.previewElement);var n=!0,o=!1,a=void 0;try{for(var s,r=t.previewElement.querySelectorAll("[data-dz-name]")[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){var l=s.value;l.textContent=t.name}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}var c=!0,d=!1,u=void 0;try{for(var h,p=t.previewElement.querySelectorAll("[data-dz-size]")[Symbol.iterator]();!(c=(h=p.next()).done);c=!0)l=h.value,l.innerHTML=this.filesize(t.size)}catch(t){d=!0,u=t}finally{try{c||null==p.return||p.return()}finally{if(d)throw u}}this.options.addRemoveLinks&&(t._removeLink=e.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'.concat(this.options.dictRemoveFile,"</a>")),t.previewElement.appendChild(t._removeLink));var f=function(n){return n.preventDefault(),n.stopPropagation(),t.status===e.UPLOADING?e.confirm(i.options.dictCancelUploadConfirmation,function(){return i.removeFile(t)}):i.options.dictRemoveFileConfirmation?e.confirm(i.options.dictRemoveFileConfirmation,function(){return i.removeFile(t)}):i.removeFile(t)},m=!0,g=!1,v=void 0;try{for(var y,b=t.previewElement.querySelectorAll("[data-dz-remove]")[Symbol.iterator]();!(m=(y=b.next()).done);m=!0){y.value.addEventListener("click",f)}}catch(t){g=!0,v=t}finally{try{m||null==b.return||b.return()}finally{if(g)throw v}}}},removedfile:function(t){return null!=t.previewElement&&null!=t.previewElement.parentNode&&t.previewElement.parentNode.removeChild(t.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function(t,e){if(t.previewElement){t.previewElement.classList.remove("dz-file-preview");var i=!0,n=!1,o=void 0;try{for(var a,s=t.previewElement.querySelectorAll("[data-dz-thumbnail]")[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var r=a.value;r.alt=t.name,r.src=e}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}return setTimeout(function(){return t.previewElement.classList.add("dz-image-preview")},1)}},error:function(t,e){if(t.previewElement){t.previewElement.classList.add("dz-error"),"String"!=typeof e&&e.error&&(e=e.error);var i=!0,n=!1,o=void 0;try{for(var a,s=t.previewElement.querySelectorAll("[data-dz-errormessage]")[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){a.value.textContent=e}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}}},errormultiple:function(){},processing:function(t){if(t.previewElement&&(t.previewElement.classList.add("dz-processing"),t._removeLink))return t._removeLink.innerHTML=this.options.dictCancelUpload},processingmultiple:function(){},uploadprogress:function(t,e,i){if(t.previewElement){var n=!0,o=!1,a=void 0;try{for(var s,r=t.previewElement.querySelectorAll("[data-dz-uploadprogress]")[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){var l=s.value;"PROGRESS"===l.nodeName?l.value=e:l.style.width="".concat(e,"%")}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}}},totaluploadprogress:function(){},sending:function(){},sendingmultiple:function(){},success:function(t){if(t.previewElement)return t.previewElement.classList.add("dz-success")},successmultiple:function(){},canceled:function(t){return this.emit("error",t,this.options.dictUploadCanceled)},canceledmultiple:function(){},complete:function(t){if(t._removeLink&&(t._removeLink.innerHTML=this.options.dictRemoveFile),t.previewElement)return t.previewElement.classList.add("dz-complete")},completemultiple:function(){},maxfilesexceeded:function(){},maxfilesreached:function(){},queuecomplete:function(){},addedfiles:function(){}},this.prototype._thumbnailQueue=[],this.prototype._processingThumbnail=!1}},{key:"extend",value:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(var o=0,a=i;o<a.length;o++){var s=a[o];for(var r in s){var l=s[r];t[r]=l}}return t}}]),c(e,[{key:"getAcceptedFiles",value:function(){return this.files.filter(function(t){return t.accepted}).map(function(t){return t})}},{key:"getRejectedFiles",value:function(){return this.files.filter(function(t){return!t.accepted}).map(function(t){return t})}},{key:"getFilesWithStatus",value:function(t){return this.files.filter(function(e){return e.status===t}).map(function(t){return t})}},{key:"getQueuedFiles",value:function(){return this.getFilesWithStatus(e.QUEUED)}},{key:"getUploadingFiles",value:function(){return this.getFilesWithStatus(e.UPLOADING)}},{key:"getAddedFiles",value:function(){return this.getFilesWithStatus(e.ADDED)}},{key:"getActiveFiles",value:function(){return this.files.filter(function(t){return t.status===e.UPLOADING||t.status===e.QUEUED}).map(function(t){return t})}},{key:"init",value:function(){var t=this;if("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(e.createElement('<div class="dz-default dz-message"><button class="dz-button" type="button">'.concat(this.options.dictDefaultMessage,"</button></div>"))),this.clickableElements.length){!function i(){return t.hiddenFileInput&&t.hiddenFileInput.parentNode.removeChild(t.hiddenFileInput),t.hiddenFileInput=document.createElement("input"),t.hiddenFileInput.setAttribute("type","file"),(null===t.options.maxFiles||t.options.maxFiles>1)&&t.hiddenFileInput.setAttribute("multiple","multiple"),t.hiddenFileInput.className="dz-hidden-input",null!==t.options.acceptedFiles&&t.hiddenFileInput.setAttribute("accept",t.options.acceptedFiles),null!==t.options.capture&&t.hiddenFileInput.setAttribute("capture",t.options.capture),t.hiddenFileInput.style.visibility="hidden",t.hiddenFileInput.style.position="absolute",t.hiddenFileInput.style.top="0",t.hiddenFileInput.style.left="0",t.hiddenFileInput.style.height="0",t.hiddenFileInput.style.width="0",e.getElement(t.options.hiddenInputContainer,"hiddenInputContainer").appendChild(t.hiddenFileInput),t.hiddenFileInput.addEventListener("change",function(){var e=t.hiddenFileInput.files;if(e.length){var n=!0,o=!1,a=void 0;try{for(var s,r=e[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){var l=s.value;t.addFile(l)}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}}return t.emit("addedfiles",e),i()})}()}this.URL=null!==window.URL?window.URL:window.webkitURL;var i=!0,n=!1,o=void 0;try{for(var a,s=this.events[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var r=a.value;this.on(r,this.options[r])}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}this.on("uploadprogress",function(){return t.updateTotalUploadProgress()}),this.on("removedfile",function(){return t.updateTotalUploadProgress()}),this.on("canceled",function(e){return t.emit("complete",e)}),this.on("complete",function(e){if(0===t.getAddedFiles().length&&0===t.getUploadingFiles().length&&0===t.getQueuedFiles().length)return setTimeout(function(){return t.emit("queuecomplete")},0)});var l=function(t){return t.dataTransfer.types&&t.dataTransfer.types.some(function(t){return"Files"==t})},c=function(t){if(l(t))return t.stopPropagation(),t.preventDefault?t.preventDefault():t.returnValue=!1};return this.listeners=[{element:this.element,events:{dragstart:function(e){return t.emit("dragstart",e)},dragenter:function(e){return c(e),t.emit("dragenter",e)},dragover:function(e){var i;try{i=e.dataTransfer.effectAllowed}catch(t){}return e.dataTransfer.dropEffect="move"===i||"linkMove"===i?"move":"copy",c(e),t.emit("dragover",e)},dragleave:function(e){return t.emit("dragleave",e)},drop:function(e){return c(e),t.drop(e)},dragend:function(e){return t.emit("dragend",e)}}}],this.clickableElements.forEach(function(i){return t.listeners.push({element:i,events:{click:function(n){return(i!==t.element||n.target===t.element||e.elementInside(n.target,t.element.querySelector(".dz-message")))&&t.hiddenFileInput.click(),!0}}})}),this.enable(),this.options.init.call(this)}},{key:"destroy",value:function(){return this.disable(),this.removeAllFiles(!0),(null!=this.hiddenFileInput?this.hiddenFileInput.parentNode:void 0)&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone,e.instances.splice(e.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function(){var t,e=0,i=0;if(this.getActiveFiles().length){var n=!0,o=!1,a=void 0;try{for(var s,r=this.getActiveFiles()[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){var l=s.value;e+=l.upload.bytesSent,i+=l.upload.total}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}t=100*e/i}else t=100;return this.emit("totaluploadprogress",t,i,e)}},{key:"_getParamName",value:function(t){return"function"==typeof this.options.paramName?this.options.paramName(t):"".concat(this.options.paramName).concat(this.options.uploadMultiple?"[".concat(t,"]"):"")}},{key:"_renameFile",value:function(t){return"function"!=typeof this.options.renameFile?t.name:this.options.renameFile(t)}},{key:"getFallbackForm",value:function(){var t,i;if(t=this.getExistingFallback())return t;var n='<div class="dz-fallback">';this.options.dictFallbackText&&(n+="<p>".concat(this.options.dictFallbackText,"</p>")),n+='<input type="file" name="'.concat(this._getParamName(0),'" ').concat(this.options.uploadMultiple?'multiple="multiple"':void 0,' /><input type="submit" value="Upload!"></div>');var o=e.createElement(n);return"FORM"!==this.element.tagName?(i=e.createElement('<form action="'.concat(this.options.url,'" enctype="multipart/form-data" method="').concat(this.options.method,'"></form>')),i.appendChild(o)):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=i?i:o}},{key:"getExistingFallback",value:function(){for(var t=0,e=["div","form"];t<e.length;t++){var i,n=e[t];if(i=function(t){var e=!0,i=!1,n=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var s=o.value;if(/(^| )fallback($| )/.test(s.className))return s}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}(this.element.getElementsByTagName(n)))return i}}},{key:"setupEventListeners",value:function(){return this.listeners.map(function(t){return function(){var e=[];for(var i in t.events){var n=t.events[i];e.push(t.element.addEventListener(i,n,!1))}return e}()})}},{key:"removeEventListeners",value:function(){return this.listeners.map(function(t){return function(){var e=[];for(var i in t.events){var n=t.events[i];e.push(t.element.removeEventListener(i,n,!1))}return e}()})}},{key:"disable",value:function(){var t=this;return this.clickableElements.forEach(function(t){return t.classList.remove("dz-clickable")}),this.removeEventListeners(),this.disabled=!0,this.files.map(function(e){return t.cancelUpload(e)})}},{key:"enable",value:function(){return delete this.disabled,this.clickableElements.forEach(function(t){return t.classList.add("dz-clickable")}),
this.setupEventListeners()}},{key:"filesize",value:function(t){var e=0,i="b";if(t>0){for(var n=["tb","gb","mb","kb","b"],o=0;o<n.length;o++){var a=n[o];if(t>=Math.pow(this.options.filesizeBase,4-o)/10){e=t/Math.pow(this.options.filesizeBase,4-o),i=a;break}}e=Math.round(10*e)/10}return"<strong>".concat(e,"</strong> ").concat(this.options.dictFileSizeUnits[i])}},{key:"_updateMaxFilesReachedClass",value:function(){return null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(this.getAcceptedFiles().length===this.options.maxFiles&&this.emit("maxfilesreached",this.files),this.element.classList.add("dz-max-files-reached")):this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function(t){if(t.dataTransfer){this.emit("drop",t);for(var e=[],i=0;i<t.dataTransfer.files.length;i++)e[i]=t.dataTransfer.files[i];if(e.length){var n=t.dataTransfer.items;n&&n.length&&null!=n[0].webkitGetAsEntry?this._addFilesFromItems(n):this.handleFiles(e)}this.emit("addedfiles",e)}}},{key:"paste",value:function(t){if(null!=d(null!=t?t.clipboardData:void 0,function(t){return t.items})){this.emit("paste",t);var e=t.clipboardData.items;return e.length?this._addFilesFromItems(e):void 0}}},{key:"handleFiles",value:function(t){var e=!0,i=!1,n=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var s=o.value;this.addFile(s)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}},{key:"_addFilesFromItems",value:function(t){var e=this;return function(){var i=[],n=!0,o=!1,a=void 0;try{for(var s,r=t[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){var l,c=s.value;null!=c.webkitGetAsEntry&&(l=c.webkitGetAsEntry())?l.isFile?i.push(e.addFile(c.getAsFile())):l.isDirectory?i.push(e._addFilesFromDirectory(l,l.name)):i.push(void 0):null!=c.getAsFile&&(null==c.kind||"file"===c.kind)?i.push(e.addFile(c.getAsFile())):i.push(void 0)}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}return i}()}},{key:"_addFilesFromDirectory",value:function(t,e){var i=this,n=t.createReader(),o=function(t){return u(console,"log",function(e){return e.log(t)})};return function t(){return n.readEntries(function(n){if(n.length>0){var o=!0,a=!1,s=void 0;try{for(var r,l=n[Symbol.iterator]();!(o=(r=l.next()).done);o=!0){var c=r.value;c.isFile?c.file(function(t){if(!i.options.ignoreHiddenFiles||"."!==t.name.substring(0,1))return t.fullPath="".concat(e,"/").concat(t.name),i.addFile(t)}):c.isDirectory&&i._addFilesFromDirectory(c,"".concat(e,"/").concat(c.name))}}catch(t){a=!0,s=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw s}}t()}return null},o)}()}},{key:"accept",value:function(t,i){this.options.maxFilesize&&t.size>1024*this.options.maxFilesize*1024?i(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(t.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):e.isValidFile(t,this.options.acceptedFiles)?null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(i(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",t)):this.options.accept.call(this,t,i):i(this.options.dictInvalidFileType)}},{key:"addFile",value:function(t){var i=this;t.upload={uuid:e.uuidv4(),progress:0,total:t.size,bytesSent:0,filename:this._renameFile(t)},this.files.push(t),t.status=e.ADDED,this.emit("addedfile",t),this._enqueueThumbnail(t),this.accept(t,function(e){e?(t.accepted=!1,i._errorProcessing([t],e)):(t.accepted=!0,i.options.autoQueue&&i.enqueueFile(t)),i._updateMaxFilesReachedClass()})}},{key:"enqueueFiles",value:function(t){var e=!0,i=!1,n=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var s=o.value;this.enqueueFile(s)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}return null}},{key:"enqueueFile",value:function(t){var i=this;if(t.status!==e.ADDED||!0!==t.accepted)throw new Error("This file can't be queued because it has already been processed or was rejected.");if(t.status=e.QUEUED,this.options.autoProcessQueue)return setTimeout(function(){return i.processQueue()},0)}},{key:"_enqueueThumbnail",value:function(t){var e=this;if(this.options.createImageThumbnails&&t.type.match(/image.*/)&&t.size<=1024*this.options.maxThumbnailFilesize*1024)return this._thumbnailQueue.push(t),setTimeout(function(){return e._processThumbnailQueue()},0)}},{key:"_processThumbnailQueue",value:function(){var t=this;if(!this._processingThumbnail&&0!==this._thumbnailQueue.length){this._processingThumbnail=!0;var e=this._thumbnailQueue.shift();return this.createThumbnail(e,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,!0,function(i){return t.emit("thumbnail",e,i),t._processingThumbnail=!1,t._processThumbnailQueue()})}}},{key:"removeFile",value:function(t){if(t.status===e.UPLOADING&&this.cancelUpload(t),this.files=m(this.files,t),this.emit("removedfile",t),0===this.files.length)return this.emit("reset")}},{key:"removeAllFiles",value:function(t){null==t&&(t=!1);var i=!0,n=!1,o=void 0;try{for(var a,s=this.files.slice()[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var r=a.value;(r.status!==e.UPLOADING||t)&&this.removeFile(r)}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}return null}},{key:"resizeImage",value:function(t,i,n,o,a){var s=this;return this.createThumbnail(t,i,n,o,!0,function(i,n){if(null==n)return a(t);var o=s.options.resizeMimeType;null==o&&(o=t.type);var r=n.toDataURL(o,s.options.resizeQuality);return"image/jpeg"!==o&&"image/jpg"!==o||(r=b.restore(t.dataURL,r)),a(e.dataURItoBlob(r))})}},{key:"createThumbnail",value:function(t,e,i,n,o,a){var s=this,r=new FileReader;r.onload=function(){if(t.dataURL=r.result,"image/svg+xml"===t.type)return void(null!=a&&a(r.result));s.createThumbnailFromUrl(t,e,i,n,o,a)},r.readAsDataURL(t)}},{key:"displayExistingFile",value:function(t,e,i,n){var o=this,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];if(this.emit("addedfile",t),this.emit("complete",t),a){var s=function(e){o.emit("thumbnail",t,e),i&&i()};t.dataURL=e,this.createThumbnailFromUrl(t,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.resizeMethod,this.options.fixOrientation,s,n)}else this.emit("thumbnail",t,e),i&&i()}},{key:"createThumbnailFromUrl",value:function(t,e,i,n,o,a,s){var r=this,l=document.createElement("img");return s&&(l.crossOrigin=s),l.onload=function(){var s=function(t){return t(1)};return"undefined"!=typeof EXIF&&null!==EXIF&&o&&(s=function(t){return EXIF.getData(l,function(){return t(EXIF.getTag(this,"Orientation"))})}),s(function(o){t.width=l.width,t.height=l.height;var s=r.options.resize.call(r,t,e,i,n),c=document.createElement("canvas"),d=c.getContext("2d");switch(c.width=s.trgWidth,c.height=s.trgHeight,o>4&&(c.width=s.trgHeight,c.height=s.trgWidth),o){case 2:d.translate(c.width,0),d.scale(-1,1);break;case 3:d.translate(c.width,c.height),d.rotate(Math.PI);break;case 4:d.translate(0,c.height),d.scale(1,-1);break;case 5:d.rotate(.5*Math.PI),d.scale(1,-1);break;case 6:d.rotate(.5*Math.PI),d.translate(0,-c.width);break;case 7:d.rotate(.5*Math.PI),d.translate(c.height,-c.width),d.scale(-1,1);break;case 8:d.rotate(-.5*Math.PI),d.translate(-c.height,0)}y(d,l,null!=s.srcX?s.srcX:0,null!=s.srcY?s.srcY:0,s.srcWidth,s.srcHeight,null!=s.trgX?s.trgX:0,null!=s.trgY?s.trgY:0,s.trgWidth,s.trgHeight);var u=c.toDataURL("image/png");if(null!=a)return a(u,c)})},null!=a&&(l.onerror=a),l.src=t.dataURL}},{key:"processQueue",value:function(){var t=this.options.parallelUploads,e=this.getUploadingFiles().length,i=e;if(!(e>=t)){var n=this.getQueuedFiles();if(n.length>0){if(this.options.uploadMultiple)return this.processFiles(n.slice(0,t-e));for(;i<t;){if(!n.length)return;this.processFile(n.shift()),i++}}}}},{key:"processFile",value:function(t){return this.processFiles([t])}},{key:"processFiles",value:function(t){var i=!0,n=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var r=a.value;r.processing=!0,r.status=e.UPLOADING,this.emit("processing",r)}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}return this.options.uploadMultiple&&this.emit("processingmultiple",t),this.uploadFiles(t)}},{key:"_getFilesWithXhr",value:function(t){return this.files.filter(function(e){return e.xhr===t}).map(function(t){return t})}},{key:"cancelUpload",value:function(t){if(t.status===e.UPLOADING){var i=this._getFilesWithXhr(t.xhr),n=!0,o=!1,a=void 0;try{for(var s,r=i[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){s.value.status=e.CANCELED}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}void 0!==t.xhr&&t.xhr.abort();var l=!0,c=!1,d=void 0;try{for(var u,h=i[Symbol.iterator]();!(l=(u=h.next()).done);l=!0){var p=u.value;this.emit("canceled",p)}}catch(t){c=!0,d=t}finally{try{l||null==h.return||h.return()}finally{if(c)throw d}}this.options.uploadMultiple&&this.emit("canceledmultiple",i)}else t.status!==e.ADDED&&t.status!==e.QUEUED||(t.status=e.CANCELED,this.emit("canceled",t),this.options.uploadMultiple&&this.emit("canceledmultiple",[t]));if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function(t){if("function"==typeof t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return t.apply(this,i)}return t}},{key:"uploadFile",value:function(t){return this.uploadFiles([t])}},{key:"uploadFiles",value:function(t){var i=this;this._transformFiles(t,function(n){if(i.options.chunking){var o=n[0];t[0].upload.chunked=i.options.chunking&&(i.options.forceChunking||o.size>i.options.chunkSize),t[0].upload.totalChunkCount=Math.ceil(o.size/i.options.chunkSize)}if(t[0].upload.chunked){var a=t[0],s=n[0],r=0;a.upload.chunks=[];var l=function(){for(var n=0;void 0!==a.upload.chunks[n];)n++;if(!(n>=a.upload.totalChunkCount)){r++;var o=n*i.options.chunkSize,l=Math.min(o+i.options.chunkSize,a.size),c={name:i._getParamName(0),data:s.webkitSlice?s.webkitSlice(o,l):s.slice(o,l),filename:a.upload.filename,chunkIndex:n};a.upload.chunks[n]={file:a,index:n,dataBlock:c,status:e.UPLOADING,progress:0,retries:0},i._uploadData(t,[c])}};if(a.upload.finishedChunkUpload=function(n,o){var s=!0;n.status=e.SUCCESS,i.options.chunkSuccess.call(i,n,a,o),n.dataBlock=null,n.xhr=null;for(var r=0;r<a.upload.totalChunkCount;r++){if(void 0===a.upload.chunks[r])return l();a.upload.chunks[r].status!==e.SUCCESS&&(s=!1)}s&&i.options.chunksUploaded.call(i,a,function(e){i._finished(t,e||"",null)})},i.options.parallelChunkUploads)for(var c=0;c<a.upload.totalChunkCount;c++)l();else l()}else{for(var d=[],u=0;u<t.length;u++)d[u]={name:i._getParamName(u),data:n[u],filename:t[u].upload.filename};i._uploadData(t,d)}})}},{key:"_getChunk",value:function(t,e){for(var i=0;i<t.upload.totalChunkCount;i++)if(void 0!==t.upload.chunks[i]&&t.upload.chunks[i].xhr===e)return t.upload.chunks[i]}},{key:"_uploadData",value:function(t,i){var n=this,o=new XMLHttpRequest,a=!0,s=!1,r=void 0;try{for(var l,c=t[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){l.value.xhr=o}}catch(t){s=!0,r=t}finally{try{a||null==c.return||c.return()}finally{if(s)throw r}}t[0].upload.chunked&&(t[0].upload.chunks[i[0].chunkIndex].xhr=o);var d=this.resolveOption(this.options.method,t),u=this.resolveOption(this.options.url,t);o.open(d,u,!0),o.timeout=this.resolveOption(this.options.timeout,t),o.withCredentials=!!this.options.withCredentials,o.onload=function(e){n._finishedUploading(t,o,e)},o.ontimeout=function(){n._handleUploadError(t,o,"Request timedout after ".concat(n.options.timeout," seconds"))},o.onerror=function(){n._handleUploadError(t,o)},(null!=o.upload?o.upload:o).onprogress=function(e){return n._updateFilesUploadProgress(t,o,e)};var h={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"};this.options.headers&&e.extend(h,this.options.headers);for(var p in h){var f=h[p];f&&o.setRequestHeader(p,f)}var m=new FormData;if(this.options.params){var g=this.options.params;"function"==typeof g&&(g=g.call(this,t,o,t[0].upload.chunked?this._getChunk(t[0],o):null));for(var v in g){var y=g[v];m.append(v,y)}}var b=!0,x=!1,w=void 0;try{for(var k,_=t[Symbol.iterator]();!(b=(k=_.next()).done);b=!0){var C=k.value;this.emit("sending",C,o,m)}}catch(t){x=!0,w=t}finally{try{b||null==_.return||_.return()}finally{if(x)throw w}}this.options.uploadMultiple&&this.emit("sendingmultiple",t,o,m),this._addFormElementData(m);for(var S=0;S<i.length;S++){var T=i[S];m.append(T.name,T.data,T.filename)}this.submitRequest(o,m,t)}},{key:"_transformFiles",value:function(t,e){for(var i=this,n=[],o=0,a=0;a<t.length;a++)!function(a){i.options.transformFile.call(i,t[a],function(i){n[a]=i,++o===t.length&&e(n)})}(a)}},{key:"_addFormElementData",value:function(t){if("FORM"===this.element.tagName){var e=!0,i=!1,n=void 0;try{for(var o,a=this.element.querySelectorAll("input, textarea, select, button")[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var s=o.value,r=s.getAttribute("name"),l=s.getAttribute("type");if(l&&(l=l.toLowerCase()),void 0!==r&&null!==r)if("SELECT"===s.tagName&&s.hasAttribute("multiple")){var c=!0,d=!1,u=void 0;try{for(var h,p=s.options[Symbol.iterator]();!(c=(h=p.next()).done);c=!0){var f=h.value;f.selected&&t.append(r,f.value)}}catch(t){d=!0,u=t}finally{try{c||null==p.return||p.return()}finally{if(d)throw u}}}else(!l||"checkbox"!==l&&"radio"!==l||s.checked)&&t.append(r,s.value)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}}},{key:"_updateFilesUploadProgress",value:function(t,e,i){var n;if(void 0!==i){if(n=100*i.loaded/i.total,t[0].upload.chunked){var o=t[0],a=this._getChunk(o,e);a.progress=n,a.total=i.total,a.bytesSent=i.loaded;o.upload.progress=0,o.upload.total=0,o.upload.bytesSent=0;for(var s=0;s<o.upload.totalChunkCount;s++)void 0!==o.upload.chunks[s]&&void 0!==o.upload.chunks[s].progress&&(o.upload.progress+=o.upload.chunks[s].progress,o.upload.total+=o.upload.chunks[s].total,o.upload.bytesSent+=o.upload.chunks[s].bytesSent);o.upload.progress=o.upload.progress/o.upload.totalChunkCount}else{var r=!0,l=!1,c=void 0;try{for(var d,u=t[Symbol.iterator]();!(r=(d=u.next()).done);r=!0){var h=d.value;h.upload.progress=n,h.upload.total=i.total,h.upload.bytesSent=i.loaded}}catch(t){l=!0,c=t}finally{try{r||null==u.return||u.return()}finally{if(l)throw c}}}var p=!0,f=!1,m=void 0;try{for(var g,v=t[Symbol.iterator]();!(p=(g=v.next()).done);p=!0){var y=g.value;this.emit("uploadprogress",y,y.upload.progress,y.upload.bytesSent)}}catch(t){f=!0,m=t}finally{try{p||null==v.return||v.return()}finally{if(f)throw m}}}else{var b=!0;n=100;var x=!0,w=!1,k=void 0;try{for(var _,C=t[Symbol.iterator]();!(x=(_=C.next()).done);x=!0){var S=_.value;100===S.upload.progress&&S.upload.bytesSent===S.upload.total||(b=!1),S.upload.progress=n,S.upload.bytesSent=S.upload.total}}catch(t){w=!0,k=t}finally{try{x||null==C.return||C.return()}finally{if(w)throw k}}if(b)return;var T=!0,D=!1,$=void 0;try{for(var E,A=t[Symbol.iterator]();!(T=(E=A.next()).done);T=!0){var O=E.value;this.emit("uploadprogress",O,n,O.upload.bytesSent)}}catch(t){D=!0,$=t}finally{try{T||null==A.return||A.return()}finally{if(D)throw $}}}}},{key:"_finishedUploading",value:function(t,i,n){var o;if(t[0].status!==e.CANCELED&&4===i.readyState){if("arraybuffer"!==i.responseType&&"blob"!==i.responseType&&(o=i.responseText,i.getResponseHeader("content-type")&&~i.getResponseHeader("content-type").indexOf("application/json")))try{o=JSON.parse(o)}catch(t){n=t,o="Invalid JSON response from server."}this._updateFilesUploadProgress(t),200<=i.status&&i.status<300?t[0].upload.chunked?t[0].upload.finishedChunkUpload(this._getChunk(t[0],i),o):this._finished(t,o,n):this._handleUploadError(t,i,o)}}},{key:"_handleUploadError",value:function(t,i,n){if(t[0].status!==e.CANCELED){if(t[0].upload.chunked&&this.options.retryChunks){var o=this._getChunk(t[0],i);if(o.retries++<this.options.retryChunksLimit)return void this._uploadData(t,[o.dataBlock]);console.warn("Retried this chunk too often. Giving up.")}this._errorProcessing(t,n||this.options.dictResponseError.replace("{{statusCode}}",i.status),i)}}},{key:"submitRequest",value:function(t,e,i){t.send(e)}},{key:"_finished",value:function(t,i,n){var o=!0,a=!1,s=void 0;try{for(var r,l=t[Symbol.iterator]();!(o=(r=l.next()).done);o=!0){var c=r.value;c.status=e.SUCCESS,this.emit("success",c,i,n),this.emit("complete",c)}}catch(t){a=!0,s=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw s}}if(this.options.uploadMultiple&&(this.emit("successmultiple",t,i,n),this.emit("completemultiple",t)),this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function(t,i,n){var o=!0,a=!1,s=void 0;try{for(var r,l=t[Symbol.iterator]();!(o=(r=l.next()).done);o=!0){var c=r.value;c.status=e.ERROR,this.emit("error",c,i,n),this.emit("complete",c)}}catch(t){a=!0,s=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw s}}if(this.options.uploadMultiple&&(this.emit("errormultiple",t,i,n),this.emit("completemultiple",t)),this.options.autoProcessQueue)return this.processQueue()}}],[{key:"uuidv4",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})}}]),e}(p);f.initClass(),f.version="5.7.0",f.options={},f.optionsForElement=function(t){return t.getAttribute("id")?f.options[g(t.getAttribute("id"))]:void 0},f.instances=[],f.forElement=function(t){if("string"==typeof t&&(t=document.querySelector(t)),null==(null!=t?t.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return t.dropzone},f.autoDiscover=!0,f.discover=function(){var t;if(document.querySelectorAll)t=document.querySelectorAll(".dropzone");else{t=[];var e=function(e){return function(){var i=[],n=!0,o=!1,a=void 0;try{for(var s,r=e[Symbol.iterator]();!(n=(s=r.next()).done);n=!0){var l=s.value;/(^| )dropzone($| )/.test(l.className)?i.push(t.push(l)):i.push(void 0)}}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}return i}()};e(document.getElementsByTagName("div")),e(document.getElementsByTagName("form"))}return function(){var e=[],i=!0,n=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(i=(a=s.next()).done);i=!0){var r=a.value;!1!==f.optionsForElement(r)?e.push(new f(r)):e.push(void 0)}}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}return e}()},f.blacklistedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i],f.isBrowserSupported=function(){var t=!0;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a")){var e=!0,i=!1,n=void 0;try{for(var o,a=f.blacklistedBrowsers[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var s=o.value;s.test(navigator.userAgent)&&(t=!1)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}else t=!1;else t=!1;return t},f.dataURItoBlob=function(t){for(var e=atob(t.split(",")[1]),i=t.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(e.length),o=new Uint8Array(n),a=0,s=e.length,r=0<=s;r?a<=s:a>=s;r?a++:a--)o[a]=e.charCodeAt(a);return new Blob([n],{type:i})};var m=function(t,e){return t.filter(function(t){return t!==e}).map(function(t){return t})},g=function(t){return t.replace(/[\-_](\w)/g,function(t){return t.charAt(1).toUpperCase()})};f.createElement=function(t){var e=document.createElement("div");return e.innerHTML=t,e.childNodes[0]},f.elementInside=function(t,e){if(t===e)return!0;for(;t=t.parentNode;)if(t===e)return!0;return!1},f.getElement=function(t,e){var i;if("string"==typeof t?i=document.querySelector(t):null!=t.nodeType&&(i=t),null==i)throw new Error("Invalid `".concat(e,"` option provided. Please provide a CSS selector or a plain HTML element."));return i},f.getElements=function(t,e){var i,n;if(t instanceof Array){n=[];try{var o=!0,a=!1,s=void 0;try{for(var r,l=t[Symbol.iterator]();!(o=(r=l.next()).done);o=!0)i=r.value,n.push(this.getElement(i,e))}catch(t){a=!0,s=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw s}}}catch(t){n=null}}else if("string"==typeof t){n=[];var c=!0,d=!1,u=void 0;try{for(var h,p=document.querySelectorAll(t)[Symbol.iterator]();!(c=(h=p.next()).done);c=!0)i=h.value,n.push(i)}catch(t){d=!0,u=t}finally{try{c||null==p.return||p.return()}finally{if(d)throw u}}}else null!=t.nodeType&&(n=[t]);if(null==n||!n.length)throw new Error("Invalid `".concat(e,"` option provided. Please provide a CSS selector, a plain HTML element or a list of those."));return n},f.confirm=function(t,e,i){return window.confirm(t)?e():null!=i?i():void 0},f.isValidFile=function(t,e){if(!e)return!0;e=e.split(",");var i=t.type,n=i.replace(/\/.*$/,""),o=!0,a=!1,s=void 0;try{for(var r,l=e[Symbol.iterator]();!(o=(r=l.next()).done);o=!0){var c=r.value;if(c=c.trim(),"."===c.charAt(0)){if(-1!==t.name.toLowerCase().indexOf(c.toLowerCase(),t.name.length-c.length))return!0}else if(/\/\*$/.test(c)){if(n===c.replace(/\/.*$/,""))return!0}else if(i===c)return!0}}catch(t){a=!0,s=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw s}}return!1},void 0!==t&&null!==t&&(t.fn.dropzone=function(t){return this.each(function(){return new f(this,t)})}),void 0!==h&&null!==h?h.exports=f:window.Dropzone=f,f.ADDED="added",f.QUEUED="queued",f.ACCEPTED=f.QUEUED,f.UPLOADING="uploading",f.PROCESSING=f.UPLOADING,f.CANCELED="canceled",f.ERROR="error",f.SUCCESS="success";var v=function(t){var e=(t.naturalWidth,t.naturalHeight),i=document.createElement("canvas");i.width=1,i.height=e;var n=i.getContext("2d");n.drawImage(t,0,0);for(var o=n.getImageData(1,0,1,e),a=o.data,s=0,r=e,l=e;l>s;){0===a[4*(l-1)+3]?r=l:s=l,l=r+s>>1}var c=l/e;return 0===c?1:c},y=function(t,e,i,n,o,a,s,r,l,c){var d=v(e);return t.drawImage(e,i,n,o,a,s,r,l,c/d)},b=function(){function t(){r(this,t)}return c(t,null,[{key:"initClass",value:function(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function(t){for(var e="",i=void 0,n=void 0,o="",a=void 0,s=void 0,r=void 0,l="",c=0;;)if(i=t[c++],n=t[c++],o=t[c++],a=i>>2,s=(3&i)<<4|n>>4,r=(15&n)<<2|o>>6,l=63&o,isNaN(n)?r=l=64:isNaN(o)&&(l=64),e=e+this.KEY_STR.charAt(a)+this.KEY_STR.charAt(s)+this.KEY_STR.charAt(r)+this.KEY_STR.charAt(l),i=n=o="",a=s=r=l="",!(c<t.length))break;return e}},{key:"restore",value:function(t,e){if(!t.match("data:image/jpeg;base64,"))return e;var i=this.decode64(t.replace("data:image/jpeg;base64,","")),n=this.slice2Segments(i),o=this.exifManipulation(e,n);return"data:image/jpeg;base64,".concat(this.encode64(o))}},{key:"exifManipulation",value:function(t,e){var i=this.getExifArray(e),n=this.insertExif(t,i);return new Uint8Array(n)}},{key:"getExifArray",value:function(t){for(var e=void 0,i=0;i<t.length;){if(e=t[i],255===e[0]&225===e[1])return e;i++}return[]}},{key:"insertExif",value:function(t,e){var i=t.replace("data:image/jpeg;base64,",""),n=this.decode64(i),o=n.indexOf(255,3),a=n.slice(0,o),s=n.slice(o),r=a;return r=r.concat(e),r=r.concat(s)}},{key:"slice2Segments",value:function(t){for(var e=0,i=[];;){var n;if(255===t[e]&218===t[e+1])break;if(255===t[e]&216===t[e+1])e+=2;else{n=256*t[e+2]+t[e+3];var o=e+n+2,a=t.slice(e,o);i.push(a),e=o}if(e>t.length)break}return i}},{key:"decode64",value:function(t){var e=void 0,i=void 0,n="",o=void 0,a=void 0,s=void 0,r="",l=0,c=[],d=/[^A-Za-z0-9\+\/\=]/g;for(d.exec(t)&&console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding."),t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");;)if(o=this.KEY_STR.indexOf(t.charAt(l++)),a=this.KEY_STR.indexOf(t.charAt(l++)),s=this.KEY_STR.indexOf(t.charAt(l++)),r=this.KEY_STR.indexOf(t.charAt(l++)),e=o<<2|a>>4,i=(15&a)<<4|s>>2,n=(3&s)<<6|r,c.push(e),64!==s&&c.push(i),64!==r&&c.push(n),e=i=n="",o=a=s=r="",!(l<t.length))break;return c}}]),t}();b.initClass();return f._autoDiscoverFunction=function(){if(f.autoDiscover)return f.discover()},function(t,e){var i=!1,n=!0,o=t.document,a=o.documentElement,s=o.addEventListener?"addEventListener":"attachEvent",r=o.addEventListener?"removeEventListener":"detachEvent",l=o.addEventListener?"":"on",c=function n(a){if("readystatechange"!==a.type||"complete"===o.readyState)return("load"===a.type?t:o)[r](l+a.type,n,!1),!i&&(i=!0)?e.call(t,a.type||a):void 0};if("complete"!==o.readyState){if(o.createEventObject&&a.doScroll){try{n=!t.frameElement}catch(t){}n&&function t(){try{a.doScroll("left")}catch(e){return void setTimeout(t,50)}return c("poll")}()}o[s](l+"DOMContentLoaded",c,!1),o[s](l+"readystatechange",c,!1),t[s](l+"load",c,!1)}}(window,f._autoDiscoverFunction),h.exports}),define("upload",["jquery","bootstrap","dropzone","template"],function(t,e,i,n){var o={list:{},options:{},config:{container:document.body,classname:".plupload:not([initialized]),.faupload:not([initialized])",previewtpl:'<li class="col-xs-3"><a href="<%=fullurl%>" data-url="<%=url%>" target="_blank" class="thumbnail"><img src="<%=fullurl%>" onerror="this.src=\''+Fast.api.fixurl("ajax/icon")+'?suffix=<%=suffix%>\';this.onerror=null;" class="img-responsive"></a><a href="javascript:;" class="btn btn-danger btn-xs btn-trash"><i class="fa fa-trash"></i></a></li>'},events:{onInit:function(){},onUploadSuccess:function(e,i,n){var a=e.element,s=e.options.onUploadSuccess,r=void 0!==i.data?i.data:null;if(a){var l=t(a).data("input-id")?t(a).data("input-id"):"";if(l){var c=[],d=t("#"+l);t(a).data("multiple")&&""!==d.val()&&c.push(d.val());var u=Config.upload.fullmode?r.fullurl?r.fullurl:Fast.api.cdnurl(r.url):r.url;c.push(u),d.val(c.join(",")).trigger("change").trigger("validate")}var h=t(a).data("upload-success");if(h&&("function"!=typeof h&&"function"==typeof o.api.custom[h]&&(h=o.api.custom[h]),"function"==typeof h)){var p=h.call(a,r,i,e,n);if(!1===p)return}}if("function"==typeof s){var p=s.call(a,r,i,e,n);if(!1===p)return}},onUploadError:function(e,i,n){var a=e.element,s=e.options.onUploadError,r=void 0!==i.data?i.data:null;if(a){var l=t(a).data("upload-error");if(l&&("function"!=typeof l&&"function"==typeof o.api.custom[l]&&(l=o.api.custom[l]),"function"==typeof l)){var c=l.call(a,r,i,e,n);if(!1===c)return}}if("function"==typeof s){var c=s.call(a,r,i,e,n);if(!1===c)return}Toastr.error(i.msg.toString().replace(/(<([^>]+)>)/gi,"")+"(code:"+i.code+")")},onUploadResponse:function(e,i,n){try{var o="object"==typeof e?e:JSON.parse(e);o.hasOwnProperty("code")||t.extend(o,{code:-2,msg:e,data:null})}catch(t){var o={code:-1,msg:t.message,data:null}}return o},onUploadComplete:function(e,i){var n=e.element,a=e.options.onUploadComplete;if(n){var s=t(n).data("upload-complete");if(s&&("function"!=typeof s&&"function"==typeof o.api.custom[s]&&(s=o.api.custom[s]),"function"==typeof s)){var r=s.call(n,i);if(!1===r)return}}if("function"==typeof a){var r=a.call(n,i);if(!1===r)return}}},api:{upload:function(e,a,s,r){e=void 0===e?o.config.classname:e,t(e,o.config.container).each(function(){if(t(this).attr("initialized"))return!0;t(this).attr("initialized",!0);var e=this,l=t(this).prop("id")||t(this).prop("name")||i.uuidv4(),c=t(this).data("url"),d=t(this).data("maxsize"),u=t(this).data("maxcount"),h=t(this).data("mimetype"),p=t(this).data("multipart"),f=t(this).data("multiple"),m=t(e).data("input-id")?t(e).data("input-id"):"",g=t(e).data("preview-id")?t(e).data("preview-id"):"";c=c||Config.upload.uploadurl,c=Fast.api.fixurl(c);var v=Config.upload.chunksize||2097152,y=Config.upload.timeout||6e5;d=void 0!==d?d:Config.upload.maxsize,h=void 0!==h?h:Config.upload.mimetype,p=void 0!==p?p:Config.upload.multipart,f=void 0!==f?f:Config.upload.multiple,h=h.split(",").map(function(t){return t.indexOf("/")>-1?t:t&&"*"!==t&&"."!==t.charAt(0)?"."+t:t}).join(","),h="*"===h?null:h;var b=function(t){var e=t.toString().match(/^([0-9\.]+)(\w+)$/),i=e?parseFloat(e[1]):parseFloat(t),n=e?e[2].toLowerCase():"b",o={b:0,k:1,kb:1,m:2,mb:2,gb:3,g:3,tb:4,t:4},a=void 0!==o[n]?o[n]:0;return i*Math.pow(1024,a)/Math.pow(1024,2)}(d),x=t(this).data()||{};x=t.extend(!0,{},x,t(this).data("upload-options")||{}),delete x.success,delete x.url,p=t.isArray(p)?{}:p;var w=t(this).data("params")||{};void 0!==w.category||t(this).data("category");o.list[l]=new i(this,t.extend({url:c,params:function(e,i,n){var o=p;return n?t.extend({},o,{filesize:n.file.size,filename:n.file.name,chunkid:n.file.upload.uuid,chunkindex:n.index,chunkcount:n.file.upload.totalChunkCount,chunksize:this.options.chunkSize,chunkfilesize:n.dataBlock.data.size,width:n.file.width||0,height:n.file.height||0,type:n.file.type}):o},chunking:!1,chunkSize:v,maxFilesize:b,acceptedFiles:h,maxFiles:u&&parseInt(u)>1?u:f?null:1,timeout:y,parallelUploads:1,previewsContainer:!1,dictDefaultMessage:__("Drop files here to upload"),dictFallbackMessage:__("Your browser does not support drag'n'drop file uploads"),dictFallbackText:__("Please use the fallback form below to upload your files like in the olden days"),dictFileTooBig:__("File is too big (%sMiB), Max filesize: %sMiB","{{filesize}}","{{maxFilesize}}"),dictInvalidFileType:__("You can't upload files of this type"),dictResponseError:__("Server responded with %s code.","{{statusCode}}"),dictCancelUpload:__("Cancel upload"),dictUploadCanceled:__("Upload canceled"),dictCancelUploadConfirmation:__("Are you sure you want to cancel this upload?"),dictRemoveFile:__("Remove file"),dictMaxFilesExceeded:__("You can only upload a maximum of %s files","{{maxFiles}}"),init:function(){o.events.onInit.call(this),t(">i",this.element).addClass("dz-message"),this.options.elementHtml=t(this.element).html()},sending:function(t,e,i){void 0!==t.category&&i.append("category",t.category)},addedfile:function(e){var i=t(this.element).data("params")||{},n=void 0!==i.category?i.category:t(this.element).data("category")||"";e.category="function"==typeof n?n.call(this,e):n},addedfiles:function(e){if(this.options.maxFiles&&(!this.options.maxFiles||this.options.maxFiles>1)&&this.options.inputId){var i=t("#"+this.options.inputId);if(i.length>0){var n=t.trim(i.val()),o=""===n?0:n.split(/\,/).length,a=this.options.maxFiles-o;if(0===a||e.length>a){e=Array.prototype.slice.call(e,a);for(var s=0;s<e.length;s++)this.removeFile(e[s]);Toastr.error(__("You can only upload a maximum of %s files",this.options.maxFiles))}}}},success:function(t,e){var i=o.events.onUploadResponse(e,this,t);t.ret=i,1===i.code?o.events.onUploadSuccess(this,i,t):o.events.onUploadError(this,i,t)},error:function(e,i,n){var a=t("<div>"+(n&&void 0!==n.responseText?n.responseText:i)+"</div>");a.find("style, title, script").remove();var s=a.text()||__("Network error"),r={code:0,data:null,msg:s};o.events.onUploadError(this,r,e)},uploadprogress:function(e,i,n){e.upload.chunked&&t(this.element).prop("disabled",!0).html("<i class='fa fa-upload'></i> "+__("Upload")+Math.floor(e.upload.bytesSent/e.size*100)+"%")},totaluploadprogress:function(e,i){this.getActiveFiles().length>0&&!this.options.chunking&&t(this.element).prop("disabled",!0).html("<i class='fa fa-upload'></i> "+__("Upload")+Math.floor(e)+"%")},queuecomplete:function(){o.events.onUploadComplete(this,this.files),this.removeAllFiles(!0),t(this.element).prop("disabled",!1).html(this.options.elementHtml)},chunkSuccess:function(t,e,i){},chunksUploaded:function(e,i){var n=this;Fast.api.ajax({url:this.options.url,data:t.extend({},p,{action:"merge",filesize:e.size,filename:e.name,chunkid:e.upload.uuid,chunkcount:e.upload.totalChunkCount})},function(t,e){return i(JSON.stringify(e)),!1},function(t,i){e.accepted=!1,n._errorProcessing([e],i.msg)})},onUploadSuccess:a,onUploadError:s,onUploadComplete:r},o.options,x)),g&&f&&require(["dragsort"],function(){t("#"+g).dragsort({dragSelector:"li a:not(.btn-trash)",dragEnd:function(){
t("#"+g).trigger("fa.preview.change")},placeHolderTemplate:'<li class="col-xs-3"></li>'})});var k=function(e){var i={},n=t("textarea[name='"+e+"']"),o=n.prev("ul");t.each(t("input,select,textarea",o).serializeArray(),function(t,e){var n=/\[?(\w+)\]?\[(\w+)\]$/g,o=n.exec(e.name);if(!o)return!0;isNaN(o[2])?(o[1]="x"+parseInt(o[1]),void 0===i[o[1]]&&(i[o[1]]={}),i[o[1]][o[2]]=e.value):i[t]=e.value});var a=[];t.each(i,function(t,e){a.push(e)}),n.val(JSON.stringify(a))};g&&m&&(t(document.body).on("keyup change","#"+m,function(i){var a=t("#"+m).val(),s=a.split(/\,/),r=t("#"+g);r.empty();var l=r.data("template")?r.data("template"):"",c=r.next().is("textarea")?r.next("textarea").val():"{}",d={};try{d=JSON.parse(c)}catch(i){}t.each(s,function(i,a){if(!a)return!0;var s=/[\.]?([a-zA-Z0-9]+)$/.exec(a);s=s?s[1]:"file";var c=t(e).data(),u=void 0!==c.cdnurl?Fast.api.cdnurl(a,c.cdnurl):Fast.api.cdnurl(a);a=Config.upload.fullmode?u:a;var h=d&&void 0!==d[i]?d[i]:null,p={url:a,fullurl:u,data:c,key:i,index:i,value:h,row:h,suffix:s},f=l?n(l,p):n.render(o.config.previewtpl,p);r.append(f)}),k(r.data("name"))}),t("#"+m).trigger("change")),g&&(t("#"+g).on("change keyup","input,textarea,select",function(){k(t(this).closest("ul").data("name"))}),t(document.body).on("fa.preview.change","#"+g,function(){var e=[];t("#"+g+" [data-url]").each(function(i,n){e.push(t(this).data("url"))}),m&&t("#"+m).val(e.join(",")),k(t("#"+g).data("name"))}),t(document.body).on("click","#"+g+" .btn-trash",function(){t(this).closest("li").remove(),t("#"+g).trigger("fa.preview.change")})),m&&(t("#"+m).closest("form").on("reset",function(){setTimeout(t.proxy(function(){t("#"+m,this).trigger("change")},this),0)}),t("body").on("paste drop","#"+m,function(e){var i=e.originalEvent,n=t(".plupload[data-input-id='"+t(this).attr("id")+"'],.faupload[data-input-id='"+t(this).attr("id")+"']");if("paste"===e.type&&i.clipboardData&&i.clipboardData.items){var a=i.clipboardData.items;if(!(1===a.length&&a[0].type.indexOf("text")>-1||2===a.length&&a[1].type.indexOf("text")>-1))return o.list[n.attr("id")].paste(i),!1}if("drop"===e.type&&i.dataTransfer&&i.dataTransfer.files)return o.list[n.attr("id")].drop(i),!1}))})},plupload:function(t,e,i,n){return o.api.upload(t,e,i,n)},faupload:function(t,e,i,n){return o.api.upload(t,e,i,n)},send:function(e,n,a,s){var r=Layer.msg(__("Uploading"),{offset:"t",time:0}),l="dropzone-"+i.uuidv4();t('<button type="button" id="'+l+'" class="btn btn-danger hidden faupload" />').appendTo("body"),t("#"+l).data("upload-complete",function(t){Layer.close(r),o.list[l].removeAllFiles(!0)}),o.api.upload("#"+l,n,a,s),setTimeout(function(){o.list[l].addFile(e)},1)},custom:{afteruploadcallback:function(t){console.log(this,t),alert("Custom Callback,Response URL:"+t.url)}}}};return o}),function(t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define("validator",["jquery"],t):t(jQuery)}(function(t,e){"use strict";function i(e,n){function o(){a.$el=t(e),a.$el.length?a._init(a.$el[0],n):F(e)&&(I[e]=n)}var a=this;if(!(a instanceof i))return new i(e,n);i.pending?t(window).on("validatorready",o):o()}function n(e){function i(){var e=this.options;for(var i in e)i in R&&(this[i]=e[i]);t.extend(this,{_valHook:function(){return"true"===this.element.contentEditable?"text":"val"},getValue:function(){var e=this.element;return"number"===e.type&&e.validity&&e.validity.badInput?"NaN":t(e)[this._valHook()]()},setValue:function(e){t(this.element)[this._valHook()](this.value=e)},getRangeMsg:function(t,e,i){function n(t,e){return l?t>e:t>=e}if(e){var o,a=this,s=a.messages[a._r]||"",r=e[0].split("~"),l="false"===e[1],c=r[0],d=r[1],u="rg",h=[""],p=A(t)&&+t==+t;return 2===r.length?c&&d?(p&&n(t,+c)&&n(+d,t)&&(o=!0),h=h.concat(r),u=l?"gtlt":"rg"):c&&!d?(p&&n(t,+c)&&(o=!0),h.push(c),u=l?"gt":"gte"):!c&&d&&(p&&n(+d,t)&&(o=!0),h.push(d),u=l?"lt":"lte"):(t===+c&&(o=!0),h.push(c),u="eq"),s&&(i&&s[u+i]&&(u+=i),h[0]=s[u]),o||a._rules&&(a._rules[a._i].msg=a.renderMsg.apply(null,h))}},renderMsg:function(){var t=arguments,e=t[0],i=t.length;if(e){for(;--i;)e=e.replace("{"+i+"}",t[i]);return e}}})}function n(i,n,o){this.key=i,this.validator=e,t.extend(this,o,n)}return i.prototype=e,n.prototype=new i,n}function o(t,e){if(P(t)){var i,n=e?!0===e?this:e:o.prototype;for(i in t)p(i)&&(n[i]=s(t[i]))}}function a(t,e){if(P(t)){var i,n=e?!0===e?this:e:a.prototype;for(i in t)n[i]=t[i]}}function s(e){switch(t.type(e)){case"function":return e;case"array":var i=function(){return e[0].test(this.value)||e[1]||!1};return i.msg=e[1],i;case"regexp":return function(){return e.test(this.value)}}}function r(e){var i,n,o;if(e&&e.tagName){switch(e.tagName){case"INPUT":case"SELECT":case"TEXTAREA":case"BUTTON":case"FIELDSET":i=e.form||t(e).closest("."+y);break;case"FORM":i=e;break;default:i=t(e).closest("."+y)}for(n in I)if(t(i).is(n)){o=I[n];break}return t(i).data(g)||t(i)[g](o).data(g)}}function l(t,e){var i=A(L(t,b+"-"+e));if(i&&(i=new Function("return "+i)()))return s(i)}function c(t,e,i){var n=e.msg,o=e._r;return P(n)&&(n=n[o]),F(n)||(n=L(t,x+"-"+o)||L(t,x)||(i?F(i)?i:i[o]:"")),n}function d(t){var e;return t&&(e=S.exec(t)),e&&e[0]}function u(t){return"INPUT"===t.tagName&&"checkbox"===t.type||"radio"===t.type}function h(t){return Date.parse(t.replace(/\.|\-/g,"/"))}function p(t){return/^\w+$/.test(t)}function f(t){var e="#"===t.charAt(0);return t=t.replace(/([:.{(|)}/\[\]])/g,"\\$1"),e?t:'[name="'+t+'"]:first'}var m,g="validator",v="."+g,y="nice-"+g,b="data-rule",x="data-msg",w=/(&)?(!)?\b(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?\s*(;|\|)?/g,k=/(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?/,_=/(?:([^:;\(\[]*):)?(.*)/,C=/[^\x00-\xff]/g,S=/top|right|bottom|left/,T=/(?:(cors|jsonp):)?(?:(post|get):)?(.+)/i,D=/[<>'"`\\]|&#x?\d+[A-F]?;?|%3[A-F]/gim,$=t.noop,E=t.proxy,A=t.trim,O=t.isFunction,F=function(t){return"string"==typeof t},P=function(t){return t&&"[object Object]"===Object.prototype.toString.call(t)},M=document.documentMode||+(navigator.userAgent.match(/MSIE (\d+)/)&&RegExp.$1),L=function(t,i,n){return t&&t.tagName?n===e?t.getAttribute(i):void(null===n?t.removeAttribute(i):t.setAttribute(i,""+n)):null},I={},N={debug:0,theme:"default",ignore:"",focusInvalid:!0,focusCleanup:!1,stopOnError:!1,beforeSubmit:null,valid:null,invalid:null,validation:null,formClass:"n-default",validClass:"n-valid",invalidClass:"n-invalid",bindClassTo:null,remoteDataType:"cors"},R={timely:1,display:null,target:null,ignoreBlank:!1,showOk:!0,dataFilter:function(t){if(F(t)||P(t)&&("error"in t||"ok"in t))return t},msgMaker:function(e){var i;return i='<span role="alert" class="msg-wrap n-'+e.type+'">'+e.arrow,e.result?t.each(e.result,function(t,n){i+='<span class="n-'+n.type+'">'+e.icon+'<span class="n-msg">'+n.msg+"</span></span>"}):i+=e.icon+'<span class="n-msg">'+e.msg+"</span>",i+="</span>"},msgWrapper:"span",msgArrow:"",msgIcon:'<span class="n-icon"></span>',msgClass:"n-right",msgStyle:"",msgShow:null,msgHide:null},j={};return t.fn.validator=function(e){var n=this,o=arguments;return n.is(":verifiable")?n:(n.is("form")||(n=this.find("form")),n.length||(n=this),n.each(function(){var n=t(this).data(g);if(n)if(F(e)){if("_"===e.charAt(0))return;n[e].apply(n,[].slice.call(o,1))}else e&&(n._reset(!0),n._init(this,e));else new i(this,e)}),this)},t.fn.isValid=function(t,i){var n,o,a=r(this[0]),s=O(t);return!a||(s||i!==e||(i=t),a.checkOnly=!!i,o=a.options,n=a._multiValidate(this.is(":verifiable")?this:this.find(":verifiable"),function(e){e||!o.focusInvalid||a.checkOnly||a.$el.find("[aria-invalid]:first").focus(),s&&(t.length?t(e):e&&t()),a.checkOnly=!1}),s?this:n)},t.extend(t.expr.pseudos||t.expr[":"],{verifiable:function(t){var e=t.nodeName.toLowerCase();return("input"===e&&!{submit:1,button:1,reset:1,image:1}[t.type]||"select"===e||"textarea"===e||"true"===t.contentEditable)&&!t.disabled},filled:function(e){return!!A(t(e).val())}}),i.prototype={_init:function(e,i){var s,r,l,c=this;O(i)&&(i={valid:i}),i=c._opt=i||{},l=L(e,"data-"+g+"-option"),l=c._dataOpt=l&&"{"===l.charAt(0)?new Function("return "+l)():{},r=c._themeOpt=j[i.theme||l.theme||N.theme],s=c.options=t.extend({},N,R,r,c.options,i,l),c.rules=new o(s.rules,!0),c.messages=new a(s.messages,!0),c.Field=n(c),c.elements=c.elements||{},c.deferred={},c.errors={},c.fields={},c._initFields(s.fields),c.$el.data(g)||(c.$el.data(g,c).addClass(y+" "+s.formClass).on("form-submit-validate",function(t,e,i,n,o){c.vetoed=o.veto=!c.isValid,c.ajaxFormOptions=n}).on("submit"+v+" validate"+v,E(c,"_submit")).on("reset"+v,E(c,"_reset")).on("showmsg"+v,E(c,"_showmsg")).on("hidemsg"+v,E(c,"_hidemsg")).on("focusin"+v+" click"+v,":verifiable",E(c,"_focusin")).on("focusout"+v+" validate"+v,":verifiable",E(c,"_focusout")).on("keyup"+v+" input"+v+" compositionstart compositionend",":verifiable",E(c,"_focusout")).on("click"+v,":radio,:checkbox","click",E(c,"_focusout")).on("change"+v,'select,input[type="file"]',"change",E(c,"_focusout")),c._NOVALIDATE=L(e,"novalidate"),L(e,"novalidate","novalidate")),F(s.target)&&c.$el.find(s.target).addClass("msg-container")},_guessAjax:function(e){function i(e,i,n){return!!(e&&e[i]&&t.map(e[i],function(t){return~t.namespace.indexOf(n)?1:null}).length)}var n=this;if(!(n.isAjaxSubmit=!!n.options.valid)){var o=(t._data||t.data)(e,"events");n.isAjaxSubmit=i(o,"valid","form")||i(o,"submit","form-plugin")}},_initFields:function(t){function e(t,e){if(null===e||s){var i=a.elements[t];i&&a._resetElement(i,!0),delete a.fields[t]}else a.fields[t]=new a.Field(t,F(e)?{rule:e}:e,a.fields[t])}var i,n,o,a=this,s=null===t;if(s&&(t=a.fields),P(t))for(i in t)if(~i.indexOf(","))for(n=i.split(","),o=n.length;o--;)e(A(n[o]),t[i]);else e(i,t[i]);a.$el.find(":verifiable").each(function(){a._parse(this)})},_parse:function(t){var e,i,n,o=this,a=t.name,s=L(t,b);return s&&L(t,b,null),t.id&&("#"+t.id in o.fields||!a||null!==s&&(e=o.fields[a])&&s!==e.rule&&t.id!==e.key)&&(a="#"+t.id),a||(a="#"+(t.id="N"+String(Math.random()).slice(-12))),e=o.getField(a,!0),e.rule=s||e.rule,(i=L(t,"data-display"))&&(e.display=i),e.rule&&((null!==L(t,"data-must")||/\b(?:match|checked)\b/.test(e.rule))&&(e.must=!0),/\brequired\b/.test(e.rule)&&(e.required=!0),(n=L(t,"data-timely"))?e.timely=+n:e.timely>3&&L(t,"data-timely",e.timely),o._parseRule(e),e.old={}),F(e.target)&&L(t,"data-target",e.target),F(e.tip)&&L(t,"data-tip",e.tip),o.fields[a]=e},_parseRule:function(i){var n=_.exec(i.rule);n&&(i._i=0,n[1]&&(i.display=n[1]),n[2]&&(i._rules=[],n[2].replace(w,function(){var n=arguments;n[4]=n[4]||n[5],i._rules.push({and:"&"===n[1],not:"!"===n[2],or:"|"===n[6],method:n[3],params:n[4]?t.map(n[4].split(", "),A):e})})))},_multiValidate:function(i,n){var o=this,a=o.options;return o.hasError=!1,a.ignore&&(i=i.not(a.ignore)),i.each(function(){if(o._validate(this),o.hasError&&a.stopOnError)return!1}),n&&(o.validating=!0,t.when.apply(null,t.map(o.deferred,function(t){return t})).done(function(){n.call(o,!o.hasError),o.validating=!1})),t.isEmptyObject(o.deferred)?!o.hasError:e},_submit:function(i){var n=this,o=n.options,a=i.target,s="submit"===i.type&&"FORM"===a.tagName&&!i.isDefaultPrevented();i.preventDefault(),m&&~(m=!1)||n.submiting||"validate"===i.type&&n.$el[0]!==a||O(o.beforeSubmit)&&!1===o.beforeSubmit.call(n,a)||(n.isAjaxSubmit===e&&n._guessAjax(a),n._debug("log","\n<<< event: "+i.type),n._reset(),n.submiting=!0,n._multiValidate(n.$el.find(":verifiable"),function(e){var i,r=e||2===o.debug?"valid":"invalid";e||(o.focusInvalid&&n.$el.find("[aria-invalid]:first").focus(),i=t.map(n.errors,function(t){return t})),n.submiting=!1,n.isValid=e,O(o[r])&&o[r].call(n,a,i),n.$el.trigger(r+".form",[a,i]),n._debug("log",">>> "+r),e&&(n.vetoed?t(a).ajaxSubmit(n.ajaxFormOptions):s&&!n.isAjaxSubmit&&document.createElement("form").submit.call(a))}))},_reset:function(t){var e=this;e.errors={},t&&(e.reseting=!0,e.$el.find(":verifiable").each(function(){e._resetElement(this)}),delete e.reseting)},_resetElement:function(t,e){this._setClass(t,null),this.hideMsg(t)},_focusin:function(t){var e,i,n=this,o=n.options,a=t.target;n.validating||"click"===t.type&&document.activeElement===a||(o.focusCleanup&&"true"===L(a,"aria-invalid")&&(n._setClass(a,null),n.hideMsg(a)),i=L(a,"data-tip"),i?n.showMsg(a,{type:"tip",msg:i}):(L(a,b)&&n._parse(a),(e=L(a,"data-timely"))&&(8!==e&&9!==e||n._focusout(t))))},_focusout:function(e){var i,n,o,a,s,r,l,c,d,h=this,p=h.options,f=e.target,m=e.type,g="focusin"===m,v="validate"===m,y=0;if("compositionstart"===m&&(h.pauseValidate=!0),"compositionend"===m&&(h.pauseValidate=!1),!h.pauseValidate&&(n=f.name&&u(f)?h.$el.find('input[name="'+f.name+'"]').get(0):f,(o=h.getField(n))&&o.rule)){if(i=o._e,o._e=m,d=o.timely,!v){if(!d||u(f)&&"click"!==m)return;if(s=o.getValue(),o.ignoreBlank&&!s&&!g)return void h.hideMsg(f);if("focusout"===m){if("change"===i)return;if(2===d||8===d){if(a=o.old,!s||!a)return;o.isValid&&!a.showOk?h.hideMsg(f):h._makeMsg(f,o,a)}}else{if(d<2&&!e.data)return;if((r=+new Date)-(f._ts||0)<100)return;if(f._ts=r,"keyup"===m){if("input"===i)return;if(l=e.keyCode,c={8:1,9:1,16:1,32:1,46:1},9===l&&!s)return;if(l<48&&!c[l])return}g||(y=d<100?"click"===m||"SELECT"===f.tagName?0:400:d)}}p.ignore&&t(f).is(p.ignore)||(clearTimeout(o._t),y?o._t=setTimeout(function(){h._validate(f,o)},y):(v&&(o.old={}),h._validate(f,o)))}},_setClass:function(e,i){var n=t(e),o=this.options;o.bindClassTo&&(n=n.closest(o.bindClassTo)),n.removeClass(o.invalidClass+" "+o.validClass),null!==i&&n.addClass(i?o.validClass:o.invalidClass)},_showmsg:function(t,e,i){var n=this,o=t.target;n.$el.is(o)?P(e)?n.showMsg(e):"tip"===e&&n.$el.find(":verifiable[data-tip]",o).each(function(){n.showMsg(this,{type:e,msg:i})}):n.showMsg(o,{type:e,msg:i})},_hidemsg:function(e){var i=t(e.target);i.is(":verifiable")&&this.hideMsg(i)},_validatedField:function(e,i,n){var o=this,a=o.options,s=i.isValid=n.isValid=!!n.isValid,r=s?"valid":"invalid";n.key=i.key,n.ruleName=i._r,n.id=e.id,n.value=i.value,o.elements[i.key]=n.element=e,o.isValid=o.$el[0].isValid=s?o.isFormValid():s,s?n.type="ok":(o.submiting&&(o.errors[i.key]=n.msg),o.hasError=!0),i.old=n,O(i[r])&&i[r].call(o,e,n),O(a.validation)&&a.validation.call(o,e,n),t(e).attr("aria-invalid",!s||null).trigger(r+".field",[n,o]),o.$el.triggerHandler("validation",[n,o]),o.checkOnly||(o._setClass(e,n.skip||"tip"===n.type?null:s),o._makeMsg.apply(o,arguments))},_makeMsg:function(e,i,n){i.msgMaker&&(n=t.extend({},n),"focusin"===i._e&&(n.type="tip"),this[n.showOk||n.msg||"tip"===n.type?"showMsg":"hideMsg"](e,n,i))},_validatedRule:function(i,n,o,a){n=n||u.getField(i),a=a||{};var s,r,l,d,u=this,h=n._r,p=n.timely,f=9===p||8===p,m=!1;if(null===o)return u._validatedField(i,n,{isValid:!0,skip:!0}),void(n._i=0);if(o===e?l=!0:!0===o||""===o?m=!0:F(o)?s=o:P(o)?o.error?s=o.error:(s=o.ok,m=!0):m=!!o,r=n._rules[n._i],r.not&&(s=e,m="required"===h||!m),r.or)if(m)for(;n._i<n._rules.length&&n._rules[n._i].or;)n._i++;else l=!0;else r.and&&(n.isValid||(l=!0));l?m=!0:(m&&!1!==n.showOk&&(d=L(i,"data-ok"),s=null===d?F(n.ok)?n.ok:s:d,!F(s)&&F(n.showOk)&&(s=n.showOk),F(s)&&(a.showOk=m)),m&&!f||(s=(c(i,n,s||r.msg||u.messages[h])||u.messages.fallback).replace(/\{0\|?([^\}]*)\}/,function(t,e){return u._getDisplay(i,n.display)||e||u.messages[0]})),m||(n.isValid=m),a.msg=s,t(i).trigger((m?"valid":"invalid")+".rule",[h,s])),!f||l&&!r.and||(m||n._m||(n._m=s),n._v=n._v||[],n._v.push({type:m?l?"tip":"ok":"error",msg:s||r.msg})),u._debug("log","   "+n._i+": "+h+" => "+(m||s)),(m||f)&&n._i<n._rules.length-1?(n._i++,u._checkRule(i,n)):(n._i=0,f?(a.isValid=n.isValid,a.result=n._v,a.msg=n._m||"",n.value||"focusin"!==n._e||(a.type="tip")):a.isValid=m,u._validatedField(i,n,a),delete n._m,delete n._v)},_checkRule:function(i,n){var o,a,s,r=this,c=n.key,d=n._rules[n._i],u=d.method,h=d.params;r.submiting&&r.deferred[c]||(s=n.old,n._r=u,s&&!n.must&&!d.must&&d.result!==e&&s.ruleName===u&&s.id===i.id&&n.value&&s.value===n.value?o=d.result:(a=l(i,u)||r.rules[u]||$,o=a.call(n,i,h,n),a.msg&&(d.msg=a.msg)),P(o)&&O(o.then)?(r.deferred[c]=o,n.isValid=e,!r.checkOnly&&r.showMsg(i,{type:"loading",msg:r.messages.loading},n),o.then(function(o,a,s){var l,c=A(s.responseText),u=n.dataFilter;/jsonp?/.test(this.dataType)?c=o:"{"===c.charAt(0)&&(c=t.parseJSON(c)),l=u.call(this,c,n),l===e&&(l=u.call(this,c.data,n)),d.data=this.data,d.result=n.old?l:e,r._validatedRule(i,n,l)},function(t,e){r._validatedRule(i,n,r.messages[e]||e)}).always(function(){delete r.deferred[c]})):r._validatedRule(i,n,o))},_validate:function(t,e){var i=this;if(!t.disabled&&null===L(t,"novalidate")&&(e=e||i.getField(t))&&(e._rules||i._parse(t),e._rules))return i._debug("info",e.key),e.isValid=!0,e.element=t,e.value=e.getValue(),e.required||e.must||e.value||u(t)?(i._checkRule(t,e),e.isValid):(i._validatedField(t,e,{isValid:!0}),!0)},_debug:function(t,e){window.console&&this.options.debug&&console[t](e)},test:function(t,i){var n,o,a,s,r=this,l=k.exec(i);return l&&(a=l[1])in r.rules&&(s=l[2]||l[3],s=s?s.split(", "):e,o=r.getField(t,!0),o._r=a,o.value=o.getValue(),n=r.rules[a].call(o,t,s)),!0===n||n===e||null===n},_getDisplay:function(t,e){return F(e)?e:O(e)?e.call(this,t):""},_getMsgOpt:function(e,i){var n=i||this.options;return t.extend({type:"error",pos:d(n.msgClass),target:n.target,wrapper:n.msgWrapper,style:n.msgStyle,cls:n.msgClass,arrow:n.msgArrow,icon:n.msgIcon},F(e)?{msg:e}:e)},_getMsgDOM:function(i,n){var o,a,s,r,l=t(i);if(l.is(":verifiable")?(s=n.target||L(i,"data-target"),s&&(s=O(s)?s.call(this,i):"#"===s.charAt(0)?t(s):this.$el.find(s),s.length&&(s.is(":verifiable")?(l=s,i=s.get(0)):s.hasClass("msg-box")?o=s:r=s)),o||(a=u(i)&&i.name||!i.id?i.name:i.id,o=(r||this.$el).find(n.wrapper+'.msg-box[for="'+a+'"]'))):o=l,!n.hide&&!o.length)if(o=t("<"+n.wrapper+">").attr({class:"msg-box"+(n.cls?" "+n.cls:""),style:n.style||e,for:a}),r)o.appendTo(r);else if(u(i)){var c=l.parent();o.appendTo(c.is("label")?c.parent():c)}else o[n.pos&&"right"!==n.pos?"insertBefore":"insertAfter"](l);return o},showMsg:function(e,i,n){if(e){var o,a,s,r,l=this,c=l.options;if(P(e)&&!e.jquery&&!i)return void t.each(e,function(t,e){var i=l.elements[t]||l.$el.find(f(t))[0];l.showMsg(i,e)});t(e).is(":verifiable")&&(n=n||l.getField(e)),(a=(n||c).msgMaker)&&(i=l._getMsgOpt(i,n),e=(e.name&&u(e)?l.$el.find('input[name="'+e.name+'"]'):t(e)).get(0),i.msg||"error"===i.type||null!==(s=L(e,"data-"+i.type))&&(i.msg=s),F(i.msg)&&(r=l._getMsgDOM(e,i),!S.test(r[0].className)&&r.addClass(i.cls),6===M&&"bottom"===i.pos&&(r[0].style.marginTop=t(e).outerHeight()+"px"),r.html(a.call(l,i))[0].style.display="",O(o=n&&n.msgShow||c.msgShow)&&o.call(l,r,i.type)))}},hideMsg:function(e,i,n){var o,a,s=this,r=s.options;e=t(e).get(0),t(e).is(":verifiable")&&(n=n||s.getField(e))&&(n.isValid||s.reseting)&&L(e,"aria-invalid",null),i=s._getMsgOpt(i,n),i.hide=!0,a=s._getMsgDOM(e,i),a.length&&(O(o=n&&n.msgHide||r.msgHide)?o.call(s,a,i.type):(a[0].style.display="none",a[0].innerHTML=""))},getField:function(t,i){var n,o,a=this;if(F(t))n=t,t=e;else{if(L(t,b))return a._parse(t);n=t.id&&"#"+t.id in a.fields||!t.name?"#"+t.id:t.name}return((o=a.fields[n])||i&&(o=new a.Field(n)))&&(o.element=t),o},setField:function(t,e){var i={};t&&(F(t)?i[t]=e:i=t,this._initFields(i))},isFormValid:function(){var t,e,i=this.fields;for(t in i)if(e=i[t],e._rules&&(e.required||e.must||e.value)&&!e.isValid)return!1;return!0},holdSubmit:function(t){this.submiting=t===e||t},cleanUp:function(){this._reset(1)},destroy:function(){this._reset(1),this.$el.off(v).removeData(g),L(this.$el[0],"novalidate",this._NOVALIDATE)}},t(window).on("beforeunload",function(){this.focus()}),t(document).on("click",":submit",function(){var t,e=this;e.form&&((t=e.getAttributeNode("formnovalidate"))&&null!==t.nodeValue||null!==L(e,"novalidate"))&&(m=!0)}).on("focusin submit validate","form,."+y,function(e){if(null===L(this,"novalidate")){var i,n=t(this);!n.data(g)&&(i=r(this))&&(t.isEmptyObject(i.fields)?(L(this,"novalidate","novalidate"),n.off(v).removeData(g)):"focusin"===e.type?i._focusin(e):i._submit(e))}}),new a({fallback:"This field is not valid.",loading:"Validating..."}),new o({required:function(e,i){var n=this,o=A(n.value),a=!0;if(i)if(1===i.length){if(p(i[0])){if(n.rules[i[0]]){if(!o&&!n.test(e,i[0]))return null;n._r="required"}}else if(!o&&!t(i[0],n.$el).length)return null}else if("not"===i[0])t.each(i.slice(1),function(){return a=o!==A(this)});else if("from"===i[0]){var s,r=n.$el.find(i[1]);return a=r.filter(function(){var t=n.getField(this);return t&&!!A(t.getValue())}).length>=(i[2]||1),a?o||(s=null):s=c(r[0],n)||!1,t(e).data("_validated_")||r.data("_validated_",1).each(function(){e!==this&&n._validate(this)}).removeData("_validated_"),s}return a&&!!o},integer:function(t,e){var i,n="0|",o="[1-9]\\d*",a=e?e[0]:"*";switch(a){case"+":i=o;break;case"-":i="-"+o;break;case"+0":i=n+o;break;case"-0":i="0|-"+o;break;default:i="0|-?"+o}return i="^(?:"+i+")$",new RegExp(i).test(this.value)||this.messages.integer&&this.messages.integer[a]},match:function(e,i){if(i){var n,o,a,s,r,l,c,d=this,u=!0,p="eq";if(1===i.length?a=i[0]:(p=i[0],a=i[1]),r=f(a),l=d.$el.find(r)[0]){if(c=d.getField(l),n=d.value,o=c.getValue(),d._match||(d.$el.on("valid.field"+v,r,function(){t(e).trigger("validate")}),d._match=c._match=1),!d.required&&""===n&&""===o)return null;if(s=i[2],s&&(/^date(time)?$/i.test(s)?(n=h(n),o=h(o)):"time"===s&&(n=+n.replace(/:/g,""),o=+o.replace(/:/g,""))),"eq"!==p&&!isNaN(+n)&&isNaN(+o))return!0;switch(p){case"lt":u=+n<+o;break;case"lte":u=+n<=+o;break;case"gte":u=+n>=+o;break;case"gt":u=+n>+o;break;case"neq":u=n!==o;break;default:u=n===o}return u||P(d.messages.match)&&d.messages.match[p].replace("{1}",d._getDisplay(l,c.display||a))}}},range:function(t,e){return this.getRangeMsg(this.value,e)},checked:function(t,e){if(u(t)){var i,n,o=this;return t.name?n=o.$el.find('input[name="'+t.name+'"]').filter(function(){var t=this;return!i&&u(t)&&(i=t),!t.disabled&&t.checked}).length:(i=t,n=i.checked),e?o.getRangeMsg(n,e):!!n||c(i,o,"")||o.messages.required||!1}},length:function(t,e){var i=this.value,n=("true"===e[1]?i.replace(C,"xx"):i).length;return this.getRangeMsg(n,e,e[1]?"_2":"")},remote:function(e,i){if(i){var n,o=this,a=T.exec(i[0]),s=o._rules[o._i],r={},l="",c=a[3],d=a[2]||"POST",u=(a[1]||this.validator.options.remoteDataType||"").toLowerCase();return s.must=!0,(r[e.name]=o.value,i[1]&&t.map(i.slice(1),function(t){var e,i;~t.indexOf("=")?l+="&"+t:(e=t.split(":"),t=A(e[0]),i=A(e[1])||t,r[t]=o.$el.find(f(i)).val())}),r=t.param(r)+l,!o.must&&s.data&&s.data===r)?s.result:("cors"!==u&&/^https?:/.test(c)&&!~c.indexOf(location.host)&&(n="jsonp"),t.ajax({url:c,type:d,data:r,dataType:n}))}},filter:function(t,e){var i=this.value,n=i.replace(e?new RegExp("["+e[0]+"]","gm"):D,"");n!==i&&this.setValue(n)}}),i.config=function(e,i){function n(t,e){"rules"===t?new o(e):"messages"===t?new a(e):t in R?R[t]=e:N[t]=e}P(e)?t.each(e,n):F(e)&&n(e,i)},i.setTheme=function(e,i){P(e)?t.extend(!0,j,e):F(e)&&P(i)&&(j[e]=t.extend(j[e],i))},i.load=function(e){if(e){var n,o,a,s=document,r={},l=s.scripts[0];e.replace(/([^?=&]+)=([^&#]*)/g,function(t,e,i){r[e]=i}),n=r.dir||i.dir,i.css||""===r.css||(o=s.createElement("link"),o.rel="stylesheet",o.href=i.css=n+"jquery.validator.css",l.parentNode.insertBefore(o,l)),!i.local&&~e.indexOf("local")&&""!==r.local&&(i.local=(r.local||s.documentElement.lang||"en").replace("_","-"),i.pending=1,o=s.createElement("script"),o.src=n+"local/"+i.local+".js",a="onload"in o?"onload":"onreadystatechange",o[a]=function(){o.readyState&&!/loaded|complete/.test(o.readyState)||(o=o[a]=null,delete i.pending,t(window).triggerHandler("validatorready"))},l.parentNode.insertBefore(o,l))}},function(){for(var t,e,n=document.scripts,o=n.length,a=/(.*validator(?:\.min)?.js)(\?.*(?:local|css|dir)(?:=[\w\-]*)?)?/;o--&&!e;)t=n[o],e=(t.hasAttribute?t.src:t.getAttribute("src",4)||"").match(a);e&&(i.dir=e[1].split("/").slice(0,-1).join("/")+"/",i.load(e[2]))}(),t[g]=i}),function(t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define("validator-lang",["jquery"],t):t(jQuery)}(function(t){t.validator.config({rules:{digits:[/^\d+$/,"请填写数字"],letters:[/^[a-z]+$/i,"请填写字母"],date:[/^\d{4}-\d{2}-\d{2}$/,"请填写有效的日期，格式:yyyy-mm-dd"],time:[/^([01]\d|2[0-3])(:[0-5]\d){1,2}$/,"请填写有效的时间，00:00到23:59之间"],email:[/^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/i,"请填写有效的邮箱"],url:[/^(https?|s?ftp):\/\/\S+$/i,"请填写有效的网址"],qq:[/^[1-9]\d{4,}$/,"请填写有效的QQ号"],IDcard:[/^\d{6}(19|2\d)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)?$/,"请填写正确的身份证号码"],tel:[/^(?:(?:0\d{2,3}[\- ]?[1-9]\d{6,7})|(?:[48]00[\- ]?[1-9]\d{6}))$/,"请填写有效的电话号码"],mobile:[/^1[3-9]\d{9}$/,"请填写有效的手机号"],zipcode:[/^\d{6}$/,"请检查邮政编码格式"],chinese:[/^[\u0391-\uFFE5]+$/,"请填写中文字符"],username:[/^\w{3,12}$/,"请填写3-12位数字、字母、下划线"],password:[/^[\S]{6,16}$/,"请填写6-16位字符，不能包含空格"],accept:function(e,i){if(!i)return!0;var n=i[0],o=t(e).val();return"*"===n||new RegExp(".(?:"+n+")$","i").test(o)||this.renderMsg("只接受{1}后缀的文件",n.replace(/\|/g,","))}},messages:{0:"此处",fallback:"{0}格式不正确",loading:"正在验证...",error:"网络异常",timeout:"请求超时",required:"{0}不能为空",remote:"{0}已被使用",integer:{"*":"请填写整数","+":"请填写正整数","+0":"请填写正整数或0","-":"请填写负整数","-0":"请填写负整数或0"},match:{eq:"{0}与{1}不一致",neq:"{0}与{1}不能相同",lt:"{0}必须小于{1}",gt:"{0}必须大于{1}",lte:"{0}不能大于{1}",gte:"{0}不能小于{1}"},range:{rg:"请填写{1}到{2}的数",gte:"请填写不小于{1}的数",lte:"请填写最大{1}的数",gtlt:"请填写{1}到{2}之间的数",gt:"请填写大于{1}的数",lt:"请填写小于{1}的数"},checked:{eq:"请选择{1}项",rg:"请选择{1}到{2}项",gte:"请至少选择{1}项",lte:"请最多选择{1}项"},length:{eq:"请填写{1}个字符",rg:"请填写{1}到{2}个字符",gte:"请至少填写{1}个字符",lte:"请最多填写{1}个字符",eq_2:"",rg_2:"",gte_2:"",lte_2:""}}});var e='<span class="n-arrow"><b>◆</b><i>◆</i></span>';t.validator.setTheme({simple_right:{formClass:"n-simple",msgClass:"n-right"},simple_bottom:{formClass:"n-simple",msgClass:"n-bottom"},yellow_top:{formClass:"n-yellow",msgClass:"n-top",msgArrow:e},yellow_right:{formClass:"n-yellow",msgClass:"n-right",msgArrow:e},yellow_right_effect:{formClass:"n-yellow",msgClass:"n-right",msgArrow:e,msgShow:function(t,e){var i=t.children();i.is(":animated")||("error"===e?i.css({left:"20px",opacity:0}).delay(100).show().stop().animate({left:"-4px",opacity:1},150).animate({left:"3px"},80).animate({left:0},80):i.css({left:0,opacity:1}).fadeIn(200))},msgHide:function(t,e){t.children().stop().delay(100).show().animate({left:"20px",opacity:0},300,function(){t.hide()})}}})}),define("form",["jquery","bootstrap","upload","validator","validator-lang"],function(t,e,i,n,e){var o={config:{fieldlisttpl:'<dd class="form-inline"><input type="text" name="<%=name%>[<%=index%>][key]" class="form-control" value="<%=key%>" placeholder="<%=options.keyPlaceholder||\'\'%>" size="10" /> <input type="text" name="<%=name%>[<%=index%>][value]" class="form-control" value="<%=value%>" placeholder="<%=options.valuePlaceholder||\'\'%>" /> <span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></dd>'},events:{validator:function(e,i,n,a){e.is("form")&&(e.validator(t.extend({rules:{username:[/^\w{3,30}$/,__("Username must be 3 to 30 characters")],password:[/^[\S]{6,30}$/,__("Password must be 6 to 30 characters")]},validClass:"has-success",invalidClass:"has-error",bindClassTo:".form-group",formClass:"n-default n-bootstrap",msgClass:"n-right",stopOnError:!0,display:function(e){return t(e).closest(".form-group").find(".control-label").text().replace(/\:/,"")},dataFilter:function(t){return 1===t.code?t.msg?{ok:t.msg}:"":t.msg},target:function(e){var i=t(e).data("target");if(i&&t(i).length>0)return t(i);var n=t(e).closest(".form-group"),o=n.find("span.msg-box");return o.length?o:[]},valid:function(s){var r=this,l=t(".layer-footer [type=submit]",e);return r.holdSubmit(!0),l.addClass("disabled"),o.api.submit(t(s),function(e,n){if(r.holdSubmit(!1),l.removeClass("disabled"),!1===t(this).triggerHandler("success.form",[e,n]))return!1;if("function"==typeof i&&!1===i.call(t(this),e,n))return!1;var o=n.hasOwnProperty("msg")&&""!==n.msg?n.msg:__("Operation completed");if(parent.Toastr.success(o),parent.$(".btn-refresh").trigger("click"),window.name){var a=parent.Layer.getFrameIndex(window.name);parent.Layer.close(a)}return!1},function(e,i){return r.holdSubmit(!1),!1!==t(this).triggerHandler("error.form",[e,i])&&(l.removeClass("disabled"),("function"!=typeof n||!1!==n.call(t(this),e,i))&&void 0)},a)||(r.holdSubmit(!1),l.removeClass("disabled")),!1}},e.data("validator-options")||{})),t(".layer-footer [type=submit],.fixed-footer [type=submit],.normal-footer [type=submit]",e).removeClass("disabled"),e.on("click",".layer-close",function(){if(window.name){var t=parent.Layer.getFrameIndex(window.name);parent.Layer.close(t)}return!1}))},selectpicker:function(e){t(".selectpicker",e).length>0&&require(["bootstrap-select","bootstrap-select-lang"],function(){t.fn.selectpicker.Constructor.BootstrapVersion="3",t(".selectpicker",e).selectpicker(),t(e).on("reset",function(){setTimeout(function(){t(".selectpicker").selectpicker("refresh").trigger("change")},1)})})},selectpage:function(e){t(".selectpage",e).length>0&&(require(["selectpage"],function(){t(".selectpage",e).selectPage({eAjaxSuccess:function(t){return t.list=void 0!==t.rows?t.rows:void 0!==t.list?t.list:[],t.totalRow=void 0!==t.total?t.total:void 0!==t.totalRow?t.totalRow:t.list.length,t}})}),t(document).on("change",".sp_hidden",function(){t(this).trigger("validate")}),t(document).on("change",".sp_input",function(){t(this).closest(".sp_container").find(".sp_hidden").trigger("change")}),t(e).on("reset",function(){setTimeout(function(){t(".selectpage",e).each(function(){t(this).data("selectPageObject").elem.hidden.val(t(this).val()),t(this).selectPageRefresh()})},1)}))},cxselect:function(e){t("[data-toggle='cxselect']",e).length>0&&require(["cxselect"],function(){t.cxSelect.defaults.jsonName="name",t.cxSelect.defaults.jsonValue="value",t.cxSelect.defaults.jsonSpace="data",t("[data-toggle='cxselect']",e).cxSelect()})},citypicker:function(e){t("[data-toggle='city-picker']",e).length>0&&require(["citypicker"],function(){t(e).on("reset",function(){setTimeout(function(){t("[data-toggle='city-picker']").citypicker("refresh")},1)})})},datetimepicker:function(e){t(".datetimepicker",e).length>0&&require(["bootstrap-datetimepicker"],function(){var i={format:"YYYY-MM-DD HH:mm:ss",icons:{time:"fa fa-clock-o",date:"fa fa-calendar",up:"fa fa-chevron-up",down:"fa fa-chevron-down",previous:"fa fa-chevron-left",next:"fa fa-chevron-right",today:"fa fa-history",clear:"fa fa-trash",close:"fa fa-remove"},showTodayButton:!0,showClose:!0};t(".datetimepicker",e).parent().css("position","relative"),t(".datetimepicker",e).datetimepicker(i).on("dp.change",function(e){t(this,document).trigger("changed")})})},daterangepicker:function(e){t(".datetimerange",e).length>0&&require(["bootstrap-daterangepicker"],function(){var i={};i[__("Today")]=[Moment().startOf("day"),Moment().endOf("day")],i[__("Yesterday")]=[Moment().subtract(1,"days").startOf("day"),Moment().subtract(1,"days").endOf("day")],i[__("Last 7 Days")]=[Moment().subtract(6,"days").startOf("day"),Moment().endOf("day")],i[__("Last 30 Days")]=[Moment().subtract(29,"days").startOf("day"),Moment().endOf("day")],i[__("This Month")]=[Moment().startOf("month"),Moment().endOf("month")],i[__("Last Month")]=[Moment().subtract(1,"month").startOf("month"),Moment().subtract(1,"month").endOf("month")];var n={timePicker:!1,autoUpdateInput:!1,timePickerSeconds:!0,timePicker24Hour:!0,autoApply:!0,locale:{format:"YYYY-MM-DD HH:mm:ss",customRangeLabel:__("Custom Range"),applyLabel:__("Apply"),cancelLabel:__("Clear")},ranges:i},o=function(e,i){t(this.element).val(e.format(this.locale.format)+" - "+i.format(this.locale.format)),t(this.element).trigger("change").trigger("validate")};t(".datetimerange",e).each(function(){var e="function"==typeof t(this).data("callback")?t(this).data("callback"):o
;t(this).on("apply.daterangepicker",function(t,i){e.call(i,i.startDate,i.endDate)}),t(this).on("cancel.daterangepicker",function(e,i){t(this).val("").trigger("change").trigger("validate")}),t(this).daterangepicker(t.extend(!0,{},n,t(this).data()||{},t(this).data("daterangepicker-options")||{}))})})},plupload:function(t){o.events.faupload(t)},faupload:function(e){t(".plupload,.faupload",e).length>0&&i.api.upload(t(".plupload,.faupload",e))},faselect:function(e){t(".faselect,.fachoose",e).length>0&&t(".faselect,.fachoose",e).off("click").on("click",function(){var e=this,i=!!t(this).data("multiple")&&t(this).data("multiple"),n=t(this).data("mimetype")?t(this).data("mimetype"):"",o=t(this).data("admin-id")?t(this).data("admin-id"):"",a=t(this).data("user-id")?t(this).data("user-id"):"";n=n.replace(/\/\*/gi,"/");var s=t(this).data("url")?t(this).data("url"):"undefined"!=typeof Backend?"general/attachment/select":"user/attachment";return parent.Fast.api.open(s+"?element_id="+t(this).attr("id")+"&multiple="+i+"&mimetype="+n+"&admin_id="+o+"&user_id="+a,__("Choose"),{callback:function(i){var n=t(e),o=t(n).data("maxcount"),a=t(n).data("input-id")?t(n).data("input-id"):"";if(o=void 0!==o?o:0,a&&i.multiple){var s=[],r=t("#"+a),l=t.trim(r.val());""!==l&&s.push(r.val());var c=""===l?0:l.split(/\,/).length,d=""!==i.url?i.url.split(/\,/):[];if(t.each(d,function(t,e){var i=Config.upload.fullmode?Fast.api.cdnurl(e):e;s.push(i)}),o>0){var u=o-c;if(d.length>u)return Toastr.error(__("You can choose up to %d file%s",u)),!1}var h=s.join(",");r.val(h).trigger("change").trigger("validate")}else if(a){var p=Config.upload.fullmode?Fast.api.cdnurl(i.url):i.url;t("#"+a).val(p).trigger("change").trigger("validate")}n.trigger("fa.event.selectedfile",i)}}),!1})},fieldlist:function(e){t(".fieldlist",e).length>0&&require(["dragsort","template"],function(i,n){var a=function(i){var n={},o=i.data("name"),a=t("textarea[name='"+o+"']",e),s=i.data("template");t.each(t("input,select,textarea",i).serializeArray(),function(t,e){var i=/\[(\w+)\]\[(\w+)\]$/g,o=i.exec(e.name);if(!o)return!0;o[1]="x"+parseInt(o[1]),void 0===n[o[1]]&&(n[o[1]]={}),n[o[1]][o[2]]=e.value});var r=i.data("usearray")||!1,l=i.data("keepempty")||!1,c=s||r?[]:{},d=Object.keys(Object.values(n)[0]||{}),u=!r&&d.indexOf("value")>-1&&(1===d.length||2===d.length&&d.indexOf("key")>-1);u&&2===d.length&&(c={}),t.each(n,function(t,e){e&&(u?2===d.length?(""!=e.key||l)&&(c["__PLACEHOLDKEY__"+e.key]=e.value):c.push(e.value):c.push(e))}),a.val(JSON.stringify(c).replace(/__PLACEHOLDKEY__/g,""))},s=function(e,i,a){var s=e.data("tag")||(e.is("table")?"tr":"dd"),r=e.data("index"),l=e.data("name"),c=e.data("template"),d=e.data();r=r?parseInt(r):0,e.data("index",r+1),i=i||{},i=void 0===i.key||void 0===i.value?{key:"",value:i}:i;var u=e.data("fieldlist-options")||{},h={index:r,name:l,data:d,options:u,key:i.key,value:i.value,row:i.value},p=c?n(c,h):n.render(o.config.fieldlisttpl,h),f=t(p);return!1!==u.deleteBtn&&!1!==u.removeBtn||!a||f.find(".btn-remove").remove(),!1===u.dragsortBtn&&a&&f.find(".btn-dragsort").remove(),!0!==u.readonlyKey&&!0!==u.disableKey||!a||f.find("input[name$='[key]']").prop("readonly",!0),f.attr("fieldlist-item",!0),f.insertAfter(t(s+"[fieldlist-item]",e).length>0?t(s+"[fieldlist-item]:last",e):t(s+":first",e)),t(".btn-append,.append",e).length>0?t(".btn-append,.append",e).trigger("fa.event.appendfieldlist",f):e.trigger("fa.event.appendfieldlist",f),f},r=t(".fieldlist",e);e.on("reset",function(){setTimeout(function(){r.trigger("fa.event.refreshfieldlist")})}),t(document).on("change keyup changed",".fieldlist input,.fieldlist textarea,.fieldlist select",function(){var e=t(this).closest(".fieldlist");a(e)}),r.on("click",".btn-append,.append",function(e,i){var n=t(this).closest(".fieldlist");s(n,i),a(n)}),r.on("click",".btn-remove",function(){var e=t(this).closest(".fieldlist"),i=e.data("tag")||(e.is("table")?"tr":"dd");t(this).closest(i).remove(),a(e)}),r.on("fa.event.appendtofieldlist",function(e,i){var n=t(this);s(n,i),a(n)}),r.on("fa.event.refreshfieldlist",function(){var i=t(this),n=t("textarea[name='"+i.data("name")+"']",e);t("[fieldlist-item]",i).remove();var o={};try{var a=n.val().replace(/"(\d+)"\:/g,'"__PLACEHOLDERKEY__$1":');o=JSON.parse(a)}catch(t){}t.each(o,function(t,e){s(i,{key:t.toString().replace("__PLACEHOLDERKEY__",""),value:e},!0)})}),r.each(function(){var i=t(this),n=i.data("tag")||(i.is("table")?"tr":"dd");i.dragsort({itemSelector:n,dragSelector:".btn-dragsort",dragEnd:function(){a(i)},placeHolderTemplate:t("<"+n+"/>")}),"object"==typeof i.data("options")&&!1===i.data("options").appendBtn&&t(".btn-append,.append",i).hide(),t("textarea[name='"+i.data("name")+"']",e).on("fa.event.refreshfieldlist",function(){t(this).closest(".fieldlist").trigger("fa.event.refreshfieldlist")})}),r.trigger("fa.event.refreshfieldlist")})},switcher:function(e){e.on("click","[data-toggle='switcher']",function(){if(t(this).hasClass("disabled"))return!1;var e=t.proxy(function(){var e=t(this).prev("input");if(e=t(this).data("input-id")?t("#"+t(this).data("input-id")):e,e.length>0){var i=t(this).data("yes"),n=t(this).data("no");e.val()==i?(e.val(n),t("i",this).addClass("fa-flip-horizontal text-gray")):(e.val(i),t("i",this).removeClass("fa-flip-horizontal text-gray")),e.trigger("change")}},this);return void 0!==t(this).data("confirm")?Layer.confirm(t(this).data("confirm"),function(t){e(),Layer.close(t)}):e(),!1})},bindevent:function(t){},slider:function(e){t("[data-role='slider'],input.slider",e).length>0&&require(["bootstrap-slider"],function(){t("[data-role='slider'],input.slider").removeClass("hidden").css("width",function(e,i){return t(this).parents(".form-control").width()}).slider().on("slide",function(e){var i=t(this).data();void 0!==i.unit&&t(this).parents(".form-control").siblings(".value").text(e.value+i.unit)})})},tagsinput:function(e){t("[data-role='tagsinput']",e).length>0&&require(["tagsinput","autocomplete"],function(){t("[data-role='tagsinput']").tagsinput(),e.on("reset",function(){setTimeout(function(){t("[data-role='tagsinput']").tagsinput("reset")},0)})})},autocomplete:function(e){t("[data-role='autocomplete']",e).length>0&&require(["autocomplete"],function(){t("[data-role='autocomplete']").autocomplete({onSelect:function(){t(this).trigger("change").trigger("validate")}})})},favisible:function(e){if(0!=t("[data-favisible]",e).length){var i=function(i){var n,o,a=i.split(/&&/),s=0,r=/^([a-z0-9\_]+)([>|<|=|\!]=?)(.*)$/i,l=/^('|")(.*)('|")$/,c=/^regex:(.*)$/,d={">":function(t,e){return t>e},">=":function(t,e){return t>=e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},"==":function(t,e){return t==e},"!=":function(t,e){return t!=e},in:function(t,e){return e.split(/\,/).indexOf(t)>-1},regex:function(t,e){var i=e.match(/^\/(.*?)\/([gim]*)$/);return(i?new RegExp(i[1],i[2]):new RegExp(e)).test(t)}},u=e.find(":disabled").removeAttr("disabled"),h=e.serializeArray(),p={};return u.attr("disabled","disabled"),t(h).each(function(t,e){n=e.name,o=e.value,n="[]"===n.substr(-2)?n.substr(0,n.length-2):n,p[n]=void 0!==p[n]?[p[n],o].join(","):o}),t.each(a,function(e,i){var n=r.exec(i);if(n){var o=n[1],a=n[2],u=n[3].toString();if("="===a){var h=l.exec(u);a=h?"==":"in",u=h?h[2]:u}var f=c.exec(u);f&&(a="regex",u=f[1]);var m="row["+o+"]";if(void 0===p[m])return!1;var g=p[m];t.isArray(g)&&(g=p[m].join(",")),[">",">=","<","<="].indexOf(a)>-1&&(g=parseFloat(g),u=parseFloat(u));var v=d[a](g,u);s+=v?1:0}}),s===a.length};e.on("keyup change click configchange","input,textarea,select",function(){t("[data-favisible][data-favisible!='']",e).each(function(){var e=t(this).data("favisible"),n=e?e.toString().split(/\|\|/):[],o=0;t.each(n,function(t,e){i(e)&&o++}),o>0?t(this).removeClass("hidden"):t(this).addClass("hidden")})}),setTimeout(function(){var t=e.data("validator");t&&(t.options.ignore+=(t.options.ignore?",":"")+".hidden[data-favisible] :hidden,.hidden[data-favisible]:hidden")},0),t("input,textarea,select",e).trigger("configchange")}}},api:{submit:function(e,i,n,o){if(0===e.length)return Toastr.error("表单未初始化完成,无法提交"),!1;if("function"==typeof o&&!1===o.call(e,i,n))return!1;var a=e.attr("method")?e.attr("method").toUpperCase():"GET";a=!a||"GET"!==a&&"POST"!==a?"GET":a,url=e.attr("action"),url=url||location.href;var s={},r=t("[name$='[]']",e);if(r.length>0){var l=e.serializeArray().map(function(e){return t(e).prop("name")});t.each(r,function(e,i){l.indexOf(t(this).prop("name"))<0&&(s[t(this).prop("name")]="")})}return Fast.api.ajax({type:a,url:url,data:e.serialize()+(Object.keys(s).length>0?"&"+t.param(s):""),dataType:"json",complete:function(e){var i=e.getResponseHeader("__token__");i&&t("input[name='__token__']").val(i)}},function(n,o){if(t(".form-group",e).removeClass("has-feedback has-success has-error"),n&&"object"==typeof n&&(void 0!==n.token&&t("input[name='__token__']").val(n.token),void 0!==n.callback&&"function"==typeof n.callback&&n.callback.call(e,n)),"function"==typeof i&&!1===i.call(e,n,o))return!1},function(i,o){if(i&&"object"==typeof i&&void 0!==i.token&&t("input[name='__token__']").val(i.token),"function"==typeof n&&!1===n.call(e,i,o))return!1}),!0},bindevent:function(e,i,n,a){e="object"==typeof e?e:t(e);var s=o.events;s.bindevent(e),s.validator(e,i,n,a),s.selectpicker(e),s.daterangepicker(e),s.selectpage(e),s.cxselect(e),s.citypicker(e),s.datetimepicker(e),s.faupload(e),s.faselect(e),s.fieldlist(e),s.slider(e),s.switcher(e),s.tagsinput(e),s.autocomplete(e),s.favisible(e)},custom:{}}};return o}),function(t){"use strict";var e=[],i=t.fn.bootstrapTable.utils.sprintf,n=function(e,n){var a=o(e,n),s=i('<div class="commonsearch-table %s">',n.options.searchFormVisible?"":"hidden");s+=a,s+="</div>",n.$container.prepend(t(s)),n.$commonsearch=t(".commonsearch-table",n.$container);var r=t("form.form-commonsearch",n.$commonsearch);require(["form"],function(t){t.api.bindevent(r),r.validator("destroy")}),r.on("submit",function(t){return t.preventDefault(),n.onCommonSearch(),!1}),r.on("click","button[type=reset]",function(t){r[0].reset(),setTimeout(function(){n.onCommonSearch()},1)})},o=function(n,o){if(o.options.searchFormTemplate)return Template(o.options.searchFormTemplate,{columns:n,table:o});var r=[];r.push(i('<form class="form-horizontal form-commonsearch" novalidate method="post" action="%s" >',o.options.actionForm)),r.push("<fieldset>"),o.options.titleForm.length>0&&r.push(i("<legend>%s</legend>",o.options.titleForm)),r.push('<div class="row">');for(var l in n){var c=n[l];if(!c.checkbox&&!c.radio&&c.field&&"operate"!==c.field&&c.searchable&&!1!==c.operate){var d=Fast.api.query(c.field),u=Fast.api.query(c.field+"-operate"),h=o.options.renderDefault&&(void 0===c.renderDefault||c.renderDefault);c.defaultValue=h&&d?d:void 0===c.defaultValue?"":c.defaultValue,c.operate=h&&u?u:void 0===c.operate?"=":c.operate,e.push(c),r.push('<div class="form-group col-xs-12 col-sm-6 col-md-4 col-lg-3">'),r.push(i('<label for="%s" class="control-label col-xs-4">%s</label>',c.field,c.title)),r.push('<div class="col-xs-8">'),c.operate=c.operate?c.operate.toUpperCase():"=",r.push(i('<input type="hidden" class="form-control operate" name="%s-operate" data-name="%s" value="%s" readonly>',c.field,c.field,c.operate));var p=void 0===c.addClass?void 0===c.addclass?"form-control":"form-control "+c.addclass:"form-control "+c.addClass,f=void 0===c.extend?"":c.extend,m=void 0===c.style?"":i('style="%s"',c.style);if(f=void 0!==c.data&&""==f?c.data:f,f=void 0!==c.autocomplete?f+' autocomplete="'+(!1===c.autocomplete||"off"===c.autocomplete?"off":"on")+'"':f,c.searchList)if("function"==typeof c.searchList)r.push(c.searchList.call(this,c));else{var g=[i('<option value="">%s</option>',o.options.formatCommonChoose())];"object"==typeof c.searchList&&"function"==typeof c.searchList.then?function(e,i){t.when(e.searchList).done(function(n){var o=[];n.data&&n.data.searchlist&&t.isArray(n.data.searchlist)?o=n.data.searchlist:n.constructor!==Array&&n.constructor!==Object||(o=n);var a=s(o,e,i);t("form.form-commonsearch select[name='"+e.field+"']",i.$container).html(a.join("")).trigger("change")})}(c,o):g=s(c.searchList,c,o),r.push(i('<select class="%s" name="%s" %s %s>%s</select>',p,c.field,m,f,g.join("")))}else{var v=void 0===c.placeholder?c.title:c.placeholder,y=void 0===c.type?"text":c.type,b=void 0===c.defaultValue?"":c.defaultValue;if(/BETWEEN$/.test(c.operate)){var x=b.toString().match(/\|/)?b.split("|"):["",""],w=v.toString().match(/\|/)?v.split("|"):[v,v];r.push('<div class="row row-between">'),r.push(i('<div class="col-xs-6"><input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s-min" data-index="%s" %s %s></div>',y,p,c.field,x[0],w[0],c.field,l,m,f)),r.push(i('<div class="col-xs-6"><input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s-max" data-index="%s" %s %s></div>',y,p,c.field,x[1],w[1],c.field,l,m,f)),r.push("</div>")}else r.push(i('<input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s" data-index="%s" %s %s>',y,p,c.field,b,v,c.field,l,m,f))}r.push("</div>"),r.push("</div>")}}return r.push('<div class="form-group col-xs-12 col-sm-6 col-md-4 col-lg-3">'),r.push(a(o).join("")),r.push("</div>"),r.push("</div>"),r.push("</fieldset>"),r.push("</form>"),r.join("")},a=function(t){var e=[],n=t.options.formatCommonSubmitButton(),o=t.options.formatCommonResetButton();return e.push('<div class="col-sm-8 col-xs-offset-4">'),e.push(i('<button type="submit" class="btn btn-success" formnovalidate>%s</button> ',n)),e.push(i('<button type="reset" class="btn btn-default" >%s</button> ',o)),e.push("</div>"),e},s=function(e,n,o){var a=e.constructor===Array,s=[];return s.push(i('<option value="">%s</option>',o.options.formatCommonChoose())),t.each(e,function(t,e){e.constructor===Object?(t=e.id,e=e.name):t=a?e:t,s.push(i("<option value='"+Fast.api.escape(t)+"' %s>"+Fast.api.escape(e)+"</option>",t==n.defaultValue?"selected":""))}),s},r=function(t){return!(!t.options.commonSearch||"server"!=t.options.sidePagination||!t.options.url)},l=function(i,n){var o={},a={},s="";return t("form.form-commonsearch .operate",i.$commonsearch).each(function(r){var l=t(this).data("name"),c=t(this).is("select")?t("option:selected",this).val():t(this).val().toUpperCase(),d=t("[name='"+l+"']",i.$commonsearch);if(0==d.length)return!0;var u=e[r],h=!i.options.searchFormTemplate&&u&&"function"==typeof u.process?u.process:null;if(d.length>1)if(/BETWEEN$/.test(c)){var p=t.trim(t("[name='"+l+"']:first",i.$commonsearch).val()),f=t.trim(t("[name='"+l+"']:last",i.$commonsearch).val());p.length||f.length?(h&&(p=h(p,"begin"),f=h(f,"end")),s=p+","+f):s="",t("[name='"+l+"']:first",i.$commonsearch).hasClass("datetimepicker")&&(c="RANGE")}else s=t("[name='"+l+"']:checked",i.$commonsearch).val(),s=h?h(s):s;else s=h?h(d.val()):d.val();if(n&&(""===s||null==s||t.isArray(s)&&0===s.length)&&!c.match(/null/i))return!0;o[l]=c,a[l]=s}),{op:o,filter:a}},c=function(e,i,n){return e.filter="Object"==typeof e.filter?e.filter:e.filter?JSON.parse(e.filter):{},e.op="Object"==typeof e.op?e.op:e.op?JSON.parse(e.op):{},e.filter=t.extend({},e.filter,i.filter),e.op=t.extend({},e.op,i.op),n&&t.each(e.filter,function(i,n){(""==n||null==n||t.isArray(n)&&0==n.length)&&!e.op[i].match(/null/i)&&(delete e.filter[i],delete e.op[i])}),e.filter=JSON.stringify(e.filter),e.op=JSON.stringify(e.op),e};t.extend(t.fn.bootstrapTable.defaults,{commonSearch:!1,titleForm:"Common search",actionForm:"",searchFormTemplate:"",searchFormVisible:!0,searchClass:"searchit",showSearch:!0,renderDefault:!0,onCommonSearch:function(t,e){return!1},onPostCommonSearch:function(t){return!1}}),t.extend(t.fn.bootstrapTable.defaults.icons,{commonSearchIcon:"glyphicon-search"}),t.extend(t.fn.bootstrapTable.Constructor.EVENTS,{"common-search.bs.table":"onCommonSearch","post-common-search.bs.table":"onPostCommonSearch"}),t.extend(t.fn.bootstrapTable.locales[t.fn.bootstrapTable.defaults.locale],{formatCommonSearch:function(){return"Common search"},formatCommonSubmitButton:function(){return"Search"},formatCommonResetButton:function(){return"Reset"},formatCommonCloseButton:function(){return"Close"},formatCommonChoose:function(){return"Choose"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales);var d=t.fn.bootstrapTable.Constructor,u=d.prototype.initHeader,h=d.prototype.initToolbar,p=d.prototype.load,f=d.prototype.initSearch;d.prototype.initHeader=function(){u.apply(this,Array.prototype.slice.apply(arguments)),this.$header.find("th[data-field]").each(function(e){var i=t(this).data();void 0!==i.width&&-1===i.width.toString().indexOf("%")&&(t(".th-inner",this).outerWidth(i.width),t(this).css("max-width",i.width))}),this.options.stateField=this.header.stateField},d.prototype.initToolbar=function(){if(h.apply(this,Array.prototype.slice.apply(arguments)),r(this)){var e=this,o=[];e.options.showSearch&&(o.push(i('<div class="columns-%s pull-%s" style="margin-top:10px;margin-bottom:10px;">',this.options.buttonsAlign,this.options.buttonsAlign)),o.push(i('<button class="btn btn-default%s" type="button" name="commonSearch" title="%s">',void 0===e.options.iconSize?"":" btn-"+e.options.iconSize,e.options.formatCommonSearch())),o.push(i('<i class="%s %s"></i>',e.options.iconsPrefix,e.options.icons.commonSearchIcon)),o.push("</button></div>")),e.$toolbar.find(".pull-right").length>0?t(o.join("")).insertBefore(e.$toolbar.find(".pull-right:first")):e.$toolbar.append(o.join("")),n(e.columns,e),e.$toolbar.find('button[name="commonSearch"]').off("click").on("click",function(){e.$commonsearch.toggleClass("hidden")}),e.$container.on("click","."+e.options.searchClass,function(){var i=t(this).data("value"),n=t(this).data("field"),o=e.$container.closest(".panel-intro").find("ul[data-field='"+n+"']");if(o.length>0)return void t('li a[data-value="'+i+'"][data-toggle="tab"]',o).trigger("click");var a=t("form [name='"+n+"']",e.$commonsearch);a.length>0&&(a.is("select")?t("option[value='"+i+"']",a).prop("selected",!0):a.length>1?t("form [name='"+n+"'][value='"+i+"']",e.$commonsearch).prop("checked",!0):a.val(i+""),a.trigger("change"),t("form",e.$commonsearch).trigger("submit"))});var a=e.options.queryParams;this.options.queryParams=function(t){return a(c(t,l(e,!0)))},this.trigger("post-common-search",e)}},d.prototype.onCommonSearch=function(){var t=l(this);this.trigger("common-search",this,t),this.options.pageNumber=1,this.refresh({})},d.prototype.load=function(t){p.apply(this,Array.prototype.slice.apply(arguments)),r(this)},d.prototype.initSearch=function(){if(f.apply(this,Array.prototype.slice.apply(arguments)),r(this)){var e=this,i=t.isEmptyObject(this.filterColumnsPartial)?null:this.filterColumnsPartial;this.data=i?t.grep(this.data,function(n,o){for(var a in i){var s=i[a].toLowerCase(),r=n[a];if(r=t.fn.bootstrapTable.utils.calculateObjectValue(e.header,e.header.formatters[t.inArray(a,e.header.fields)],[r,n,o],r),-1===t.inArray(a,e.header.fields)||"string"!=typeof r&&"number"!=typeof r||-1===(r+"").toLowerCase().indexOf(s))return!1}return!0}):this.data}}}(jQuery),define("bootstrap-table-commonsearch",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";t.extend(t.fn.bootstrapTable.defaults,{templateView:!1,templateFormatter:"itemtpl",templateParentClass:"row row-flex",templateTableClass:"table-template"});var e=t.fn.bootstrapTable.Constructor,i=e.prototype.initContainer,n=e.prototype.initBody,o=e.prototype.initRow;e.prototype.initContainer=function(){i.apply(this,Array.prototype.slice.apply(arguments));var t=this;t.options.templateView&&(t.options.cardView=!0)},e.prototype.initBody=function(){var e=this;t.extend(e.options,{showHeader:!e.options.templateView&&t.fn.bootstrapTable.defaults.showHeader,showFooter:!e.options.templateView&&t.fn.bootstrapTable.defaults.showFooter}),t(e.$el).toggleClass(e.options.templateTableClass,e.options.templateView),n.apply(this,Array.prototype.slice.apply(arguments)),e.options.templateView&&t("> *:not(.no-records-found)",e.$body).wrapAll(t("<div />").addClass(e.options.templateParentClass))},e.prototype.initRow=function(t,e,i,n){var a=this;if(!a.options.templateView)return o.apply(a,Array.prototype.slice.apply(arguments));var s="";if("function"==typeof a.options.templateFormatter)s=a.options.templateFormatter.call(a,t,e,i);else{s=require("template")(a.options.templateFormatter,{item:t,i:e,data:i})}return s}}(jQuery),define("bootstrap-table-template",["bootstrap-table","template"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";var e=t.fn.bootstrapTable.utils.sprintf;t.extend(t.fn.bootstrapTable.defaults,{showJumpto:!1,exportOptions:{}}),t.extend(t.fn.bootstrapTable.locales,{formatJumpto:function(){return"GO"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales);var i=t.fn.bootstrapTable.Constructor,n=i.prototype.initPagination;i.prototype.initPagination=function(){if(this.showToolbar=this.options.showExport,n.apply(this,Array.prototype.slice.apply(arguments)),this.options.showJumpto){var i=this,o=this.$pagination.find("ul.pagination"),a=o.find("li.jumpto");a.length||(a=t(['<li class="jumpto">','<input type="text" class="form-control">','<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" title="'+this.options.formatJumpto()+'"  type="button">'+this.options.formatJumpto(),"</button>","</li>"].join("")).appendTo(o),a.find("button").click(function(){i.selectPage(parseInt(a.find("input").val()))}))}}}(jQuery),define("bootstrap-table-jumpto",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";function e(t){var e=0,a=0,s=0,r=0;return"detail"in t&&(a=t.detail),"wheelDelta"in t&&(a=-t.wheelDelta/120),"wheelDeltaY"in t&&(a=-t.wheelDeltaY/120),"wheelDeltaX"in t&&(e=-t.wheelDeltaX/120),"axis"in t&&t.axis===t.HORIZONTAL_AXIS&&(e=a,a=0),s=e*i,r=a*i,"deltaY"in t&&(r=t.deltaY),"deltaX"in t&&(s=t.deltaX),(s||r)&&t.deltaMode&&(1===t.deltaMode?(s*=n,r*=n):(s*=o,r*=o)),s&&!e&&(e=s<1?-1:1),r&&!a&&(a=r<1?-1:1),{spinX:e,spinY:a,pixelX:s,pixelY:r}}var i=10,n=40,o=800,a=null,s=function(){if(null===a){var e=t("<p/>").addClass("fixed-table-scroll-inner"),i=t("<div/>").addClass("fixed-table-scroll-outer"),n=void 0,o=void 0;i.append(e),t("body").append(i),n=e[0].offsetWidth,i.css("overflow","scroll"),o=e[0].offsetWidth,n===o&&(o=i[0].clientWidth),i.remove(),a=n-o}return a},r=function(t){return t[0].scrollHeight>t[0].clientHeight?15:0};t.extend(t.fn.bootstrapTable.defaults,{fixedColumns:!1,fixedNumber:0,fixedRightNumber:0});var l=t.fn.bootstrapTable.Constructor,c=l.prototype.initBody,d=l.prototype.initContainer,u=l.prototype.trigger,h=l.prototype.hideLoading,p=l.prototype.updateSelected;l.prototype.fixedColumnsSupported=function(){var t=this;return t.options.fixedColumns&&!t.options.detailView&&!t.options.cardView},l.prototype.initFixedContainer=function(){this.options.fixedNumber&&(0==this.$tableContainer.find(".fixed-columns").length&&this.$tableContainer.append('<div class="fixed-columns"></div>'),this.$fixedColumns=this.$tableContainer.find(".fixed-columns")),this.options.fixedRightNumber&&(0==this.$tableContainer.find(".fixed-columns-right").length&&this.$tableContainer.append('<div class="fixed-columns-right"></div>'),this.$fixedColumnsRight=this.$tableContainer.find(".fixed-columns-right"))},l.prototype.initContainer=function(){d.apply(this,Array.prototype.slice.apply(arguments)),this.initFixedContainer()},l.prototype.initBody=function(){c.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&(this.options.showHeader&&this.options.height||(this.initFixedColumnsBody(),this.initFixedColumnsEvents()))},l.prototype.trigger=function(){var t=this;u.apply(this,Array.prototype.slice.apply(arguments)),"pre-body"===arguments[0]&&this.options.cardView&&this.$tableBody.css("height","auto"),"toggle"===arguments[0]&&(arguments[1]?(this.$tableBody.css("height","auto"),this.$fixedColumns&&this.$fixedColumns.hide(),this.$fixedColumnsRight&&this.$fixedColumnsRight.hide()):(this.$tableBody.css("height","100%"),this.$fixedColumns&&this.$fixedColumns.show(),this.$fixedColumnsRight&&this.$fixedColumnsRight.show(),this.$fixedHeaderRight&&this.$fixedHeaderRight.scrollLeft(this.$tableBody.find("table").width()),this.$fixedBodyRight&&this.$fixedBodyRight.scrollLeft(this.$tableBody.find("table").width()))),t.fixedColumnsSupported()&&("post-header"===arguments[0]?this.initFixedColumnsHeader():"scroll-body"===arguments[0]?(this.needFixedColumns&&this.options.fixedNumber&&this.$fixedBody&&this.$fixedBody.scrollTop(this.$tableBody.scrollTop()),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedBodyRight&&this.$fixedBodyRight.scrollTop(this.$tableBody.scrollTop())):"load-success"===arguments[0]&&this.hideLoading())},l.prototype.updateSelected=function(){var e=this;p.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&this.$tableBody.find("tr").each(function(i,n){var o=t(n),a=o.data("index"),s=o.attr("class"),r='[name="'+e.options.selectItemName+'"]',l=o.find(r);if(void 0!==a){var c=function(t,i){var n=i.find('tr[data-index="'+a+'"]');n.attr("class",s),l.length&&n.find(r).prop("checked",l.prop("checked")),e.$selectAll.length&&t.add(i).find('[name="btSelectAll"]').prop("checked",e.$selectAll.prop("checked"))};e.$fixedBody&&e.options.fixedNumber&&c(e.$fixedHeader,e.$fixedBody),e.$fixedBodyRight&&e.options.fixedRightNumber&&c(e.$fixedHeaderRight,e.$fixedBodyRight)}})},l.prototype.hideLoading=function(){h.apply(this,Array.prototype.slice.apply(arguments)),this.needFixedColumns&&this.options.fixedNumber&&this.$fixedColumns.find(".fixed-table-loading").hide(),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedColumnsRight.find(".fixed-table-loading").hide()},l.prototype.initFixedColumnsHeader=function(){var t=this;this.options.height?this.needFixedColumns=this.$tableHeader.outerWidth(!0)<this.$tableHeader.find("table").outerWidth(!0):this.needFixedColumns=this.$tableBody.outerWidth(!0)<this.$tableBody.find("table").outerWidth(!0);var e=function(e,i){return e.find(".fixed-table-header").remove(),e.append(t.$tableHeader.clone(!0)),e.find(".fixed-table-header").css("margin-right",""),e.css({width:t.getFixedColumnsWidth(i)}),e.find(".fixed-table-header")};this.needFixedColumns&&this.options.fixedNumber?(this.$fixedHeader=e(this.$fixedColumns),this.$fixedHeader.css("margin-right","")):this.$fixedColumns&&this.$fixedColumns.html("").css("width",""),this.needFixedColumns&&this.options.fixedRightNumber?(this.$fixedHeaderRight=e(this.$fixedColumnsRight,!0),this.$fixedHeaderRight.scrollLeft(this.$fixedHeaderRight.find("table").width())):this.$fixedColumnsRight&&this.$fixedColumnsRight.html("").css("width",""),this.initFixedColumnsBody(),this.initFixedColumnsEvents()},l.prototype.initFixedColumnsBody=function(){var e=this,i=function(i,n){i.find(".fixed-table-body").remove(),i.append(e.$tableBody.clone(!0));var o=i.find(".fixed-table-body"),a=e.$tableBody.get(0),r=function(){var r=a.scrollWidth>a.clientWidth?s():0,l=t(".fixed-table-pagination",e.$tableContainer).height();void 0!==e.options.height&&(l=0),i.css({height:"calc(100% - "+(l+r)+"px)"}),o.css({height:"calc(100% - "+n.height()+"px)",overflow:"hidden"})};return t(window).on("resize",r),r(),o};this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody=i(this.$fixedColumns,this.$fixedHeader)),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight=i(this.$fixedColumnsRight,this.$fixedHeaderRight),this.$fixedBodyRight.scrollLeft(this.$fixedBodyRight.find("table").width()),this.$fixedBodyRight.css("overflow-y","hidden"))},l.prototype.getFixedColumnsWidth=function(t){var e=this.getVisibleFields(),i=0,n=this.options.fixedNumber;t&&(e=e.reverse(),n=this.options.fixedRightNumber,this.$fixedColumnsRight.css("right",r(this.$tableBody)));for(var o=0;o<n;o++)i+=this.$header.find('th[data-field="'+e[o]+'"]').outerWidth();return i+1},l.prototype.initFixedColumnsEvents=function(){var i=this,n=function(e,n){var o='tr[data-index="'+t(e.currentTarget).data("index")+'"]',a=i.$tableBody.find(o);i.$fixedBody&&(a=a.add(i.$fixedBody.find(o))),i.$fixedBodyRight&&(a=a.add(i.$fixedBodyRight.find(o))),a.css("background-color",n?t(e.currentTarget).css("background-color"):"")};this.$tableBody.find("tr").hover(function(t){n(t,!0)},function(t){n(t,!1)});var o="undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().indexOf("firefox")>-1,a=o?"DOMMouseScroll":"mousewheel",s=function(t,n){var o=e(t),a=Math.ceil(o.pixelY),s=i.$tableBody.scrollTop()+a;(a<0&&s>0||a>0&&s<n.scrollHeight-n.clientHeight)&&t.preventDefault(),i.$tableBody.scrollTop(s),i.$fixedBody&&i.$fixedBody.scrollTop(s),i.$fixedBodyRight&&i.$fixedBodyRight.scrollTop(s)};this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody.find("tr").hover(function(t){n(t,!0)},function(t){n(t,!1)}),this.$fixedBody[0].addEventListener(a,function(t){s(t,i.$fixedBody[0])}),this.$fixedBody.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(e){e.stopImmediatePropagation();var n=t(e.target).data("index");t(i.$selectItem[n]).trigger("click")}),this.$fixedBody.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var n=t(this).closest("tr[data-index]").data("index");t(i.$selectItem[n]).closest("tr[data-index]").find(">td:eq("+t(this).index()+")").trigger("click")})),t("div.fixed-table-body").off("scroll"),this.$tableBody.off("scroll").on("scroll",function(t){i.$tableHeader.scrollLeft(0),i.$tableBody.scrollLeft()>0&&(i.$tableHeader.scrollLeft(i.$tableBody.scrollLeft()),i.options.showFooter&&!i.options.cardView&&i.$tableFooter.scrollLeft(i.$tableBody.scrollLeft()));var e=i.$tableBody.scrollTop();i.$fixedBody&&i.$fixedBody.scrollTop(e),i.$fixedBodyRight&&i.$fixedBodyRight.scrollTop(e)}),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight.find("tr").hover(function(t){n(t,!0)},function(t){n(t,!1)}),this.$fixedBodyRight[0].addEventListener(a,function(t){s(t,i.$fixedBodyRight[0])}),this.$fixedBodyRight.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(e){e.stopImmediatePropagation();var n=t(e.target).data("index");t(i.$selectItem[n]).trigger("click")}),this.$fixedBodyRight.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var n=t(this).closest("tr[data-index]").data("index");t(i.$selectItem[n]).closest("tr[data-index]").find(">td:eq("+t(this).index()+")").trigger("click")})),this.options.filterControl&&t(this.$fixedColumns).off("keyup change").on("keyup change",function(e){var n=t(e.target),o=n.val(),a=n.parents("th").data("field"),s=i.$header.find('th[data-field="'+a+'"]');if(n.is("input"))s.find("input").val(o);else if(n.is("select")){var r=s.find("select");r.find("option[selected]").removeAttr("selected"),r.find('option[value="'+o+'"]').attr("selected",!0)}i.triggerSearch()})}}(jQuery),define("bootstrap-table-fixed-columns",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),define("table",["jquery","bootstrap","moment","moment/locale/zh-cn","bootstrap-table","bootstrap-table-lang","bootstrap-table-export","bootstrap-table-commonsearch","bootstrap-table-template","bootstrap-table-jumpto","bootstrap-table-fixed-columns"],function(t,e,i){var n={list:{},defaults:{url:"",sidePagination:"server",method:"get",toolbar:".toolbar",search:!0,cache:!1,commonSearch:!0,searchFormVisible:!1,titleForm:"",idTable:"commonTable",showExport:!0,exportDataType:"auto",exportTypes:["json","xml","csv","txt","doc","excel"],exportOptions:{fileName:"export_"+i().format("YYYY-MM-DD"),preventInjection:!1,mso:{onMsoNumberFormat:function(e,i,n){return isNaN(t(e).text())?"":"\\@"}},ignoreColumn:[0,"operate"]},
pageSize:Config.pagesize||localStorage.getItem("pagesize")||10,pageList:[10,15,20,25,50,"All"],pagination:!0,clickToSelect:!0,dblClickToEdit:!0,singleSelect:!1,showRefresh:!1,showJumpto:!0,locale:"zh-cn"===Config.language?"zh-CN":"en-US",showToggle:!0,showColumns:!0,pk:"id",sortName:"id",sortOrder:"desc",paginationFirstText:__("First"),paginationPreText:__("Previous"),paginationNextText:__("Next"),paginationLastText:__("Last"),cardView:!1,iosCardView:!0,checkOnInit:!0,escape:!0,fixDropdownPosition:!0,dragCheckboxMultiselect:!0,selectedIds:[],selectedData:[],extend:{index_url:"",add_url:"",edit_url:"",del_url:"",import_url:"",multi_url:"",dragsort_url:"ajax/weigh"}},columnDefaults:{align:"center",valign:"middle"},config:{checkboxtd:"tbody>tr>td.bs-checkbox",toolbar:".toolbar",refreshbtn:".btn-refresh",addbtn:".btn-add",editbtn:".btn-edit",delbtn:".btn-del",importbtn:".btn-import",multibtn:".btn-multi",disabledbtn:".btn-disabled",editonebtn:".btn-editone",restoreonebtn:".btn-restoreone",destroyonebtn:".btn-destroyone",restoreallbtn:".btn-restoreall",destroyallbtn:".btn-destroyall",dragsortfield:"weigh"},button:{edit:{name:"edit",icon:"fa fa-pencil",title:__("Edit"),extend:'data-toggle="tooltip" data-container="body"',classname:"btn btn-xs btn-success btn-editone"},del:{name:"del",icon:"fa fa-trash",title:__("Del"),extend:'data-toggle="tooltip" data-container="body"',classname:"btn btn-xs btn-danger btn-delone"},dragsort:{name:"dragsort",icon:"fa fa-arrows",title:__("Drag to sort"),extend:'data-toggle="tooltip"',classname:"btn btn-xs btn-primary btn-dragsort"}},api:{init:function(e,i,o){e=e||{},i=i||{},o=o||{},t.fn.bootstrapTable.Constructor.prototype.getSelectItem=function(){return this.$selectItem};var a=t.fn.bootstrapTable.Constructor.prototype.onPageListChange;t.fn.bootstrapTable.Constructor.prototype.onPageListChange=function(){return a.apply(this,Array.prototype.slice.apply(arguments)),localStorage.setItem("pagesize",this.options.pageSize),!1},t.extend(!0,t.fn.bootstrapTable.defaults,n.defaults,e),t.extend(t.fn.bootstrapTable.columnDefaults,n.columnDefaults,i),t.extend(t.fn.bootstrapTable.locales[n.defaults.locale],{formatCommonSearch:function(){return __("Common search")},formatCommonSubmitButton:function(){return __("Search")},formatCommonResetButton:function(){return __("Reset")},formatCommonCloseButton:function(){return __("Close")},formatCommonChoose:function(){return __("Choose")},formatJumpto:function(){return __("Go")}},o),t.fn.bootstrapTable.defaults.iosCardView&&navigator.userAgent.match(/(iPod|iPhone|iPad)/)&&(n.defaults.cardView=!0,t.fn.bootstrapTable.defaults.cardView=!0),void 0!==e.exportTypes&&(t.fn.bootstrapTable.defaults.exportTypes=e.exportTypes)},bindevent:function(e){var i=e.closest(".bootstrap-table"),o=e.bootstrapTable("getOptions"),a=t(o.toolbar,i),s=t(".btn-selected-tips",i);0===s.length&&(s=t('<a href="javascript:" class="btn btn-warning-light btn-selected-tips hide" data-animation="false" data-toggle="tooltip" data-title="'+__("Click to uncheck all")+'"><i class="fa fa-info-circle"></i> '+__("Multiple selection mode: %s checked","<b>0</b>")+"</a>").appendTo(a)),s.off("click").on("click",function(t){e.trigger("uncheckbox"),e.bootstrapTable("refresh")}),e.on("uncheckbox",function(t,e,i){o.selectedIds=[],o.selectedData=[],s.tooltip("hide"),s.addClass("hide")}),e.on("load-error.bs.table",function(t,e,i){0!==i.status&&Toastr.error(__("Unknown data format"))}),e.on("load-success.bs.table",function(t,e){void 0===e.rows&&void 0!==e.code&&Toastr.error(e.msg)}),e.on("refresh.bs.table",function(e,i,o){t(n.config.refreshbtn,a).find(".fa").addClass("fa-spin"),t(".layui-layer-autocontent").remove()}),e.on("search.bs.table common-search.bs.table",function(t,i,n){e.trigger("uncheckbox")}),o.dblClickToEdit&&e.on("dbl-click-row.bs.table",function(e,i,o,a){t(n.config.editonebtn,o).trigger("click")}),e.on("pre-body.bs.table",function(e,i){o.maintainSelected&&t.each(i,function(e,i){i[o.stateField]=t.inArray(i[o.pk],o.selectedIds)>-1})}),e.on("post-body.bs.table",function(i,s){if(t(n.config.refreshbtn,a).find(".fa").removeClass("fa-spin"),t(n.config.checkboxtd+":first",e).find("input[type='checkbox'][data-index]").length>0){var r,l,c,d=!1,u=!1,h=function(i){if(d){var o=Math.min(i.pageX,r),a=Math.min(i.pageY,l),s=Math.abs(r-i.pageX),u=Math.abs(l-i.pageY);c.css({left:o+"px",top:a+"px",width:s+"px",height:u+"px"});var h={x:o,y:a,width:s,height:u};t(n.config.checkboxtd,e).each(function(){var e=t("input:checkbox",this),i=this.getBoundingClientRect();i.x+=document.documentElement.scrollLeft,i.y+=document.documentElement.scrollTop;var n=i.x,o=i.y,a=i.x+i.width,s=i.y+i.height,r=h.x,l=h.y,c=h.x+h.width,d=h.y+h.height;n<=c&&a>=r&&o<=d&&s>=l?t(this).hasClass("overlaped")||(t(this).addClass("overlaped"),e.trigger("click")):t(this).hasClass("overlaped")&&(t(this).removeClass("overlaped"),e.trigger("click"))})}},p=function(){return!1},f=function(){d&&(t(document).off("mousemove",h),t(document).off("selectstart",p),c.remove()),d=!1,u=!1,t(document.body).css({MozUserSelect:"",webkitUserSelect:""}).attr("unselectable","off")};t(n.config.checkboxtd,e).on("mousedown",function(e){if(2===e.button||t(e.target).is("input"))return!1;r=e.pageX,l=e.pageY,u=!0}).on("mousemove",function(i){u&&!d&&(d=!0,c=t("<div />"),c.css({position:"absolute",width:0,height:0,border:"1px dashed blue",background:"#0029ff",left:i.pageX+"px",top:i.pageY+"px",opacity:.1}),c.appendTo(document.body),t(document.body).css({MozUserSelect:"none",webkitUserSelect:"none"}).attr("unselectable","on"),t(document).on("mousemove",h).on("mouseup",f).on("selectstart",p),o.dragCheckboxMultiselect&&t(n.config.checkboxtd,e).removeClass("overlaped"))})}});var r=o.exportDataType;if(e.on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table post-body.bs.table",function(i){var l=[];t.each(e.bootstrapTable("getData"),function(t,e){l.push(void 0!==e[o.pk]?e[o.pk]:"")});var c=n.api.selectedids(e,!0),d=n.api.selecteddata(e,!0);o.maintainSelected?(o.selectedIds=o.selectedIds.filter(function(e,i,n){return-1===t.inArray(e,l)}).concat(c),o.selectedData=o.selectedData.filter(function(e,i,n){return-1===t.inArray(e[o.pk],l)}).concat(d),o.selectedIds.length>c.length?(t("b",s).text(o.selectedIds.length),s.removeClass("hide")):s.addClass("hide")):(o.selectedIds=c,o.selectedData=d),"auto"===r&&(o.exportDataType=c.length>0?"selected":"all",0===t(".export .exporttips").length&&t(".export .dropdown-menu").prepend("<li class='exporttips alert alert-warning-light mb-0 no-border p-2'></li>"),t(".export .exporttips").html("导出记录："+(c.length>0?"选中":"全部"))),t(n.config.disabledbtn,a).toggleClass("disabled",!o.selectedIds.length)}),e.on("common-search.bs.table",function(i,n,o){var a=t(".panel-heading [data-field]",e.closest(".panel-intro")),s=a.data("field"),r=t("li.active > a",a).data("value");o.filter&&void 0!==o.filter[s]&&o.filter[s]!=r&&(t("li",a).removeClass("active"),t("li > a[data-value='"+o.filter[s]+"']",a).parent().addClass("active"))}),t('.panel-heading [data-field] a[data-toggle="tab"]',e.closest(".panel-intro")).on("shown.bs.tab",function(i){var n=t(this).closest("[data-field]").data("field"),o=t(this).data("value"),a=t("[name='"+n+"']",e.closest(".bootstrap-table").find(".commonsearch-table"));return"SELECT"===a.prop("tagName")?t("option[value='"+o+"']",a).prop("selected",!0):a.val(o),e.trigger("uncheckbox"),e.bootstrapTable("getOptions").totalRows=0,e.bootstrapTable("refresh",{pageNumber:1}),!1}),t("form",e.closest(".bootstrap-table").find(".commonsearch-table")).on("reset",function(){setTimeout(function(){},0),t(".panel-heading [data-field] li",e.closest(".panel-intro")).removeClass("active"),t(".panel-heading [data-field] li:first",e.closest(".panel-intro")).addClass("active")}),a.on("click",n.config.refreshbtn,function(){e.bootstrapTable("refresh")}),a.on("click",n.config.addbtn,function(){var i=n.api.selectedids(e),a=o.extend.add_url;-1!==a.indexOf("{ids}")&&(a=n.api.replaceurl(a,{ids:i.length>0?i.join(","):0},e)),Fast.api.open(a,t(this).data("original-title")||t(this).attr("title")||__("Add"),t(this).data()||{})}),t(n.config.importbtn,a).length>0&&require(["upload"],function(i){i.api.upload(t(n.config.importbtn,a),function(t,i){Fast.api.ajax({url:o.extend.import_url,data:{file:t.url}},function(t,i){e.trigger("uncheckbox"),e.bootstrapTable("refresh")})})}),a.on("click",n.config.editbtn,function(){var i=this;if(!(n.api.selectedids(e).length>10)){var a=t(i).data("title")||t(i).attr("title")||__("Edit"),s=t(i).data()||{};delete s.title,t.each(n.api.selecteddata(e),function(i,r){var l=o.extend.edit_url;r=t.extend({},r||{},{ids:r[o.pk]}),l=n.api.replaceurl(l,r,e),Fast.api.open(l,"function"==typeof a?a.call(e,r):a,s)})}}),t(document).on("click",n.config.destroyallbtn,function(){var i=this;return Layer.confirm(__("Are you sure you want to truncate?"),function(){var n=t(i).data("url")?t(i).data("url"):t(i).attr("href");Fast.api.ajax(n,function(){Layer.closeAll(),e.trigger("uncheckbox"),e.bootstrapTable("refresh")},function(){Layer.closeAll()})}),!1}),t(document).on("click",n.config.restoreallbtn,function(){var i=this,n=t(i).data("url")?t(i).data("url"):t(i).attr("href");return Fast.api.ajax(n,function(){Layer.closeAll(),e.trigger("uncheckbox"),e.bootstrapTable("refresh")},function(){Layer.closeAll()}),!1}),t(document).on("click",n.config.restoreonebtn+","+n.config.destroyonebtn,function(){var i=this,a=t(i).data("url")?t(i).data("url"):t(i).attr("href"),s=n.api.getrowbyindex(e,t(i).data("row-index"));return Fast.api.ajax({url:a,data:{ids:s[o.pk]}},function(){e.trigger("uncheckbox"),e.bootstrapTable("refresh")}),!1}),a.on("click",n.config.multibtn,function(){var i=n.api.selectedids(e);n.api.multi(t(this).data("action"),i,e,this)}),a.on("click",n.config.delbtn,function(){var t=this,i=n.api.selectedids(e);Layer.confirm(__("Are you sure you want to delete the %s selected item?",i.length),{icon:3,title:__("Warning"),offset:0,shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(o){n.api.multi("del",i,e,t),Layer.close(o)})}),require(["dragsort"],function(){t("tbody",e).dragsort({itemSelector:"tr:visible",dragSelector:"a.btn-dragsort",dragEnd:function(i,o){var a=t("a.btn-dragsort",this),s=e.bootstrapTable("getData"),r=s[parseInt(t(this).data("index"))],l=e.bootstrapTable("getOptions"),c=t.map(t("tbody tr:visible",e),function(e){return s[parseInt(t(e).data("index"))][l.pk]}),d=r[l.pk],u=void 0!==r.pid?r.pid:"",h={url:e.bootstrapTable("getOptions").extend.dragsort_url,data:{ids:c.join(","),changeid:d,pid:u,field:n.config.dragsortfield,orderway:l.sortOrder,table:l.extend.table,pk:l.pk}};Fast.api.ajax(h,function(i,n){var o=t(a).data("success")||t.noop;if("function"==typeof o&&!1===o.call(a,i,n))return!1;e.bootstrapTable("refresh")},function(i,n){var o=t(a).data("error")||t.noop;if("function"==typeof o&&!1===o.call(a,i,n))return!1;e.bootstrapTable("refresh")})},placeHolderTemplate:""})}),e.on("click","input[data-id][name='checkbox']",function(i){var n=t(this).data("id");e.bootstrapTable(t(this).prop("checked")?"checkBy":"uncheckBy",{field:o.pk,values:[n]})}),e.on("click","[data-id].btn-change",function(i){i.preventDefault();var o=t.proxy(function(){n.api.multi(t(this).data("action")?t(this).data("action"):"",[t(this).data("id")],e,this)},this);void 0!==t(this).data("confirm")?Layer.confirm(t(this).data("confirm"),function(t){o(),Layer.close(t)}):o()}),e.on("click","[data-id].btn-edit",function(i){i.preventDefault();var a=t(this).data("id"),s=n.api.getrowbyid(e,a);s.ids=a;var r=n.api.replaceurl(o.extend.edit_url,s,e);Fast.api.open(r,t(this).data("original-title")||t(this).attr("title")||__("Edit"),t(this).data()||{})}),e.on("click","[data-id].btn-del",function(i){i.preventDefault();var o=t(this).data("id"),a=this;Layer.confirm(__("Are you sure you want to delete this item?"),{icon:3,title:__("Warning"),shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(t){n.api.multi("del",o,e,a),Layer.close(t)})}),e.on("mouseenter mouseleave",".autocontent",function(e){var i=t(".autocontent-item",this).get(0);i&&("mouseenter"===e.type?i.scrollWidth>i.offsetWidth&&0===t(".autocontent-caret",this).length&&t(this).append("<div class='autocontent-caret'><i class='fa fa-chevron-down'></div>"):t(".autocontent-caret",this).remove())}),e.on("click mouseenter",".autocontent-caret",function(e){var i=t(this).prev().hasClass("autocontent-hover");if(i||"mouseenter"!==e.type){var n=t(this).prev().text(),o=t(this).parent().get(0).getBoundingClientRect(),a=Layer.open({id:"autocontent",skin:"layui-layer-fast layui-layer-autocontent",title:!1,content:n,btn:!1,anim:!1,shade:0,isOutAnim:!1,area:"auto",maxWidth:450,maxHeight:350,offset:[o.y,o.x]});i&&t(document).one("mouseleave","#layui-layer"+a,function(){Layer.close(a)});var s=function(e){0===t(e.target).closest(".layui-layer").length&&(Layer.close(a),t(document).off("mousedown",s))};t(document).off("mousedown",s).on("mousedown",s)}}),o.fixDropdownPosition){var l=e.closest(".fixed-table-body");e.on("show.bs.dropdown fa.event.refreshdropdown",".btn-group",function(e){var i,n,o,a=t(".dropdown-menu",this),s=t(this),r=a.hasClass("pull-right")||a.hasClass("dropdown-menu-right");o="fixed",n=s.offset().top-t(window).scrollTop()+s.outerHeight(),n+a.outerHeight()>t(window).height()&&(n=s.offset().top-a.outerHeight()-5),i=r?s.offset().left+s.outerWidth()-a.outerWidth():s.offset().left,(i||n)&&a.css({position:o,left:i,top:n,right:"inherit"})});var c=function(){t(".btn-group.open",e).length>0&&"fixed"==t(".btn-group.open .dropdown-menu",e).css("position")&&t(".btn-group.open",e).trigger("fa.event.refreshdropdown")};t(window).on("scroll",function(){c()}),l.on("scroll",function(){c()})}var d=e.attr("id");return n.list[d]=e,e},multi:function(e,i,n,o){var a=n.bootstrapTable("getOptions"),s=o?t(o).data():{};i=t.isArray(i)?i.join(","):i,a={url:void 0!==s.url?s.url:"del"==e?a.extend.del_url:a.extend.multi_url,data:{action:e,ids:i,params:void 0!==s.params?"object"==typeof s.params?t.param(s.params):s.params:""}},Fast.api.ajax(a,function(e,i){n.trigger("uncheckbox");var a=t(o).data("success")||t.noop;if("function"==typeof a&&!1===a.call(o,e,i))return!1;n.bootstrapTable("refresh")},function(e,i){var n=t(o).data("error")||t.noop;if("function"==typeof n&&!1===n.call(o,e,i))return!1})},events:{operate:{"click .btn-editone":function(e,i,o,a){e.stopPropagation(),e.preventDefault();var s=t(this).closest("table"),r=s.bootstrapTable("getOptions"),l=o[r.pk];o=t.extend({},o||{},{ids:l});var c=r.extend.edit_url;Fast.api.open(n.api.replaceurl(c,o,s),t(this).data("original-title")||t(this).attr("title")||__("Edit"),t(this).data()||{})},"click .btn-delone":function(i,o,a,s){i.stopPropagation(),i.preventDefault();var r=this,l=t(r).offset().top-t(window).scrollTop(),c=t(r).offset().left-t(window).scrollLeft()-260;l+154>t(window).height()&&(l-=154),t(window).width()<480&&(l=c=e),Layer.confirm(__("Are you sure you want to delete this item?"),{icon:3,title:__("Warning"),offset:[l,c],shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(e){var i=t(r).closest("table"),o=i.bootstrapTable("getOptions");n.api.multi("del",a[o.pk],i,r),Layer.close(e)})}},image:{"click .img-center":function(e,i,n,o){var a=[];i=null===i?"":i.toString();var s,r=""!=i?i.split(","):[];t.each(r,function(t,e){s=Fast.api.cdnurl(e),a.push({src:s,thumb:s.match(/^(\/|data:image\\)/)?s:s+Config.upload.thumbstyle})}),Layer.photos({photos:{start:t(this).parent().index(),data:a},anim:5})}}},formatter:{icon:function(t,e,i){return t=null===t?"":t.toString(),'<i class="'+(t=t.indexOf(" ")>-1?t:"fa fa-"+t)+'"></i> '+t},image:function(t,e,i){return n.api.formatter.images.call(this,t,e,i)},images:function(e,i,n){e=null==e||0===e.length?"":e.toString();var o,a=void 0!==this.classname?this.classname:"img-sm img-center",s=""!==e?-1===e.indexOf("data:image/")?e.split(","):[e]:[],r=[];return t.each(s,function(t,e){e=e||"/assets/img/blank.gif",o=Fast.api.cdnurl(e,!0),o=!Config.upload.thumbstyle||o.match(/^(\/|data:image\/)/)||o.indexOf(Config.upload.thumbstyle.substring(0,1))>-1?o:o+Config.upload.thumbstyle,r.push('<a href="javascript:"><img class="'+a+'" src="'+o+'" /></a>')}),r.join(" ")},file:function(t,e,i){return n.api.formatter.files.call(this,t,e,i)},files:function(e,i,n){e=null==e||0===e.length?"":e.toString();var o,a,s=void 0!==this.classname?this.classname:"img-sm img-center",r=""!==e?-1===e.indexOf("data:image/")?e.split(","):[e]:[],l=[];return t.each(r,function(t,e){e=Fast.api.cdnurl(e,!0),o=/[\.]?([a-zA-Z0-9]+)$/.exec(e),o=o?o[1]:"file",a=Fast.api.fixurl("ajax/icon?suffix="+o),l.push('<a href="'+e+'" target="_blank"><img src="'+a+'" class="'+s+'" width="30" height="30"></a>')}),l.join(" ")},content:function(t,i,n){var o=this.width!=e?this.width.toString().match(/^\d+$/)?this.width+"px":this.width:"250px";return"<div class='autocontent-item "+(this.hover!=e&&this.hover?"autocontent-hover":"")+"' style='white-space: nowrap; text-overflow:ellipsis; overflow: hidden; max-width:"+o+";'>"+t+"</div>"},status:function(e,i,o){var a={normal:"success",hidden:"gray",deleted:"danger",locked:"info"};return void 0!==this.custom&&(a=t.extend(a,this.custom)),this.custom=a,this.icon="fa fa-circle",n.api.formatter.normal.call(this,e,i,o)},normal:function(e,i,n){var o=["primary","success","danger","warning","info","gray","red","yellow","aqua","blue","navy","teal","olive","lime","fuchsia","purple","maroon"],a={};void 0!==this.custom&&(a=t.extend(a,this.custom)),e=null==e||0===e.length?"":e.toString();var s="object"==typeof this.searchList?Object.keys(this.searchList):[],n=s.indexOf(e),r=e&&void 0!==a[e]?a[e]:null,l=n>-1?this.searchList[e]:null,c=void 0!==this.icon?this.icon:null;r||(r=n>-1&&void 0!==o[n]?o[n]:"primary"),l||(l=__(e.charAt(0).toUpperCase()+e.slice(1)));var d='<span class="text-'+r+'">'+(c?'<i class="'+c+'"></i> ':"")+l+"</span>";return 0!=this.operate&&(d='<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",l)+'" data-field="'+this.field+'" data-value="'+e+'">'+d+"</a>"),d},toggle:function(t,e,i){var n=this.table,o=n?n.bootstrapTable("getOptions"):{},a=o.pk||"id",s=void 0!==this.color?this.color:"success",r=void 0!==this.yes?this.yes:1,l=void 0!==this.no?this.no:0,c=void 0!==this.url?this.url:"",d="",u=!1;return void 0!==this.confirm&&(d="function"==typeof this.confirm?this.confirm.call(this,t,e,i):this.confirm),void 0!==this.disable&&(u="function"==typeof this.disable?this.disable.call(this,t,e,i):this.disable),"<a href='javascript:;' data-toggle='tooltip' title='"+__("Click to toggle")+"' class='btn-change "+(u?"btn disabled no-padding":"")+"' data-index='"+i+"' data-id='"+e[a]+"' "+(c?"data-url='"+c+"'":"")+(d?"data-confirm='"+d+"'":"")+" data-params='"+this.field+"="+(t==r?l:r)+"'><i class='fa fa-toggle-on text-success text-"+s+" "+(t==r?"":"fa-flip-horizontal text-gray")+" fa-2x'></i></a>"},url:function(t,e,i){return'<div class="input-group input-group-sm" style="width:250px;margin:0 auto;"><input type="text" class="form-control input-sm" value="'+(t=null==t||0===t.length?"":t.toString())+'"><span class="input-group-btn input-group-sm"><a href="'+t+'" target="_blank" class="btn btn-default btn-sm"><i class="fa fa-link"></i></a></span></div>'},search:function(t,i,n){var o=this.field;if(void 0!==this.customField){var a=this.customField.split(".").reduce(function(t,i){return null===t||t===e?"":t[i]},i);t=Fast.api.escape(a),o=this.customField}return'<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",t)+'" data-field="'+o+'" data-value="'+t+'">'+t+"</a>"},addtabs:function(t,e,i){var o=n.api.replaceurl(this.url||"",e,this.table),a=this.atitle?this.atitle:__("Search %s",t);return'<a href="'+Fast.api.fixurl(o)+'" class="addtabsit" data-value="'+t+'" title="'+a+'">'+t+"</a>"},dialog:function(t,e,i){var o=n.api.replaceurl(this.url||"",e,this.table),a=this.atitle?this.atitle:__("View %s",t);return'<a href="'+Fast.api.fixurl(o)+'" class="dialogit" data-value="'+t+'" title="'+a+'">'+t+"</a>"},flag:function(i,n,o){var a=this;i=null==i||0===i.length?"":i.toString();var s={index:"success",hot:"warning",recommend:"danger",new:"info"};void 0!==this.custom&&(s=t.extend(s,this.custom));var r=this.field;if(void 0!==this.customField){var l=this.customField.split(".").reduce(function(t,i){return null===t||t===e?"":t[i]},n);i=Fast.api.escape(l),r=this.customField}if("object"==typeof a.searchList&&"function"==typeof a.searchList.then&&t.when(a.searchList).done(function(e){e.data&&e.data.searchlist&&t.isArray(e.data.searchlist)?a.searchList=e.data.searchlist:e.constructor!==Array&&e.constructor!==Object||(a.searchList=e)}),"object"==typeof a.searchList&&void 0===a.custom){var c=0,d=Object.values(s);t.each(a.searchList,function(t,e){void 0===s[t]&&(s[t]=d[c],c=void 0===d[c+1]?0:c+1)})}var u,h,p,f=[],m=t.isArray(i)?i:""!=i?i.split(","):[];return t.each(m,function(t,e){if(""===(e=null==e||0===e.length?"":e.toString()))return!0;u=e&&void 0!==s[e]?s[e]:"primary",h=void 0!==a.searchList&&void 0!==a.searchList[e]?a.searchList[e]:__(e.charAt(0).toUpperCase()+e.slice(1)),p='<span class="label label-'+u+'">'+h+"</span>",a.operate?f.push('<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",h)+'" data-field="'+r+'" data-value="'+e+'">'+p+"</a>"):f.push(p)}),f.join(" ")},label:function(t,e,i){return n.api.formatter.flag.call(this,t,e,i)},datetime:function(t,e,n){var o=void 0===this.datetimeFormat?"YYYY-MM-DD HH:mm:ss":this.datetimeFormat;return isNaN(t)?t?i(t).format(o):__("None"):t?i(1e3*parseInt(t)).format(o):__("None")},operate:function(e,i,o){var a=this.table,s=a?a.bootstrapTable("getOptions"):{},r=t.extend([],this.buttons||[]),l=[];return r.forEach(function(t){l.push(t.name)}),""!==s.extend.dragsort_url&&-1===l.indexOf("dragsort")&&r.push(n.button.dragsort),""!==s.extend.edit_url&&-1===l.indexOf("edit")&&(n.button.edit.url=s.extend.edit_url,r.push(n.button.edit)),""!==s.extend.del_url&&-1===l.indexOf("del")&&r.push(n.button.del),n.api.buttonlink(this,r,e,i,o,"operate")},buttons:function(e,i,o){var a=t.extend([],this.buttons||[]);return n.api.buttonlink(this,a,e,i,o,"buttons")}},buttonlink:function(e,i,o,a,s,r){var l=e.table;e.clickToSelect=!1,r=void 0===r?"buttons":r;var c,d,u,h,p,f,m,g,v,y,b,x,w,k=l?l.bootstrapTable("getOptions"):{},_=[],C=e.fieldIndex,S={};if(t.each(i,function(t,e){if("operate"===r){if("dragsort"===e.name&&void 0===a[n.config.dragsortfield])return!0;if(["add","edit","del","multi","dragsort"].indexOf(e.name)>-1&&!k.extend[e.name+"_url"])return!0}var i=l.data(r+"-"+e.name);if(void 0===i||i){if(c="function"==typeof e.hidden?e.hidden.call(l,a,e):void 0!==e.hidden&&e.hidden)return!0;if(!(d="function"==typeof e.visible?e.visible.call(l,a,e):void 0===e.visible||e.visible))return!0;x=e.dropdown?e.dropdown:"",h=e.url?e.url:"",h="function"==typeof h?h.call(l,a,e):h?Fast.api.fixurl(n.api.replaceurl(h,a,l)):"javascript:;",p=e.classname?e.classname:x?"btn-"+name+"one":"btn-primary btn-"+name+"one",f=e.icon?e.icon:"",m="function"==typeof e.text?e.text.call(l,a,e):e.text?e.text:"",g="function"==typeof e.title?e.title.call(l,a,e):e.title?e.title:m,v=e.refresh?'data-refresh="'+e.refresh+'"':"",y="function"==typeof e.confirm?e.confirm.call(l,a,e):void 0!==e.confirm&&e.confirm,y=y?'data-confirm="'+y+'"':"",b="function"==typeof e.extend?e.extend.call(l,a,e):void 0!==e.extend?e.extend:"",u="function"==typeof e.disable?e.disable.call(l,a,e):void 0!==e.disable&&e.disable,u&&(p+=" disabled"),w='<a href="'+h+'" class="'+p+'" '+(y?y+" ":"")+(v?v+" ":"")+b+' title="'+g+'" data-table-id="'+(l?l.attr("id"):"")+'" data-field-index="'+C+'" data-row-index="'+s+'" data-button-index="'+t+'"><i class="'+f+'"></i>'+(m?" "+m:"")+"</a>",x?(void 0===S[x]&&(S[x]=[]),S[x].push(w)):_.push(w)}}),!t.isEmptyObject(S)){var T=[];t.each(S,function(t,e){T.push('<div class="btn-group"><button type="button" class="btn btn-primary dropdown-toggle btn-xs" data-toggle="dropdown">'+t+'</button><button type="button" class="btn btn-primary dropdown-toggle btn-xs" data-toggle="dropdown"><span class="caret"></span></button><ul class="dropdown-menu dropdown-menu-right"><li>'+e.join("</li><li>")+"</li></ul></div>")}),_.unshift(T.join(" "))}return _.join(" ")},replaceurl:function(t,i,n){var o=n?n.bootstrapTable("getOptions"):null,a=o?i[o.pk]:0;return i.ids=a||(void 0!==i.ids?i.ids:0),t=null==t||0===t.length?"":t.toString(),t=t.match(/(?=([?&]ids=)|(\/ids\/)|(\{ids}))/i)?t:t+(t.match(/(\?|&)+/)?"&ids=":"/ids/")+"{ids}",t=t.replace(/\{(.*?)\}/gi,function(t){t=t.substring(1,t.length-1);var n=t.split(".").reduce(function(t,i){return null===t||t===e?"":t[i]},i);return n=Fast.api.escape(n)})},selectedids:function(e,i){var n=e.bootstrapTable("getOptions");return!i&&n.maintainSelected?n.selectedIds:t.map(e.bootstrapTable("getSelections"),function(t){return t[n.pk]})},selecteddata:function(t,e){var i=t.bootstrapTable("getOptions");return!e&&i.maintainSelected?i.selectedData:t.bootstrapTable("getSelections")},toggleattr:function(e){t("input[type='checkbox']",e).trigger("click")},getrowdata:function(t,e){e=parseInt(e);var i=t.bootstrapTable("getData");return void 0!==i[e]?i[e]:null},getrowbyindex:function(t,e){return n.api.getrowdata(t,e)},getrowbyid:function(e,i){var o={},a=e.bootstrapTable("getOptions");return t.each(n.api.selecteddata(e),function(t,e){if(e[a.pk]==i)return o=e,!1}),o}}};return n}),function(t){t.fn.dragsort=function(e){if("destroy"==e)return void t(this.selector).trigger("dragsort-uninit");var i=t.extend({},t.fn.dragsort.defaults,e),n=[],o=null,a=null;return this.each(function(e,s){t(s).is("table")&&1==t(s).children().length&&t(s).children().is("tbody")&&(s=t(s).children().get(0));var r={draggedItem:null,placeHolderItem:null,pos:null,offset:null,offsetLimit:null,scroll:null,container:s,init:function(){i.tagName=""==i.tagName?0==t(this.container).children().length?"li":t(this.container).children().get(0).tagName.toLowerCase():i.tagName,""==i.itemSelector&&(i.itemSelector=i.tagName),""==i.dragSelector&&(i.dragSelector=i.tagName),""==i.placeHolderTemplate&&(i.placeHolderTemplate="<"+i.tagName+">&nbsp;</"+i.tagName+">"),t(this.container).attr("data-listidx",e).mousedown(this.grabItem).bind("dragsort-uninit",this.uninit),this.styleDragHandlers(!0)},uninit:function(){var e=n[t(this).attr("data-listidx")];t(e.container).unbind("mousedown",e.grabItem).unbind("dragsort-uninit"),e.styleDragHandlers(!1)},getItems:function(){return t(this.container).children(i.itemSelector)},styleDragHandlers:function(e){this.getItems().map(function(){return t(this).is(i.dragSelector)?this:t(this).find(i.dragSelector).get()}).css("cursor",e?"pointer":"")},grabItem:function(e){var o=n[t(this).attr("data-listidx")],a=t(e.target).closest("[data-listidx] > "+i.tagName).get(0),s=o.getItems().filter(function(){return this==a}).length>0;if(!(1!=e.which||t(e.target).is(i.dragSelectorExclude)||t(e.target).closest(i.dragSelectorExclude).length>0)&&s&&(t(e.target).is(i.dragSelector)||t(e.target).closest(i.dragSelector).length)){e.preventDefault();for(var r=e.target;!t(r).is(i.dragSelector);){if(r==this)return;r=r.parentNode}t(r).attr("data-cursor",t(r).css("cursor")),t(r).css("cursor","move");var l=this,c=function(){o.dragStart.call(l,e),t(o.container).unbind("mousemove",c)};t(o.container).mousemove(c).mouseup(function(){t(o.container).unbind("mousemove",c),t(r).css("cursor",t(r).attr("data-cursor"))})}},dragStart:function(e){null!=o&&null!=o.draggedItem&&o.dropItem(),o=n[t(this).attr("data-listidx")],o.draggedItem=t(e.target).closest("[data-listidx] > "+i.tagName),o.draggedItem.attr("data-origpos",t(this).attr("data-listidx")+"-"+t(o.container).children().index(o.draggedItem));var a=parseInt(o.draggedItem.css("marginTop")),s=parseInt(o.draggedItem.css("marginLeft"));if(o.offset=o.draggedItem.offset(),o.offset.top=e.pageY-o.offset.top+(isNaN(a)?0:a)-1,o.offset.left=e.pageX-o.offset.left+(isNaN(s)?0:s)-1,!i.dragBetween){var r=0==t(o.container).outerHeight()?Math.max(1,Math.round(.5+o.getItems().length*o.draggedItem.outerWidth()/t(o.container).outerWidth()))*o.draggedItem.outerHeight():t(o.container).outerHeight();o.offsetLimit=t(o.container).offset(),o.offsetLimit.right=o.offsetLimit.left+t(o.container).outerWidth()-o.draggedItem.outerWidth(),o.offsetLimit.bottom=o.offsetLimit.top+r-o.draggedItem.outerHeight()}var l=o.draggedItem.height(),c=o.draggedItem.width();if("tr"==i.tagName?(o.draggedItem.children().each(function(){t(this).width(t(this).width())}),o.placeHolderItem=o.draggedItem.clone().attr("data-placeholder",!0),o.draggedItem.after(o.placeHolderItem),o.placeHolderItem.children().each(function(){t(this).html("&nbsp;")})):(o.draggedItem.after(i.placeHolderTemplate),o.placeHolderItem=o.draggedItem.next().css({height:l,width:c}).attr("data-placeholder",!0)),"td"==i.tagName){var d=o.draggedItem.closest("table").get(0);t("<table id='"+d.id+"' style='border-width: 0px;' class='dragSortItem "+d.className+"'><tr></tr></table>").appendTo("body").children().append(o.draggedItem)}var u=o.draggedItem.attr("style");o.draggedItem.attr("data-origstyle",u||""),o.draggedItem.css({position:"absolute",opacity:.8,"z-index":999,height:l,width:c}),o.scroll={moveX:0,moveY:0,maxX:t(document).width()-t(window).width(),maxY:t(document).height()-t(window).height()},o.scroll.scrollY=window.setInterval(function(){if(i.scrollContainer!=window)return void t(i.scrollContainer).scrollTop(t(i.scrollContainer).scrollTop()+o.scroll.moveY);var e=t(i.scrollContainer).scrollTop();(o.scroll.moveY>0&&e<o.scroll.maxY||o.scroll.moveY<0&&e>0)&&(t(i.scrollContainer).scrollTop(e+o.scroll.moveY),o.draggedItem.css("top",o.draggedItem.offset().top+o.scroll.moveY+1))},10),o.scroll.scrollX=window.setInterval(function(){if(i.scrollContainer!=window)return void t(i.scrollContainer).scrollLeft(t(i.scrollContainer).scrollLeft()+o.scroll.moveX);var e=t(i.scrollContainer).scrollLeft();(o.scroll.moveX>0&&e<o.scroll.maxX||o.scroll.moveX<0&&e>0)&&(t(i.scrollContainer).scrollLeft(e+o.scroll.moveX),o.draggedItem.css("left",o.draggedItem.offset().left+o.scroll.moveX+1))},10),t(n).each(function(t,e){e.createDropTargets(),e.buildPositionTable()}),o.setPos(e.pageX,e.pageY),t(document).bind("mousemove",o.swapItems),t(document).bind("mouseup",o.dropItem),i.scrollContainer!=window&&t(window).bind("wheel",o.wheel)},setPos:function(e,n){var a=n-this.offset.top,s=e-this.offset.left;i.dragBetween||(a=Math.min(this.offsetLimit.bottom,Math.max(a,this.offsetLimit.top)),s=Math.min(this.offsetLimit.right,Math.max(s,this.offsetLimit.left)));var r=this.draggedItem.offsetParent().not("body").offset();if(null!=r&&(a-=r.top,s-=r.left),i.scrollContainer==window)n-=t(window).scrollTop(),e-=t(window).scrollLeft(),n=Math.max(0,n-t(window).height()+5)+Math.min(0,n-5),e=Math.max(0,e-t(window).width()+5)+Math.min(0,e-5);else{var l=t(i.scrollContainer),c=l.offset();n=Math.max(0,n-l.height()-c.top)+Math.min(0,n-c.top),e=Math.max(0,e-l.width()-c.left)+Math.min(0,e-c.left)}o.scroll.moveX=0==e?0:e*i.scrollSpeed/Math.abs(e),o.scroll.moveY=0==n?0:n*i.scrollSpeed/Math.abs(n),this.draggedItem.css({top:a,left:s})},wheel:function(e){if(o&&i.scrollContainer!=window){var n=t(i.scrollContainer),a=n.offset();if(e=e.originalEvent,e.clientX>a.left&&e.clientX<a.left+n.width()&&e.clientY>a.top&&e.clientY<a.top+n.height()){var s=(0==e.deltaMode?1:10)*e.deltaY;n.scrollTop(n.scrollTop()+s),e.preventDefault()}}},buildPositionTable:function(){var e=[];this.getItems().not([o.draggedItem[0],o.placeHolderItem[0]]).each(function(i){var n=t(this).offset();n.right=n.left+t(this).outerWidth(),n.bottom=n.top+t(this).outerHeight(),n.elm=this,e[i]=n}),this.pos=e},dropItem:function(){if(null!=o.draggedItem){var e=o.draggedItem.attr("data-origstyle");if(o.draggedItem.attr("style",e),""==e&&o.draggedItem.removeAttr("style"),o.draggedItem.removeAttr("data-origstyle"),o.styleDragHandlers(!0),o.placeHolderItem.before(o.draggedItem),o.placeHolderItem.remove(),t("[data-droptarget], .dragSortItem").remove(),
window.clearInterval(o.scroll.scrollY),window.clearInterval(o.scroll.scrollX),o.draggedItem.attr("data-origpos")!=t(n).index(o)+"-"+t(o.container).children().index(o.draggedItem)&&0==i.dragEnd.apply(o.draggedItem)){var a=o.draggedItem.attr("data-origpos").split("-"),s=t(n[a[0]].container).children().not(o.draggedItem).eq(a[1]);s.length>0?s.before(o.draggedItem):0==a[1]?t(n[a[0]].container).prepend(o.draggedItem):t(n[a[0]].container).append(o.draggedItem)}return o.draggedItem.removeAttr("data-origpos"),o.draggedItem=null,t(document).unbind("mousemove",o.swapItems),t(document).unbind("mouseup",o.dropItem),i.scrollContainer!=window&&t(window).unbind("wheel",o.wheel),!1}},swapItems:function(e){if(null==o.draggedItem)return!1;o.setPos(e.pageX,e.pageY);for(var s=o.findPos(e.pageX,e.pageY),r=o,l=0;-1==s&&i.dragBetween&&l<n.length;l++)s=n[l].findPos(e.pageX,e.pageY),r=n[l];if(-1==s)return!1;var c=function(){return t(r.container).children().not(r.draggedItem)},d=c().not(i.itemSelector).each(function(t){this.idx=c().index(this)});return null==a||a.top>o.draggedItem.offset().top||a.left>o.draggedItem.offset().left?t(r.pos[s].elm).before(o.placeHolderItem):t(r.pos[s].elm).after(o.placeHolderItem),d.each(function(){var e=c().eq(this.idx).get(0);this!=e&&c().index(this)<this.idx?t(this).insertAfter(e):this!=e&&t(this).insertBefore(e)}),t(n).each(function(t,e){e.createDropTargets(),e.buildPositionTable()}),a=o.draggedItem.offset(),!1},findPos:function(t,e){for(var i=0;i<this.pos.length;i++)if(this.pos[i].left<t&&this.pos[i].right>t&&this.pos[i].top<e&&this.pos[i].bottom>e)return i;return-1},createDropTargets:function(){i.dragBetween&&t(n).each(function(){var e=t(this.container).find("[data-placeholder]"),n=t(this.container).find("[data-droptarget]");e.length>0&&n.length>0?n.remove():0==e.length&&0==n.length&&("td"==i.tagName?t(i.placeHolderTemplate).attr("data-droptarget",!0).appendTo(this.container):t(this.container).append(o.placeHolderItem.removeAttr("data-placeholder").clone().attr("data-droptarget",!0)),o.placeHolderItem.attr("data-placeholder",!0))})}};r.init(),n.push(r)}),this},t.fn.dragsort.defaults={tagName:"",itemSelector:"",dragSelector:"",dragSelectorExclude:"input, textarea",dragEnd:function(){},dragBetween:!1,placeHolderTemplate:"",scrollContainer:window,scrollSpeed:5}}(jQuery),define("dragsort",function(){}),function(t){t.fn.addtabs=function(e){var i=t(this);e=t.extend({content:"",close:!0,monitor:"body",nav:".nav-addtabs",tab:".tab-addtabs",iframeUse:!0,simple:!1,iframeHeight:t(window).height()-50,iframeForceRefresh:!1,iframeForceRefreshTable:!1,callback:function(){}},e||{});var n=t(e.nav),o=t(e.tab);history.pushState&&t(window).on("popstate",function(i){var n=i.originalEvent.state;n&&t("a[addtabs="+n.id+"]",e.monitor).data("pushstate",!0).trigger("click")}),t(e.monitor).on("click","[addtabs]",function(i){if(0!==t(this).attr("url").indexOf("javascript:")){t(this).is("a")&&i.preventDefault();var n=t(this).attr("addtabs"),o=t(this).attr("title")?t(this).attr("title"):t.trim(t(this).text()),s=t(this).attr("url"),r=e.content?e.content:t(this).attr("content"),l="1"===t(this).attr("ajax")||"true"===t(this).attr("ajax"),c={url:s,title:o,id:n,content:r,ajax:l};if(document.title=o,history.pushState&&!t(this).data("pushstate")){var d=-1===s.indexOf("ref=addtabs")?s+(s.indexOf("?")>-1?"&":"?")+"ref=addtabs":s;try{window.history.pushState(c,o,d)}catch(i){}}t(this).data("pushstate",null),a.call(this,{id:n,title:t(this).attr("title")?t(this).attr("title"):t(this).html(),content:r,url:s,ajax:l})}}),n.on("click",".close-tab",function(){var e=t(this).prev("a").attr("aria-controls");return s(e),!1}),n.on("dblclick","li[role=presentation]",function(){t(this).find(".close-tab").trigger("click")}),n.on("click","li[role=presentation]",function(){t("a[addtabs="+t("a",this).attr("node-id")+"]").trigger("click")}),t(window).resize(function(){if("object"==typeof e.nav){var i=0;n.siblings().each(function(){i+=t(this).outerWidth()}),n.width(n.parent().width()-i)}else t("#nav").width(t("#header").find("> .navbar").width()-t(".sidebar-toggle").outerWidth()-t(".navbar-custom-menu").outerWidth()-20);r()});var a=function(i){var a,s,l,c;a=i.id,s="tab_"+i.id,l="con_"+i.id,c=i.url,c+=i.url.indexOf("?")>-1?"&addtabs=1":"?addtabs=1",e.simple&&(n.find("[role='presentation']").remove(),o.find("[role='tabpanel']").remove());var d=t("#"+s,n),u=t("#"+l,o);if(n.find("[role='presentation']").removeClass("active"),o.find("[role='tabpanel']").removeClass("active"),0===d.length){if(d=t('<li role="presentation" id="'+s+'"><a href="#'+l+'" node-id="'+i.id+'" aria-controls="'+a+'" role="tab" data-toggle="tab">'+i.title+"</a></li>"),e.close&&t("li",n).length>0&&d.append(' <i class="close-tab fa fa-remove"></i>'),0===u.length){if(u=t('<div role="tabpanel" class="tab-pane" id="'+l+'"></div>'),i.content)u.append(i.content);else if(e.iframeUse&&!i.ajax){var h=e.iframeHeight;u.append('<iframe src="'+c+'" width="100%" height="'+h+'" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling-x="no" scrolling-y="auto" allowtransparency="yes"></iframe></div>')}else t.get(c,function(t){u.append(t)});o.append(u)}e.simple||(t(".tabdrop li",n).length>0?t(".tabdrop ul",n).append(d):n.append(d))}else if(e.iframeForceRefresh)t("#"+l+" iframe").attr("src",function(t,e){return e});else if(e.iframeForceRefreshTable)try{t("#"+l+" iframe").contents().find(".btn-refresh:not([data-force-refresh=false])").length>0&&t("#"+l+" iframe")[0].contentWindow.$(".btn-refresh:not([data-force-refresh=false])").trigger("click")}catch(t){}sessionStorage.setItem("addtabs",t(this).prop("outerHTML")),d.addClass("active"),u.addClass("active"),r()},s=function(a){var s="tab_"+a,l="con_"+a,c=t("#"+s,n),d=t("#"+l,o);if(i.find("li.active").not(".tabdrop").attr("id")===s){var u=c.prev().not(".tabdrop"),h=c.next().not(".tabdrop");u.length>0?u.find("a").trigger("click"):h.length>0?h.find("a").trigger("click"):t(">li:not(.tabdrop):last > a",n).trigger("click")}c.remove(),d.remove(),r(),e.callback()},r=function(){n.refreshAddtabs()}},t.fn.refreshAddtabs=function(){var e=t(this),i=t(".tabdrop",e);0===i.length&&(i=t('<li class="dropdown pull-right hide tabdrop"><a class="dropdown-toggle" data-toggle="dropdown" href="javascript:;"><i class="glyphicon glyphicon-align-justify"></i> <b class="caret"></b></a><ul class="dropdown-menu"></ul></li>'),i.prependTo(e)),e.parent().is(".tabs-below")&&i.addClass("dropup");var n=0,o=e.width()-65,a=0,s=e.append(i.find("li")).find(">li").not(".tabdrop"),r=0;s.each(function(){r+=t(this).outerWidth(!0)}),e.width()<r?(s.each(function(){(a+=t(this).outerWidth(!0))>o&&(i.find("ul").append(t(this)),n++)}),n>0&&(i.removeClass("hide"),1===i.find(".active").length?i.addClass("active"):i.removeClass("active"))):i.addClass("hide")}}(jQuery),define("addtabs",function(){}),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),n=i.data(d.dataKey),o=t.extend({},c,i.data(),n&&n.option,"object"==typeof e&&e);n||i.data(d.dataKey,n=new d(this,o))})}function i(e){return t(e).closest("div.sp_container").find("input.sp_input")}function n(){return this.each(function(){var t=i(this),e=t.data(d.dataKey);e&&(e.prop.init_set=!0,e.clearAll(e),e.prop.init_set=!1)})}function o(){return this.each(function(){var t=i(this),e=t.data(d.dataKey);e&&e.elem.hidden.val()&&e.setInitRecord(!0)})}function a(e){return this.each(function(){if(e&&t.isArray(e)){var n=i(this),o=n.data(d.dataKey);o&&(o.clearAll(o),o.option.data=e)}})}function s(e){var n=!1;return this.each(function(){var o=i(this),a=o.data(d.dataKey);a&&("undefined"!==t.type(e)?a.disabled(a,e):n=a.disabled(a))}),n}function r(){var e="";return this.each(function(){var n=i(this),o=n.data(d.dataKey);if(o)if(o.option.multiple){var a=[];o.elem.element_box.find("li.selected_tag").each(function(e,i){a.push(t(i).text())}),e+=a.toString()}else e+=o.elem.combo_input.val()}),e}function l(){var e=[];return this.each(function(){var n=i(this),o=n.data(d.dataKey);if(o)if(o.option.multiple)o.elem.element_box.find("li.selected_tag").each(function(i,n){e.push(t(n).data("dataObj"))});else{var a=o.elem.combo_input.data("dataObj");a&&e.push(a)}}),e}var c={data:void 0,lang:"cn",multiple:!1,pagination:!0,dropButton:!0,listSize:10,multipleControlbar:!0,maxSelectLimit:0,selectToCloseList:!1,initRecord:void 0,dbTable:"tbl",keyField:"id",showField:"name",searchField:void 0,escape:!0,andOr:"OR",separator:",",orderBy:void 0,pageSize:10,params:void 0,formatItem:void 0,autoFillResult:!1,autoSelectFirst:!1,noResultClean:!0,selectOnly:!1,inputDelay:.5,eSelect:void 0,eOpen:void 0,eAjaxSuccess:void 0,eTagRemove:void 0,eClear:void 0},d=function(e,i){t.each({data:"source",keyField:"primaryKey",showField:"field",pageSize:"perPage"},function(t,e){void 0!==i[e]&&(i[t]=i[e],delete i[e])}),this.setOption(i),this.setLanguage(),this.setCssClass(),this.setProp(),this.setElem(e),this.setButtonAttrDefault(),this.setInitRecord(),this.eDropdownButton(),this.eInput(),this.eWhole()};d.version="2.19",d.dataKey="selectPageObject",d.prototype.setOption=function(e){e.searchField=e.searchField||e.showField,e.andOr=e.andOr.toUpperCase(),"AND"!==e.andOr&&"OR"!==e.andOr&&(e.andOr="AND");for(var i=["searchField"],n=0;n<i.length;n++)e[i[n]]=this.strToArray(e[i[n]]);if(e.orderBy=e.orderBy||e.showField,!1!==e.orderBy&&(e.orderBy=this.setOrderbyOption(e.orderBy,e.showField)),e.multiple&&!e.selectToCloseList&&(e.autoFillResult=!1,e.autoSelectFirst=!1),e.pagination||(e.pageSize=200),("number"!==t.type(e.listSize)||e.listSize<0)&&(e.listSize=10),"string"==typeof e.formatItem){var o=e.formatItem;e.formatItem=function(t){return"function"==typeof Template&&o.match(/\#([a-zA-Z0-9_\-]+)$/)?Template(o.substring(1),t):o.replace(/\{(.*?)\}/gi,function(e){return e=e.substring(1,e.length-1),void 0!==t[e]?t[e]:""})}}this.option=e},d.prototype.escapeHTML=function(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t},d.prototype.strToArray=function(t){return t?t.replace(/[\s　]+/g,"").split(","):""},d.prototype.setOrderbyOption=function(e,i){var n=[],o=[];if("object"==typeof e)for(var a=0;a<e.length;a++)o=t.trim(e[a]).split(" "),o.length&&n.push(2===o.length?o.concat():[o[0],"ASC"]);else o=t.trim(e).split(" "),n[0]=2===o.length?o.concat():o[0].toUpperCase().match(/^(ASC|DESC)$/i)?[i,o[0].toUpperCase()]:[o[0],"ASC"];return n},d.prototype.setLanguage=function(){var t,e=this.option;switch(e.lang){case"en":t={add_btn:"Add button",add_title:"add a box",del_btn:"Del button",del_title:"delete a box",next:"Next",next_title:"Next"+e.pageSize+" (Right key)",prev:"Prev",prev_title:"Prev"+e.pageSize+" (Left key)",first_title:"First (Shift + Left key)",last_title:"Last (Shift + Right key)",get_all_btn:"Get All (Down key)",get_all_alt:"(button)",close_btn:"Close (Tab key)",close_alt:"(button)",loading:"loading...",loading_alt:"(loading)",page_info:"page_num of page_count",select_ng:"Attention : Please choose from among the list.",select_ok:"OK : Correctly selected.",not_found:"not found",ajax_error:"An error occurred while loading data.",clear:"Clear content",select_all:"Select current page",unselect_all:"Clear current page",clear_all:"Clear all selected",max_selected:"You can only select up to max_selected_limit items"};break;case"cn":default:t={add_btn:"添加按钮",add_title:"添加区域",del_btn:"删除按钮",del_title:"删除区域",next:"下一页",next_title:"下"+e.pageSize+" (→)",prev:"上一页",prev_title:"上"+e.pageSize+" (←)",first_title:"首页 (Shift + ←)",last_title:"尾页 (Shift + →)",get_all_btn:"获得全部 (↓)",get_all_alt:"(按钮)",close_btn:"关闭 (Tab键)",close_alt:"(按钮)",loading:"读取中...",loading_alt:"(读取中)",page_info:"第 page_num 页(共page_count页)",select_ng:"请注意：请从列表中选择.",select_ok:"OK : 已经选择.",not_found:"无查询结果",ajax_error:"加载数据时发生了错误！",clear:"清除内容",select_all:"选择当前页项目",unselect_all:"取消选择当前页项目",clear_all:"清除全部已选择项目",max_selected:"最多只能选择 max_selected_limit 个项目"}}this.message=t},d.prototype.setCssClass=function(){var t={container:"sp_container",container_open:"sp_container_open",re_area:"sp_result_area",result_open:"sp_result_area_open",control_box:"sp_control_box",element_box:"sp_element_box",navi:"sp_navi",results:"sp_results",re_off:"sp_results_off",select:"sp_over",select_ok:"sp_select_ok",select_ng:"sp_select_ng",selected:"sp_selected",input_off:"sp_input_off",message_box:"sp_message_box",disabled:"sp_disabled",button:"sp_button",caret_open:"sp_caret_open",btn_on:"sp_btn_on",btn_out:"sp_btn_out",input:"sp_input",clear_btn:"sp_clear_btn",align_right:"sp_align_right"};this.css_class=t},d.prototype.setProp=function(){this.prop={disabled:!1,current_page:1,max_page:1,is_loading:!1,xhr:!1,key_paging:!1,key_select:!1,prev_value:"",selected_text:"",last_input_time:void 0,init_set:!1},this.template={tag:{content:'<li class="selected_tag" itemvalue="#item_value#">#item_text#<span class="tag_close"><i class="spfont sp-close"></i></span></li>',textKey:"#item_text#",valueKey:"#item_value#"},page:{current:"page_num",total:"page_count"},msg:{maxSelectLimit:"max_selected_limit"}}},d.prototype.elementRealSize=function(e,i){var n,o,a,s={absolute:!1,clone:!1,includeMargin:!1,display:"block"},r=s,l=e.eq(0),c=[],d="";n=function(){a=l.parents().addBack().filter(":hidden"),d+="visibility: hidden !important; display: "+r.display+" !important; ",!0===r.absolute&&(d+="position: absolute !important;"),a.each(function(){var e=t(this),i=e.attr("style");c.push(i),e.attr("style",i?i+";"+d:d)})},o=function(){a.each(function(e){var i=t(this),n=c[e];void 0===n?i.removeAttr("style"):i.attr("style",n)})},n();var u=/(outer)/.test(i)?l[i](r.includeMargin):l[i]();return o(),u},d.prototype.setElem=function(e){var i={},n=this.option,o=this.css_class,a=this.message,s=t(e),r=s.css("width"),l=s.outerWidth();r.indexOf("%")>-1||s.parent().length>0&&s.parent().width()==l?l="100%":(l<=0&&(l=this.elementRealSize(s,"outerWidth")),l<150&&(l=150)),i.combo_input=s.attr({autocomplete:"off"}).addClass(o.input).wrap("<div>"),n.selectOnly&&i.combo_input.prop("readonly",!0),i.container=i.combo_input.parent().addClass(o.container),i.combo_input.prop("disabled")&&(n.multiple?i.container.addClass(o.disabled):i.combo_input.addClass(o.input_off)),i.container.width(l),i.button=t("<div>").addClass(o.button),i.dropdown=t('<span class="sp_caret"></span>'),i.clear_btn=t("<div>").html(t("<i>").addClass("spfont sp-close")).addClass(o.clear_btn).attr("title",a.clear),n.dropButton||i.clear_btn.addClass(o.align_right),i.element_box=t("<ul>").addClass(o.element_box),n.multiple&&n.multipleControlbar&&(i.control=t("<div>").addClass(o.control_box)),i.result_area=t("<div>").addClass(o.re_area),n.pagination&&(i.navi=t("<div>").addClass("sp_pagination").append("<ul>")),i.results=t("<ul>").addClass(o.results);var c=i.combo_input.attr("id")||i.combo_input.attr("name"),d=i.combo_input.attr("name")||"selectPage",u=d,h=c;if(i.hidden=t('<input type="hidden" class="sp_hidden" />').attr({name:u,id:h}).val(""),i.combo_input.attr({name:void 0!==s.data("name")?s.data("name"):d+"_text",id:c+"_text"}),i.hidden.attr("data-rule",i.combo_input.data("rule")||""),i.combo_input.attr("novalidate","novalidate"),i.container.append(i.hidden),n.dropButton&&(i.container.append(i.button),i.button.append(i.dropdown)),t(document.body).append(i.result_area),i.result_area.append(i.results),n.pagination&&i.result_area.append(i.navi),n.multiple){n.multipleControlbar&&(i.control.append('<button type="button" class="btn btn-default sp_clear_all" ><i class="spfont sp-clear"></i></button>'),i.control.append('<button type="button" class="btn btn-default sp_unselect_all" ><i class="spfont sp-unselect-all"></i></button>'),i.control.append('<button type="button" class="btn btn-default sp_select_all" ><i class="spfont sp-select-all"></i></button>'),i.control_text=t("<p>"),i.control.append(i.control_text),i.result_area.prepend(i.control)),i.container.addClass("sp_container_combo"),i.combo_input.addClass("sp_combo_input").before(i.element_box);var p=t("<li>").addClass("input_box");p.append(i.combo_input),i.element_box.append(p),i.combo_input.attr("placeholder")&&i.combo_input.attr("placeholder_bak",i.combo_input.attr("placeholder"))}this.elem=i},d.prototype.setButtonAttrDefault=function(){this.option.dropButton&&this.elem.button.attr("title",this.message.close_btn)},d.prototype.setInitRecord=function(e){var i=this,n=i.option,o=i.elem,a="";if("undefined"!=t.type(o.combo_input.data("init"))&&(n.initRecord=String(o.combo_input.data("init"))),e||n.initRecord||!o.combo_input.val()||(n.initRecord=o.combo_input.val()),o.combo_input.val(""),e||o.hidden.val(n.initRecord),a=e&&o.hidden.val()?o.hidden.val():n.initRecord)if("object"==typeof n.data){var s=new Array,r=a.split(",");t.each(r,function(t,e){for(var i=0;i<n.data.length;i++)if(n.data[i][n.keyField]==e){s.push(n.data[i]);break}}),!n.multiple&&s.length>1&&(s=[s[0]]),i.afterInit(i,s)}else{var l=n.params,c={},d=(n.searchField,{searchTable:n.dbTable,searchKey:n.keyField,searchValue:a,orderBy:n.orderBy,showField:n.showField,keyField:n.keyField,keyValue:a,searchField:n.searchField});if(l){var u=t.isFunction(l)?l(i):l;c=u&&t.isPlainObject(u)?t.extend({},d,u):d}else c=d;t.ajax({dataType:"json",type:"POST",url:n.data,data:c,success:function(e){var o=null;n.eAjaxSuccess&&t.isFunction(n.eAjaxSuccess)&&(o=n.eAjaxSuccess(e)),i.afterInit(i,o.list)},error:function(t,e,n){i.ajaxErrorNotify(i,n)}})}},d.prototype.afterInit=function(e,i){if(i&&(!t.isArray(i)||0!==i.length)){t.isArray(i)||(i=[i]);var n=e.option,o=e.css_class;if(e.data=i,n.multiple)e.prop.init_set=!0,e.clearAll(e),t.each(i,function(t,i){var o=n.escape?e.escapeHTML(i[n.keyField]):i[n.keyField],a=n.escape?e.escapeHTML(i[n.showField]):i[n.showField],s={text:a,value:o};e.isAlreadySelected(e,s)||e.addNewTag(e,i,s)}),e.tagValuesSet(e),e.inputResize(e),e.elem.hidden.blur(),e.prop.init_set=!1;else{var a=i[0],s=n.escape?e.escapeHTML(a[n.keyField]):a[n.keyField],r=n.escape?e.escapeHTML(a[n.showField]):a[n.showField];e.elem.combo_input.val(r),e.elem.hidden.val(s),e.prop.prev_value=r,e.prop.selected_text=r,n.selectOnly&&e.elem.combo_input.attr("title",e.message.select_ok).removeClass(o.select_ng).addClass(o.select_ok),e.putClearButton()}}},d.prototype.eDropdownButton=function(){var t=this;t.option.dropButton&&t.elem.button.mouseup(function(e){e.stopPropagation(),t.elem.result_area.is(":hidden")&&!t.elem.combo_input.prop("disabled")?t.elem.combo_input.focus():t.hideResults(t)})},d.prototype.eInput=function(){var e=this,i=e.option,n=e.elem,o=e.message,a=function(){e.prop.page_move=!1,e.suggest(e),e.setCssFocusedInput(e)};n.combo_input.keyup(function(t){e.processKey(e,t)}).keydown(function(t){e.processControl(e,t)}).focus(function(t){n.result_area.is(":hidden")&&(t.stopPropagation(),e.prop.first_show=!0,a())}),n.container.on("click.SelectPage","div."+e.css_class.clear_btn,function(n){n.stopPropagation(),e.disabled(e)||(e.clearAll(e,!0),e.elem.hidden.change(),i.eClear&&t.isFunction(i.eClear)&&i.eClear(e))}),n.result_area.on("mousedown.SelectPage",function(t){t.stopPropagation()}),i.multiple&&(i.multipleControlbar&&(n.control.find(".sp_select_all").on("click.SelectPage",function(t){e.selectAllLine(e)}).hover(function(){n.control_text.html(o.select_all)},function(){n.control_text.html("")}),n.control.find(".sp_unselect_all").on("click.SelectPage",function(t){e.unSelectAllLine(e)}).hover(function(){n.control_text.html(o.unselect_all)},function(){n.control_text.html("")}),n.control.find(".sp_clear_all").on("click.SelectPage",function(t){e.clearAll(e,!0)}).hover(function(){n.control_text.html(o.clear_all)},function(){n.control_text.html("")})),n.element_box.on("click.SelectPage",function(e){var i=e.target||e.srcElement;t(i).is("ul")&&n.combo_input.focus()}),n.element_box.on("click.SelectPage","span.tag_close",function(){var n=t(this).closest("li"),o=n.data("dataObj");e.removeTag(e,n),a(),i.eTagRemove&&t.isFunction(i.eTagRemove)&&i.eTagRemove([o])}),e.inputResize(e))},d.prototype.eWhole=function(){var e=this,i=e.css_class,n=function(t){t.elem.combo_input.val(""),t.option.multiple||t.elem.hidden.val(""),t.prop.selected_text=""};t(document.body).off("mousedown.selectPage").on("mousedown.selectPage",function(e){var o=e.target||e.srcElement,a=t(o).closest("div."+i.container);t("div."+i.container+"."+i.container_open).each(function(){if(this!=a[0]){var e=t(this),o=e.find("input."+i.input).data(d.dataKey);if(!o.elem.combo_input.val()&&o.elem.hidden.val()&&!o.option.multiple)return o.prop.current_page=1,n(o),o.hideResults(o),!0;o.elem.results.find("li").not("."+i.message_box).length?o.option.autoFillResult?o.elem.hidden.val()?o.hideResults(o):o.elem.results.find("li.sp_over").length?o.selectCurrentLine(o,!0):o.option.autoSelectFirst?(o.nextLine(o),o.selectCurrentLine(o,!0)):o.hideResults(o):o.hideResults(o):(o.option.noResultClean?n(o):o.option.multiple||o.elem.hidden.val(""),o.hideResults(o))}})})},d.prototype.eResultList=function(){var e=this,i=this.css_class;e.elem.results.children("li").hover(function(){if(e.prop.key_select)return void(e.prop.key_select=!1);t(this).hasClass(i.selected)||t(this).hasClass(i.message_box)||(t(this).addClass(i.select),e.setCssFocusedResults(e))},function(){t(this).removeClass(i.select)}).click(function(n){if(e.prop.key_select)return void(e.prop.key_select=!1);n.preventDefault(),n.stopPropagation(),t(this).hasClass(i.selected)||e.selectCurrentLine(e,!1)})},d.prototype.eScroll=function(){var e=this.css_class;t(window).on("scroll.SelectPage",function(i){t("div."+e.container+"."+e.container_open).each(function(){var i=t(this),n=i.find("input."+e.input).data(d.dataKey),o=n.elem.result_area.offset(),a=t(window).scrollTop(),s=t(document).height(),r=t(window).height(),l=n.elem.result_area.outerHeight(),c=o.top+l,u=s>r,h=n.elem.result_area.hasClass("shadowDown");u&&(h?c>r+a&&n.calcResultsSize(n):o.top<a&&n.calcResultsSize(n))})})},d.prototype.ePaging=function(){var t=this;t.option.pagination&&(t.elem.navi.find("li.csFirstPage").off("click").on("click",function(e){e.preventDefault(),t.firstPage(t)}),t.elem.navi.find("li.csPreviousPage").off("click").on("click",function(e){e.preventDefault(),t.prevPage(t)}),t.elem.navi.find("li.csNextPage").off("click").on("click",function(e){e.preventDefault(),t.nextPage(t)}),t.elem.navi.find("li.csLastPage").off("click").on("click",function(e){e.preventDefault(),t.lastPage(t)}))},d.prototype.ajaxErrorNotify=function(t,e){t.showMessage(t,t.message.ajax_error)},d.prototype.showMessage=function(t,e){if(e){var i='<li class="'+t.css_class.message_box+'"><i class="spfont sp-warning"></i> '+e+"</li>";t.elem.results.empty().append(i).show(),t.calcResultsSize(t),t.setOpenStatus(t,!0),t.elem.control&&t.elem.control.hide(),t.option.pagination&&t.elem.navi.hide()}},d.prototype.scrollWindow=function(e,i){var n,o=e.getCurrentLine(e),a=o&&!i?o.offset().top:e.elem.container.offset().top;e.prop.size_li=e.elem.results.children("li:first").outerHeight(),n=e.prop.size_li;var s,r=t(window).height(),l=t(window).scrollTop(),c=l+r-n;if(o.length)if(a<l||n>r)s=a-l;else{if(!(a>c))return;s=a-c}else a<l&&(s=a-l);window.scrollBy(0,s)},d.prototype.setOpenStatus=function(t,e){var i=t.elem,n=t.css_class;e?(i.container.addClass(n.container_open),i.result_area.addClass(n.result_open)):(i.container.removeClass(n.container_open),i.result_area.removeClass(n.result_open))},d.prototype.setCssFocusedInput=function(t){},d.prototype.setCssFocusedResults=function(t){},d.prototype.checkValue=function(t){var e=t.elem.combo_input.val();e!=t.prop.prev_value&&(t.prop.prev_value=e,t.prop.first_show=!1,t.prop.current_page=1,t.option.selectOnly&&t.setButtonAttrDefault(),t.option.multiple||e||(t.elem.combo_input.val(""),t.elem.hidden.val(""),t.elem.clear_btn.remove()),t.suggest(t))},d.prototype.processKey=function(e,i){-1===t.inArray(i.keyCode,[37,38,39,40,27,9,13])&&(16!=i.keyCode&&e.setCssFocusedInput(e),e.inputResize(e),"string"===t.type(e.option.data)?(e.prop.last_input_time=i.timeStamp,setTimeout(function(){i.timeStamp-e.prop.last_input_time==0&&e.checkValue(e)},1e3*e.option.inputDelay)):e.checkValue(e))},d.prototype.processControl=function(e,i){if(t.inArray(i.keyCode,[37,38,39,40,27,9])>-1&&e.elem.result_area.is(":visible")||t.inArray(i.keyCode,[13,9])>-1&&e.getCurrentLine(e))switch(i.preventDefault(),i.stopPropagation(),i.cancelBubble=!0,i.returnValue=!1,i.keyCode){case 37:i.shiftKey?e.firstPage(e):e.prevPage(e);break;case 38:e.prop.key_select=!0,e.prevLine(e);break;case 39:i.shiftKey?e.lastPage(e):e.nextPage(e);break;case 40:e.elem.results.children("li").length?(e.prop.key_select=!0,e.nextLine(e)):e.suggest(e);break;case 9:e.prop.key_paging=!0,e.selectCurrentLine(e,!0);break;case 13:e.selectCurrentLine(e,!0);break;case 27:e.prop.key_paging=!0,e.hideResults(e)}},d.prototype.abortAjax=function(t){t.prop.xhr&&(t.prop.xhr.abort(),t.prop.xhr=!1)},d.prototype.suggest=function(e){var i,n=t.trim(e.elem.combo_input.val());i=e.option.multiple?n:n&&n==e.prop.selected_text?"":n,i=i.split(e.option.separator),e.option.eOpen&&t.isFunction(e.option.eOpen)&&e.option.eOpen.call(e),e.abortAjax(e);var o=e.prop.current_page||1;"object"==typeof e.option.data?e.searchForJson(e,i,o):e.searchForDb(e,i,o)},d.prototype.setLoading=function(t){""===t.elem.results.html()&&t.setOpenStatus(t,!0)},d.prototype.searchForDb=function(e,i,n){var o=e.option;o.eAjaxSuccess&&t.isFunction(o.eAjaxSuccess)||e.hideResults(e);var a=o.params,s={},r=o.searchField;i.length&&i[0]&&i.join(e.option.separator)!==e.prop.prev_value&&(n=1);var l={q_word:i,pageNumber:n,pageSize:o.pageSize,andOr:o.andOr,orderBy:o.orderBy,searchTable:o.dbTable,showField:e.option.showField,keyField:e.option.keyField,searchField:e.option.searchField};if(!1!==o.orderBy&&(l.orderBy=o.orderBy),l[r]=i[0],a){var c=t.isFunction(a)?a(e):a;s=c&&t.isPlainObject(c)?t.extend({},l,c):l}else s=l;e.prop.xhr=t.ajax({dataType:"json",url:o.data,type:"POST",data:s,success:function(a){if(!a||!t.isPlainObject(a))return e.hideResults(e),void e.ajaxErrorNotify(e,errorThrown);var s={},r={};try{s=o.eAjaxSuccess(a),r.originalResult=s.list,r.cnt_whole=s.totalRow}catch(t){return void e.showMessage(e,e.message.ajax_error)}if(e.elem.navi&&t(e.elem.navi).toggleClass("hide",r.cnt_whole<=r.originalResult.length),r.candidate=[],r.keyField=[],"object"!=typeof r.originalResult)return e.prop.xhr=null,void e.notFoundSearch(e);r.cnt_page=r.originalResult.length;for(var l=0;l<r.cnt_page;l++)for(var c in r.originalResult[l])c==o.keyField&&r.keyField.push(r.originalResult[l][c]),c==o.showField&&r.candidate.push(r.originalResult[l][c]);e.prepareResults(e,r,i,n)},error:function(t,i,n){"abort"!=i&&(e.hideResults(e),e.ajaxErrorNotify(e,n))},complete:function(){e.prop.xhr=null}})},d.prototype.searchForJson=function(e,i,n){var o=e.option,a=[],s=[],r=[],l={},c=0,d=[];do{s[c]=i[c].replace(/\W/g,"\\$&").toString(),d[c]=new RegExp(s[c],"gi"),c++}while(c<i.length);for(var c=0;c<o.data.length;c++){for(var u,h=!1,p=o.data[c],f=0;f<d.length;f++)if(u=p[o.searchField],o.formatItem&&t.isFunction(o.formatItem)&&(u=o.formatItem(p)),u.match(d[f])){if(h=!0,"OR"==o.andOr)break}else if(h=!1,"AND"==o.andOr)break;h&&a.push(p)}if(!1===o.orderBy)r=a.concat();else{for(var m=new RegExp("^"+s[0]+"$","gi"),g=new RegExp("^"+s[0],"gi"),v=[],y=[],b=[],c=0;c<a.length;c++){var x=o.orderBy[0][0],w=String(a[c][x]);w.match(m)?v.push(a[c]):w.match(g)?y.push(a[c]):b.push(a[c])}o.orderBy[0][1].match(/^asc$/i)?(v=e.sortAsc(e,v),y=e.sortAsc(e,y),b=e.sortAsc(e,b)):(v=e.sortDesc(e,v),y=e.sortDesc(e,y),b=e.sortDesc(e,b)),r=r.concat(v).concat(y).concat(b)}if(l.cnt_whole=r.length,e.prop.page_move)r.length<=(n-1)*o.pageSize&&(n=1,e.prop.current_page=1);else if(!o.multiple){var k=e.elem.hidden.val();if("undefined"!==t.type(k)&&""!==t.trim(k)){var _=0;t.each(r,function(t,e){if(e[o.keyField]==k)return _=t+1,!1}),n=Math.ceil(_/o.pageSize),n<1&&(n=1),e.prop.current_page=n}}var C=(n-1)*o.pageSize,S=C+o.pageSize;l.originalResult=[];for(var c=C;c<S&&void 0!==r[c];c++){l.originalResult.push(r[c]);for(var T in r[c])T==o.keyField&&(void 0===l.keyField&&(l.keyField=[]),l.keyField.push(r[c][T])),T==o.showField&&(void 0===l.candidate&&(l.candidate=[]),l.candidate.push(r[c][T]))}void 0===l.candidate&&(l.candidate=[]),l.cnt_page=l.candidate.length,e.prepareResults(e,l,i,n)},d.prototype.sortAsc=function(e,i){return i.sort(function(i,n){var o=i[e.option.orderBy[0][0]],a=n[e.option.orderBy[0][0]];return"number"===t.type(o)?o-a:String(o).localeCompare(String(a))}),i},d.prototype.sortDesc=function(e,i){return i.sort(function(i,n){var o=i[e.option.orderBy[0][0]],a=n[e.option.orderBy[0][0]];return"number"===t.type(o)?a-o:String(a).localeCompare(String(o))}),i},d.prototype.notFoundSearch=function(t){t.elem.results.empty(),t.calcResultsSize(t),t.setOpenStatus(t,!0),t.setCssFocusedInput(t)},d.prototype.prepareResults=function(t,e,i,n){t.data=e.originalResult,t.option.pagination&&t.setNavi(t,e.cnt_whole,e.cnt_page,n),e.keyField||(e.keyField=!1),t.option.selectOnly&&1===e.candidate.length&&e.candidate[0]==i[0]&&(t.elem.hidden.val(e.keyField[0]),this.setButtonAttrDefault());var o=!1;i&&i.length&&i[0]&&(o=!0),t.displayResults(t,e,o)},d.prototype.setNavi=function(t,e,i,n){var o=t.message,a=t.elem.navi.find("ul"),s=Math.ceil(e/t.option.pageSize);0===s?n=0:s<n?n=s:0===n&&(n=1),t.prop.current_page=n,t.prop.max_page=s,function(t,e,i,n){var a=function(){return o.page_info.replace(t.template.page.current,i).replace(t.template.page.total,n)};if(0===e.find("li").length){e.hide().empty();e.append('<li class="csFirstPage" title="'+o.first_title+'" ><a href="javascript:void(0);"> <i class="spfont sp-first"></i> </a></li>'),e.append('<li class="csPreviousPage" title="'+o.prev_title+'" ><a href="javascript:void(0);"><i class="spfont sp-previous"></i></a></li>'),e.append('<li class="pageInfoBox"><a href="javascript:void(0);"> '+a()+" </a></li>"),e.append('<li class="csNextPage" title="'+o.next_title+'" ><a href="javascript:void(0);"><i class="spfont sp-next"></i></a></li>'),e.append('<li class="csLastPage" title="'+o.last_title+'" ><a href="javascript:void(0);"> <i class="spfont sp-last"></i> </a></li>'),e.show()}else e.find("li.pageInfoBox a").html(a())}(t,a,n,s);var r="disabled",l=a.find("li.csFirstPage"),c=a.find("li.csPreviousPage"),d=a.find("li.csNextPage"),u=a.find("li.csLastPage");1===n||0===n?(l.hasClass(r)||l.addClass(r),c.hasClass(r)||c.addClass(r)):(l.hasClass(r)&&l.removeClass(r),c.hasClass(r)&&c.removeClass(r)),n===s||0===s?(d.hasClass(r)||d.addClass(r),u.hasClass(r)||u.addClass(r)):(d.hasClass(r)&&d.removeClass(r),u.hasClass(r)&&u.removeClass(r)),s>1&&t.ePaging()},d.prototype.displayResults=function(e,i,n){var o=e.option,a=e.elem;if(a.results.hide().empty(),o.multiple&&"number"===t.type(o.maxSelectLimit)&&o.maxSelectLimit>0){var s=a.element_box.find("li.selected_tag").length;if(s>0&&s>=o.maxSelectLimit){var r=e.message.max_selected;return void e.showMessage(e,r.replace(e.template.msg.maxSelectLimit,o.maxSelectLimit))}}if(i.candidate.length)for(var l=i.candidate,c=i.keyField,d=a.hidden.val(),u=d?d.split(","):[],h="",p=0;p<l.length;p++){if(o.formatItem&&t.isFunction(o.formatItem))try{h=o.formatItem(i.originalResult[p])}catch(t){console.error("formatItem内容格式化函数内容设置不正确！"),h=o.escape?e.escapeHTML(l[p]):l[p]}else h=o.escape?e.escapeHTML(l[p]):l[p];var f=t("<li>").html(h).attr({pkey:c[p],index:p});o.formatItem||f.attr("title",h),-1!==t.inArray(c[p].toString(),u)&&f.addClass(e.css_class.selected),f.data("dataObj",i.originalResult[p]),a.results.append(f)}else{var m='<li class="'+e.css_class.message_box+'"><i class="spfont sp-warning"></i> '+e.message.not_found+"</li>";a.results.append(m)}a.results.show(),o.multiple&&o.multipleControlbar&&a.control.show(),o.pagination&&a.navi.show(),e.calcResultsSize(e),e.setOpenStatus(e,!0),e.eResultList(),e.eScroll(),n&&i.candidate.length&&o.autoSelectFirst&&e.nextLine(e)},d.prototype.calcResultsSize=function(e){var i=e.option,n=e.elem,o=function(){if("static"!==n.container.css("position")){if(!i.pagination){
var e=n.results.find("li:first").outerHeight(!0),o=e*i.listSize;n.results.css({"max-height":o,"overflow-y":"auto"})}var a=t(document).width(),s=t(document).height(),r=t(window).height(),l=n.container.offset(),c=t(window).scrollTop(),d=n.result_area.outerWidth(),o=n.result_area.outerHeight(),u=l.left,h=n.container.outerHeight(),p=l.left+d>a?u-(d-n.container.outerWidth()):u,f=l.top,m=0,g=f+h+o+5,v=f+o+5,y=s>r;return f-c-5>o&&y&&g>r+c||!y&&g>r&&f>=v?(m=l.top-o-5,n.result_area.removeClass("shadowUp shadowDown").addClass("shadowUp")):(m=l.top+(i.multiple?n.container.outerHeight():h),n.result_area.removeClass("shadowUp shadowDown").addClass("shadowDown"),m+=5),{top:m+"px",left:p+"px"}}var l=n.combo_input.offset();n.result_area.css({top:l.top+n.combo_input.outerHeight()+"px",left:l.left+"px"})};if(n.result_area.is(":visible"))n.result_area.css(o());else{var a=o();n.result_area.css(a).show(1,function(){var t=o();a.top===t.top&&a.left===t.left||n.result_area.css(t)})}},d.prototype.hideResults=function(e){e.prop.key_paging&&(e.scrollWindow(e,!0),e.prop.key_paging=!1),e.setCssFocusedInput(e),e.option.autoFillResult,e.elem.results.empty(),e.elem.result_area.hide(),e.setOpenStatus(e,!1),t(window).off("scroll.SelectPage"),e.abortAjax(e),e.setButtonAttrDefault()},d.prototype.disabled=function(e,i){var n=(e.option,e.elem);if("undefined"===t.type(i))return n.combo_input.prop("disabled");"boolean"===t.type(i)&&(n.combo_input.prop("disabled",i),i?n.container.addClass(e.css_class.disabled):n.container.removeClass(e.css_class.disabled))},d.prototype.firstPage=function(t){t.prop.current_page>1&&(t.prop.current_page=1,t.prop.page_move=!0,t.suggest(t))},d.prototype.prevPage=function(t){t.prop.current_page>1&&(t.prop.current_page--,t.prop.page_move=!0,t.suggest(t))},d.prototype.nextPage=function(t){t.prop.current_page<t.prop.max_page&&(t.prop.current_page++,t.prop.page_move=!0,t.suggest(t))},d.prototype.lastPage=function(t){t.prop.current_page<t.prop.max_page&&(t.prop.current_page=t.prop.max_page,t.prop.page_move=!0,t.suggest(t))},d.prototype.afterAction=function(t,e){t.inputResize(t),t.elem.combo_input.change(),t.setCssFocusedInput(t),t.prop.init_set||(t.option.multiple?(t.option.selectToCloseList&&(t.hideResults(t),t.elem.combo_input.blur()),!t.option.selectToCloseList&&e&&(t.suggest(t),t.elem.combo_input.focus())):(t.hideResults(t),t.elem.combo_input.blur()))},d.prototype.selectCurrentLine=function(e,i){e.scrollWindow(e,!0);var n=e.option,o=e.getCurrentLine(e);if(o){var a=o.data("dataObj"),s=a[n.showField]||o.text(),r=o.attr("pkey");if(n.multiple){e.elem.combo_input.val("");var l={text:s,value:r};e.isAlreadySelected(e,l)||(e.addNewTag(e,a,l),e.tagValuesSet(e))}else e.elem.combo_input.val(s),e.elem.hidden.val(r);n.selectOnly&&e.setButtonAttrDefault(),n.eSelect&&t.isFunction(n.eSelect)&&n.eSelect(a,e),e.prop.prev_value=e.elem.combo_input.val(),e.prop.selected_text=e.elem.combo_input.val(),e.putClearButton()}e.afterAction(e,!0)},d.prototype.putClearButton=function(){this.option.multiple||this.elem.combo_input.prop("disabled")||this.elem.container.append(this.elem.clear_btn)},d.prototype.selectAllLine=function(e){var i=e.option,n=new Array;e.elem.results.find("li").each(function(o,a){var s=t(a),r=s.data("dataObj"),l=r[i.showField]||s.text(),c=s.attr("pkey"),d={text:l,value:c};if(e.isAlreadySelected(e,d)||(e.addNewTag(e,r,d),e.tagValuesSet(e)),n.push(r),"number"===t.type(i.maxSelectLimit)&&i.maxSelectLimit>0&&i.maxSelectLimit===e.elem.element_box.find("li.selected_tag").length)return!1}),i.eSelect&&t.isFunction(i.eSelect)&&i.eSelect(n,e),e.afterAction(e,!0)},d.prototype.unSelectAllLine=function(e){var i=e.option,n=(e.elem.results.find("li").length,[]);e.elem.results.find("li").each(function(i,o){var a=t(o).attr("pkey"),s=e.elem.element_box.find('li.selected_tag[itemvalue="'+a+'"]');s.length&&n.push(s.data("dataObj")),e.removeTag(e,s)}),e.afterAction(e,!0),i.eTagRemove&&t.isFunction(i.eTagRemove)&&i.eTagRemove(n)},d.prototype.clearAll=function(e,i){var n=e.option,o=[];n.multiple&&(e.elem.element_box.find("li.selected_tag").each(function(e,i){o.push(t(i).data("dataObj")),i.remove()}),e.elem.element_box.find("li.selected_tag").remove()),e.reset(e),e.afterAction(e,i),n.multiple?n.eTagRemove&&t.isFunction(n.eTagRemove)&&n.eTagRemove(o):e.elem.clear_btn.remove()},d.prototype.reset=function(t){t.elem.combo_input.val(""),t.elem.hidden.val(""),t.prop.prev_value="",t.prop.selected_text="",t.prop.current_page=1},d.prototype.getCurrentLine=function(t){if(t.elem.result_area.is(":hidden"))return!1;var e=t.elem.results.find("li."+t.css_class.select);return!!e.length&&e},d.prototype.isAlreadySelected=function(e,i){var n=!1;if(i.value){var o=e.elem.hidden.val();if(o){var a=o.split(",");a&&a.length&&-1!=t.inArray(i.value,a)&&(n=!0)}}return n},d.prototype.addNewTag=function(e,i,n){if(e.option.multiple&&i&&n){var o,a=e.template.tag.content;a=a.replace(e.template.tag.textKey,n.text),a=a.replace(e.template.tag.valueKey,n.value),o=t(a),o.data("dataObj",i),e.elem.combo_input.prop("disabled")&&o.find("span.tag_close").hide(),e.elem.combo_input.closest("li").before(o)}},d.prototype.removeTag=function(e,i){var n=t(i).attr("itemvalue"),o=e.elem.hidden.val();if("undefined"!=t.type(n)&&o){var a=o.split(","),s=t.inArray(n.toString(),a);-1!=s&&(a.splice(s,1),e.elem.hidden.val(a.toString()).trigger("change"))}t(i).remove(),e.inputResize(e)},d.prototype.tagValuesSet=function(e){if(e.option.multiple){var i=e.elem.element_box.find("li.selected_tag");if(i&&i.length){var n=new Array;t.each(i,function(e,i){var o=t(i).attr("itemvalue");"undefined"!==t.type(o)&&n.push(o)}),n.length&&e.elem.hidden.val(n.join(",")).trigger("change")}}},d.prototype.inputResize=function(t){if(t.option.multiple){var e=t.elem.combo_input.closest("li");0===t.elem.element_box.find("li.selected_tag").length?(e.hasClass("full_width")||e.addClass("full_width"),t.elem.combo_input.attr("placeholder_bak")&&t.elem.combo_input.attr("placeholder",t.elem.combo_input.attr("placeholder_bak")).removeAttr("style")):function(t,e){e.removeClass("full_width");var i=t.elem.combo_input.val().length+1,n=.75*i+"em";t.elem.combo_input.css("width",n).removeAttr("placeholder")}(t,e)}},d.prototype.nextLine=function(t){var e,i=t.getCurrentLine(t);if(i?(e=t.elem.results.children("li").index(i),i.removeClass(t.css_class.select)):e=-1,++e<t.elem.results.children("li").length){t.elem.results.children("li").eq(e).addClass(t.css_class.select),t.setCssFocusedResults(t)}else t.setCssFocusedInput(t);t.scrollWindow(t,!1)},d.prototype.prevLine=function(t){var e,i=t.getCurrentLine(t);if(i?(e=t.elem.results.children("li").index(i),i.removeClass(t.css_class.select)):e=t.elem.results.children("li").length,--e>-1){t.elem.results.children("li").eq(e).addClass(t.css_class.select),t.setCssFocusedResults(t)}else t.setCssFocusedInput(t);t.scrollWindow(t,!1)};var u=t.fn.selectPage;t.fn.selectPage=e,t.fn.selectPage.Constructor=d,t.fn.selectPageClear=n,t.fn.selectPageRefresh=o,t.fn.selectPageData=a,t.fn.selectPageDisabled=s,t.fn.selectPageText=r,t.fn.selectPageSelectedData=l,t.fn.selectPage.noConflict=function(){return t.fn.selectPage=u,this}}(window.jQuery),define("selectpage",function(){}),function(t,e){if("function"==typeof define&&define.amd)define("bootstrap-daterangepicker",["moment","jquery"],function(t,i){return i.fn||(i.fn={}),e(t,i)});else if("object"==typeof module&&module.exports){var i="undefined"!=typeof window?window.jQuery:void 0;i||(i=require("jquery"),i.fn||(i.fn={}));var n="undefined"!=typeof window&&void 0!==window.moment?window.moment:require("moment");module.exports=e(n,i)}else t.daterangepicker=e(t.moment,t.jQuery)}(this,function(t,e){var i=function(i,n,o){if(this.parentEl="body",this.element=e(i),this.startDate=t().startOf("day"),this.endDate=t().endOf("day"),this.minDate=!1,this.maxDate=!1,this.dateLimit=!1,this.autoApply=!1,this.singleDatePicker=!1,this.showDropdowns=!1,this.showWeekNumbers=!1,this.showISOWeekNumbers=!1,this.showCustomRangeLabel=!0,this.timePicker=!1,this.timePicker24Hour=!1,this.timePickerIncrement=1,this.timePickerSeconds=!1,this.linkedCalendars=!0,this.autoUpdateInput=!0,this.alwaysShowCalendars=!1,this.ranges={},this.opens="right",this.element.hasClass("pull-right")&&(this.opens="left"),this.drops="down",this.element.hasClass("dropup")&&(this.drops="up"),this.buttonClasses="btn btn-sm",this.applyClass="btn-success",this.cancelClass="btn-default",this.locale={direction:"ltr",format:t.localeData().longDateFormat("L"),separator:" - ",applyLabel:"Apply",cancelLabel:"Cancel",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:t.weekdaysMin(),monthNames:t.monthsShort(),firstDay:t.localeData().firstDayOfWeek()},this.callback=function(){},this.isShowing=!1,this.leftCalendar={},this.rightCalendar={},"object"==typeof n&&null!==n||(n={}),n=e.extend(this.element.data(),n),"string"==typeof n.template||n.template instanceof e||(n.template='<div class="daterangepicker dropdown-menu"><div class="calendar left"><div class="daterangepicker_input"><input class="input-mini form-control" type="text" name="daterangepicker_start" value="" /><i class="fa fa-calendar glyphicon glyphicon-calendar"></i><div class="calendar-time"><div></div><i class="fa fa-clock-o glyphicon glyphicon-time"></i></div></div><div class="calendar-table"></div></div><div class="calendar right"><div class="daterangepicker_input"><input class="input-mini form-control" type="text" name="daterangepicker_end" value="" /><i class="fa fa-calendar glyphicon glyphicon-calendar"></i><div class="calendar-time"><div></div><i class="fa fa-clock-o glyphicon glyphicon-time"></i></div></div><div class="calendar-table"></div></div><div class="ranges"><div class="range_inputs"><button class="applyBtn" disabled="disabled" type="button"></button> <button class="cancelBtn" type="button"></button></div></div></div>'),this.parentEl=e(n.parentEl&&e(n.parentEl).length?n.parentEl:this.parentEl),this.container=e(n.template).appendTo(this.parentEl),"object"==typeof n.locale&&("string"==typeof n.locale.direction&&(this.locale.direction=n.locale.direction),"string"==typeof n.locale.format&&(this.locale.format=n.locale.format),"string"==typeof n.locale.separator&&(this.locale.separator=n.locale.separator),"object"==typeof n.locale.daysOfWeek&&(this.locale.daysOfWeek=n.locale.daysOfWeek.slice()),"object"==typeof n.locale.monthNames&&(this.locale.monthNames=n.locale.monthNames.slice()),"number"==typeof n.locale.firstDay&&(this.locale.firstDay=n.locale.firstDay),"string"==typeof n.locale.applyLabel&&(this.locale.applyLabel=n.locale.applyLabel),"string"==typeof n.locale.cancelLabel&&(this.locale.cancelLabel=n.locale.cancelLabel),"string"==typeof n.locale.weekLabel&&(this.locale.weekLabel=n.locale.weekLabel),"string"==typeof n.locale.customRangeLabel)){var a=document.createElement("textarea");a.innerHTML=n.locale.customRangeLabel;var s=a.value;this.locale.customRangeLabel=s}if(this.container.addClass(this.locale.direction),"string"==typeof n.startDate&&(this.startDate=t(n.startDate,this.locale.format)),"string"==typeof n.endDate&&(this.endDate=t(n.endDate,this.locale.format)),"string"==typeof n.minDate&&(this.minDate=t(n.minDate,this.locale.format)),"string"==typeof n.maxDate&&(this.maxDate=t(n.maxDate,this.locale.format)),"object"==typeof n.startDate&&(this.startDate=t(n.startDate)),"object"==typeof n.endDate&&(this.endDate=t(n.endDate)),"object"==typeof n.minDate&&(this.minDate=t(n.minDate)),"object"==typeof n.maxDate&&(this.maxDate=t(n.maxDate)),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),"string"==typeof n.applyClass&&(this.applyClass=n.applyClass),"string"==typeof n.cancelClass&&(this.cancelClass=n.cancelClass),"object"==typeof n.dateLimit&&(this.dateLimit=n.dateLimit),"string"==typeof n.opens&&(this.opens=n.opens),"string"==typeof n.drops&&(this.drops=n.drops),"boolean"==typeof n.showWeekNumbers&&(this.showWeekNumbers=n.showWeekNumbers),"boolean"==typeof n.showISOWeekNumbers&&(this.showISOWeekNumbers=n.showISOWeekNumbers),"string"==typeof n.buttonClasses&&(this.buttonClasses=n.buttonClasses),"object"==typeof n.buttonClasses&&(this.buttonClasses=n.buttonClasses.join(" ")),"boolean"==typeof n.showDropdowns&&(this.showDropdowns=n.showDropdowns),"boolean"==typeof n.showCustomRangeLabel&&(this.showCustomRangeLabel=n.showCustomRangeLabel),"boolean"==typeof n.singleDatePicker&&(this.singleDatePicker=n.singleDatePicker,this.singleDatePicker&&(this.endDate=this.startDate.clone())),"boolean"==typeof n.timePicker&&(this.timePicker=n.timePicker),"boolean"==typeof n.timePickerSeconds&&(this.timePickerSeconds=n.timePickerSeconds),"number"==typeof n.timePickerIncrement&&(this.timePickerIncrement=n.timePickerIncrement),"boolean"==typeof n.timePicker24Hour&&(this.timePicker24Hour=n.timePicker24Hour),"boolean"==typeof n.autoApply&&(this.autoApply=n.autoApply),"boolean"==typeof n.autoUpdateInput&&(this.autoUpdateInput=n.autoUpdateInput),"boolean"==typeof n.linkedCalendars&&(this.linkedCalendars=n.linkedCalendars),"function"==typeof n.isInvalidDate&&(this.isInvalidDate=n.isInvalidDate),"function"==typeof n.isCustomDate&&(this.isCustomDate=n.isCustomDate),"boolean"==typeof n.alwaysShowCalendars&&(this.alwaysShowCalendars=n.alwaysShowCalendars),0!=this.locale.firstDay)for(var r=this.locale.firstDay;r>0;)this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift()),r--;var l,c,d;if(void 0===n.startDate&&void 0===n.endDate&&e(this.element).is("input[type=text]")){var u=e(this.element).val(),h=u.split(this.locale.separator);l=c=null,2==h.length?(l=t(h[0],this.locale.format),c=t(h[1],this.locale.format)):this.singleDatePicker&&""!==u&&(l=t(u,this.locale.format),c=t(u,this.locale.format)),null!==l&&null!==c&&(this.setStartDate(l),this.setEndDate(c))}if("object"==typeof n.ranges){for(d in n.ranges){l="string"==typeof n.ranges[d][0]?t(n.ranges[d][0],this.locale.format):t(n.ranges[d][0]),c="string"==typeof n.ranges[d][1]?t(n.ranges[d][1],this.locale.format):t(n.ranges[d][1]),this.minDate&&l.isBefore(this.minDate)&&(l=this.minDate.clone());var p=this.maxDate;if(this.dateLimit&&p&&l.clone().add(this.dateLimit).isAfter(p)&&(p=l.clone().add(this.dateLimit)),p&&c.isAfter(p)&&(c=p.clone()),!(this.minDate&&c.isBefore(this.minDate,this.timepicker?"minute":"day")||p&&l.isAfter(p,this.timepicker?"minute":"day"))){var a=document.createElement("textarea");a.innerHTML=d;var s=a.value;this.ranges[s]=[l,c]}}var f="<ul>";for(d in this.ranges)f+='<li data-range-key="'+d+'">'+d+"</li>";this.showCustomRangeLabel&&(f+='<li data-range-key="'+this.locale.customRangeLabel+'">'+this.locale.customRangeLabel+"</li>"),f+="</ul>",this.container.find(".ranges").prepend(f)}"function"==typeof o&&(this.callback=o),this.timePicker||(this.startDate=this.startDate.startOf("day"),this.endDate=this.endDate.endOf("day"),this.container.find(".calendar-time").hide()),this.timePicker&&this.autoApply&&(this.autoApply=!1),this.autoApply&&"object"!=typeof n.ranges?this.container.find(".ranges").hide():this.autoApply&&this.container.find(".applyBtn, .cancelBtn").addClass("hide"),this.singleDatePicker&&(this.container.addClass("single"),this.container.find(".calendar.left").addClass("single"),this.container.find(".calendar.left").show(),this.container.find(".calendar.right").hide(),this.container.find(".daterangepicker_input input, .daterangepicker_input > i").hide(),this.timePicker?this.container.find(".ranges ul").hide():this.container.find(".ranges").hide()),(void 0===n.ranges&&!this.singleDatePicker||this.alwaysShowCalendars)&&this.container.addClass("show-calendar"),this.container.addClass("opens"+this.opens),void 0!==n.ranges&&"right"==this.opens&&this.container.find(".ranges").prependTo(this.container.find(".calendar.left").parent()),this.container.find(".applyBtn, .cancelBtn").addClass(this.buttonClasses),this.applyClass.length&&this.container.find(".applyBtn").addClass(this.applyClass),this.cancelClass.length&&this.container.find(".cancelBtn").addClass(this.cancelClass),this.container.find(".applyBtn").html(this.locale.applyLabel),this.container.find(".cancelBtn").html(this.locale.cancelLabel),this.container.find(".calendar").on("click.daterangepicker",".prev",e.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",e.proxy(this.clickNext,this)).on("mousedown.daterangepicker","td.available",e.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",e.proxy(this.hoverDate,this)).on("mouseleave.daterangepicker","td.available",e.proxy(this.updateFormInputs,this)).on("change.daterangepicker","select.yearselect",e.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.monthselect",e.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.secondselect,select.ampmselect",e.proxy(this.timeChanged,this)).on("click.daterangepicker",".daterangepicker_input input",e.proxy(this.showCalendars,this)).on("focus.daterangepicker",".daterangepicker_input input",e.proxy(this.formInputsFocused,this)).on("blur.daterangepicker",".daterangepicker_input input",e.proxy(this.formInputsBlurred,this)).on("change.daterangepicker",".daterangepicker_input input",e.proxy(this.formInputsChanged,this)).on("keydown.daterangepicker",".daterangepicker_input input",e.proxy(this.formInputsKeydown,this)),this.container.find(".ranges").on("click.daterangepicker","button.applyBtn",e.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",e.proxy(this.clickCancel,this)).on("click.daterangepicker","li",e.proxy(this.clickRange,this)).on("mouseenter.daterangepicker","li",e.proxy(this.hoverRange,this)).on("mouseleave.daterangepicker","li",e.proxy(this.updateFormInputs,this)),this.element.is("input")||this.element.is("button")?this.element.on({"click.daterangepicker":e.proxy(this.show,this),"focus.daterangepicker":e.proxy(this.show,this),"keyup.daterangepicker":e.proxy(this.elementChanged,this),"keydown.daterangepicker":e.proxy(this.keydown,this)}):(this.element.on("click.daterangepicker",e.proxy(this.toggle,this)),this.element.on("keydown.daterangepicker",e.proxy(this.toggle,this))),this.element.is("input")&&!this.singleDatePicker&&this.autoUpdateInput?(this.element.val(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.element.trigger("change")):this.element.is("input")&&this.autoUpdateInput&&(this.element.val(this.startDate.format(this.locale.format)),this.element.trigger("change"))};return i.prototype={constructor:i,setStartDate:function(e){"string"==typeof e&&(this.startDate=t(e,this.locale.format)),"object"==typeof e&&(this.startDate=t(e)),this.timePicker||(this.startDate=this.startDate.startOf("day")),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.maxDate&&this.startDate.isAfter(this.maxDate)&&(this.startDate=this.maxDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.floor(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.isShowing||this.updateElement(),this.updateMonthsInView()},setEndDate:function(e){"string"==typeof e&&(this.endDate=t(e,this.locale.format)),"object"==typeof e&&(this.endDate=t(e)),this.timePicker||(this.endDate=this.endDate.add(1,"d").startOf("day").subtract(1,"second")),this.timePicker&&this.timePickerIncrement&&this.endDate.minute(Math.round(this.endDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.endDate.isBefore(this.startDate)&&(this.endDate=this.startDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),this.dateLimit&&this.startDate.clone().add(this.dateLimit).isBefore(this.endDate)&&(this.endDate=this.startDate.clone().add(this.dateLimit)),this.previousRightTime=this.endDate.clone(),this.isShowing||this.updateElement(),this.updateMonthsInView()},isInvalidDate:function(){return!1},isCustomDate:function(){return!1},updateView:function(){this.timePicker&&(this.renderTimePicker("left"),this.renderTimePicker("right"),this.endDate?this.container.find(".right .calendar-time select").removeAttr("disabled").removeClass("disabled"):this.container.find(".right .calendar-time select").attr("disabled","disabled").addClass("disabled")),this.endDate?(this.container.find('input[name="daterangepicker_end"]').removeClass("active"),this.container.find('input[name="daterangepicker_start"]').addClass("active")):(this.container.find('input[name="daterangepicker_end"]').addClass("active"),this.container.find('input[name="daterangepicker_start"]').removeClass("active")),this.updateMonthsInView(),this.updateCalendars(),this.updateFormInputs()},updateMonthsInView:function(){if(this.endDate){if(!this.singleDatePicker&&this.leftCalendar.month&&this.rightCalendar.month&&(this.startDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.startDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM"))&&(this.endDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.endDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM")))return;this.leftCalendar.month=this.startDate.clone().date(2),this.linkedCalendars||this.endDate.month()==this.startDate.month()&&this.endDate.year()==this.startDate.year()?this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"):this.rightCalendar.month=this.endDate.clone().date(2)}else this.leftCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&this.rightCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&(this.leftCalendar.month=this.startDate.clone().date(2),this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"));this.maxDate&&this.linkedCalendars&&!this.singleDatePicker&&this.rightCalendar.month>this.maxDate&&(this.rightCalendar.month=this.maxDate.clone().date(2),this.leftCalendar.month=this.maxDate.clone().date(2).subtract(1,"month"))},updateCalendars:function(){if(this.timePicker){var t,e,i;if(this.endDate){if(t=parseInt(this.container.find(".left .hourselect").val(),10),e=parseInt(this.container.find(".left .minuteselect").val(),10),i=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,!this.timePicker24Hour){var n=this.container.find(".left .ampmselect").val();"PM"===n&&t<12&&(t+=12),"AM"===n&&12===t&&(t=0)}}else if(t=parseInt(this.container.find(".right .hourselect").val(),10),e=parseInt(this.container.find(".right .minuteselect").val(),10),i=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,!this.timePicker24Hour){var n=this.container.find(".right .ampmselect").val();"PM"===n&&t<12&&(t+=12),"AM"===n&&12===t&&(t=0)}this.leftCalendar.month.hour(t).minute(e).second(i),this.rightCalendar.month.hour(t).minute(e).second(i)}this.renderCalendar("left"),this.renderCalendar("right"),this.container.find(".ranges li").removeClass("active"),null!=this.endDate&&this.calculateChosenLabel()},renderCalendar:function(i){var n="left"==i?this.leftCalendar:this.rightCalendar,o=n.month.month(),a=n.month.year(),s=n.month.hour(),r=n.month.minute(),l=n.month.second(),c=t([a,o]).daysInMonth(),d=t([a,o,1]),u=t([a,o,c]),h=t(d).subtract(1,"month").month(),p=t(d).subtract(1,"month").year(),f=t([p,h]).daysInMonth(),m=d.day(),n=[];n.firstDay=d,n.lastDay=u;for(var g=0;g<6;g++)n[g]=[];var v=f-m+this.locale.firstDay+1;v>f&&(v-=7),m==this.locale.firstDay&&(v=f-6);for(var y,b,x=t([p,h,v,12,r,l]),g=0,y=0,b=0;g<42;g++,y++,x=t(x).add(24,"hour"))g>0&&y%7==0&&(y=0,b++),n[b][y]=x.clone().hour(s).minute(r).second(l),x.hour(12),this.minDate&&n[b][y].format("YYYY-MM-DD")==this.minDate.format("YYYY-MM-DD")&&n[b][y].isBefore(this.minDate)&&"left"==i&&(n[b][y]=this.minDate.clone()),this.maxDate&&n[b][y].format("YYYY-MM-DD")==this.maxDate.format("YYYY-MM-DD")&&n[b][y].isAfter(this.maxDate)&&"right"==i&&(n[b][y]=this.maxDate.clone());"left"==i?this.leftCalendar.calendar=n:this.rightCalendar.calendar=n;var w="left"==i?this.minDate:this.startDate,k=this.maxDate,_=("left"==i?this.startDate:this.endDate,"ltr"==this.locale.direction?{left:"chevron-left",right:"chevron-right"}:{left:"chevron-right",right:"chevron-left"}),C='<table class="table-condensed">';C+="<thead>",C+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(C+="<th></th>"),w&&!w.isBefore(n.firstDay)||this.linkedCalendars&&"left"!=i?C+="<th></th>":C+='<th class="prev available"><i class="fa fa-'+_.left+" glyphicon glyphicon-"+_.left+'"></i></th>';var S=this.locale.monthNames[n[1][1].month()]+n[1][1].format(" YYYY");if(this.showDropdowns){for(var T=n[1][1].month(),D=n[1][1].year(),$=k&&k.year()||D+5,E=w&&w.year()||D-50,A=D==E,O=D==$,F='<select class="monthselect">',P=0;P<12;P++)(!A||P>=w.month())&&(!O||P<=k.month())?F+="<option value='"+P+"'"+(P===T?" selected='selected'":"")+">"+this.locale.monthNames[P]+"</option>":F+="<option value='"+P+"'"+(P===T?" selected='selected'":"")+" disabled='disabled'>"+this.locale.monthNames[P]+"</option>";F+="</select>";for(var M='<select class="yearselect">',L=E;L<=$;L++)M+='<option value="'+L+'"'+(L===D?' selected="selected"':"")+">"+L+"</option>";M+="</select>",S=F+M}if(C+='<th colspan="5" class="month">'+S+"</th>",k&&!k.isAfter(n.lastDay)||this.linkedCalendars&&"right"!=i&&!this.singleDatePicker?C+="<th></th>":C+='<th class="next available"><i class="fa fa-'+_.right+" glyphicon glyphicon-"+_.right+'"></i></th>',C+="</tr>",C+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(C+='<th class="week">'+this.locale.weekLabel+"</th>"),e.each(this.locale.daysOfWeek,function(t,e){C+="<th>"+e+"</th>"}),C+="</tr>",C+="</thead>",C+="<tbody>",null==this.endDate&&this.dateLimit){var I=this.startDate.clone().add(this.dateLimit).endOf("day");k&&!I.isBefore(k)||(k=I)}for(var b=0;b<6;b++){C+="<tr>",this.showWeekNumbers?C+='<td class="week">'+n[b][0].week()+"</td>":this.showISOWeekNumbers&&(C+='<td class="week">'+n[b][0].isoWeek()+"</td>");for(var y=0;y<7;y++){var N=[];n[b][y].isSame(new Date,"day")&&N.push("today"),n[b][y].isoWeekday()>5&&N.push("weekend"),n[b][y].month()!=n[1][1].month()&&N.push("off"),this.minDate&&n[b][y].isBefore(this.minDate,"day")&&N.push("off","disabled"),k&&n[b][y].isAfter(k,"day")&&N.push("off","disabled"),this.isInvalidDate(n[b][y])&&N.push("off","disabled"),n[b][y].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")&&N.push("active","start-date"),null!=this.endDate&&n[b][y].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")&&N.push("active","end-date"),null!=this.endDate&&n[b][y]>this.startDate&&n[b][y]<this.endDate&&N.push("in-range");var R=this.isCustomDate(n[b][y]);!1!==R&&("string"==typeof R?N.push(R):Array.prototype.push.apply(N,R));for(var j="",Y=!1,g=0;g<N.length;g++)j+=N[g]+" ","disabled"==N[g]&&(Y=!0);Y||(j+="available"),C+='<td class="'+j.replace(/^\s+|\s+$/g,"")+'" data-title="r'+b+"c"+y+'">'+n[b][y].date()+"</td>"}C+="</tr>"}C+="</tbody>",C+="</table>",this.container.find(".calendar."+i+" .calendar-table").html(C)},renderTimePicker:function(t){if("right"!=t||this.endDate){var e,i,n,o=this.maxDate;if(!this.dateLimit||this.maxDate&&!this.startDate.clone().add(this.dateLimit).isAfter(this.maxDate)||(o=this.startDate.clone().add(this.dateLimit)),"left"==t)i=this.startDate.clone(),n=this.minDate;else if("right"==t){i=this.endDate.clone(),n=this.startDate;var a=this.container.find(".calendar.right .calendar-time div");if(""!=a.html()&&(i.hour(a.find(".hourselect option:selected").val()||i.hour()),i.minute(a.find(".minuteselect option:selected").val()||i.minute()),i.second(a.find(".secondselect option:selected").val()||i.second()),!this.timePicker24Hour)){var s=a.find(".ampmselect option:selected").val();"PM"===s&&i.hour()<12&&i.hour(i.hour()+12),"AM"===s&&12===i.hour()&&i.hour(0)}i.isBefore(this.startDate)&&(i=this.startDate.clone()),o&&i.isAfter(o)&&(i=o.clone())}e='<select class="hourselect">';for(var r=this.timePicker24Hour?0:1,l=this.timePicker24Hour?23:12,c=r;c<=l;c++){var d=c;this.timePicker24Hour||(d=i.hour()>=12?12==c?12:c+12:12==c?0:c);var u=i.clone().hour(d),h=!1;n&&u.minute(59).isBefore(n)&&(h=!0),o&&u.minute(0).isAfter(o)&&(h=!0),d!=i.hour()||h?e+=h?'<option value="'+c+'" disabled="disabled" class="disabled">'+c+"</option>":'<option value="'+c+'">'+c+"</option>":e+='<option value="'+c+'" selected="selected">'+c+"</option>"}e+="</select> ",e+=': <select class="minuteselect">';for(var c=0;c<60;c+=this.timePickerIncrement){var p=c<10?"0"+c:c,u=i.clone().minute(c),h=!1;n&&u.second(59).isBefore(n)&&(h=!0),o&&u.second(0).isAfter(o)&&(h=!0),i.minute()!=c||h?e+=h?'<option value="'+c+'" disabled="disabled" class="disabled">'+p+"</option>":'<option value="'+c+'">'+p+"</option>":e+='<option value="'+c+'" selected="selected">'+p+"</option>"}if(e+="</select> ",this.timePickerSeconds){e+=': <select class="secondselect">';for(var c=0;c<60;c++){var p=c<10?"0"+c:c,u=i.clone().second(c),h=!1;n&&u.isBefore(n)&&(h=!0),o&&u.isAfter(o)&&(h=!0),i.second()!=c||h?e+=h?'<option value="'+c+'" disabled="disabled" class="disabled">'+p+"</option>":'<option value="'+c+'">'+p+"</option>":e+='<option value="'+c+'" selected="selected">'+p+"</option>"}e+="</select> "}if(!this.timePicker24Hour){e+='<select class="ampmselect">';var f="",m="";n&&i.clone().hour(12).minute(0).second(0).isBefore(n)&&(f=' disabled="disabled" class="disabled"'),o&&i.clone().hour(0).minute(0).second(0).isAfter(o)&&(m=' disabled="disabled" class="disabled"'),i.hour()>=12?e+='<option value="AM"'+f+'>AM</option><option value="PM" selected="selected"'+m+">PM</option>":e+='<option value="AM" selected="selected"'+f+'>AM</option><option value="PM"'+m+">PM</option>",e+="</select>"}this.container.find(".calendar."+t+" .calendar-time div").html(e)}},updateFormInputs:function(){this.container.find("input[name=daterangepicker_start]").is(":focus")||this.container.find("input[name=daterangepicker_end]").is(":focus")||(this.container.find("input[name=daterangepicker_start]").val(this.startDate.format(this.locale.format)),this.endDate&&this.container.find("input[name=daterangepicker_end]").val(this.endDate.format(this.locale.format)),this.singleDatePicker||this.endDate&&(this.startDate.isBefore(this.endDate)||this.startDate.isSame(this.endDate))?this.container.find("button.applyBtn").removeAttr("disabled"):this.container.find("button.applyBtn").attr("disabled","disabled"))},move:function(){var t,i={top:0,left:0},n=e(window).width();this.parentEl.is("body")||(i={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()},n=this.parentEl[0].clientWidth+this.parentEl.offset().left),t="up"==this.drops?this.element.offset().top-this.container.outerHeight()-i.top:this.element.offset().top+this.element.outerHeight()-i.top,this.container["up"==this.drops?"addClass":"removeClass"]("dropup"),"left"==this.opens?(this.container.css({top:t,right:n-this.element.offset().left-this.element.outerWidth(),left:"auto"}),this.container.offset().left<0&&this.container.css({right:"auto",left:9})):"center"==this.opens?(this.container.css({top:t,left:this.element.offset().left-i.left+this.element.outerWidth()/2-this.container.outerWidth()/2,right:"auto"}),this.container.offset().left<0&&this.container.css({right:"auto",left:9})):(this.container.css({top:t,left:this.element.offset().left-i.left,right:"auto"}),this.container.offset().left+this.container.outerWidth()>e(window).width()&&this.container.css({left:"auto",right:0}))},show:function(t){this.isShowing||(this._outsideClickProxy=e.proxy(function(t){this.outsideClick(t)},this),
e(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("touchend.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy),e(window).on("resize.daterangepicker",e.proxy(function(t){this.move(t)},this)),this.oldStartDate=this.startDate.clone(),this.oldEndDate=this.endDate.clone(),this.previousRightTime=this.endDate.clone(),this.updateView(),this.container.show(),this.move(),this.element.trigger("show.daterangepicker",this),this.isShowing=!0)},hide:function(t){this.isShowing&&(this.endDate||(this.startDate=this.oldStartDate.clone(),this.endDate=this.oldEndDate.clone()),this.startDate.isSame(this.oldStartDate)&&this.endDate.isSame(this.oldEndDate)||this.callback(this.startDate,this.endDate,this.chosenLabel),this.updateElement(),e(document).off(".daterangepicker"),e(window).off(".daterangepicker"),this.container.hide(),this.element.trigger("hide.daterangepicker",this),this.isShowing=!1)},toggle:function(t){this.isShowing?this.hide():this.show()},outsideClick:function(t){var i=e(t.target);"focusin"==t.type||i.closest(this.element).length||i.closest(this.container).length||i.closest(".calendar-table").length||(this.hide(),this.element.trigger("outsideClick.daterangepicker",this))},showCalendars:function(){this.container.addClass("show-calendar"),this.move(),this.element.trigger("showCalendar.daterangepicker",this)},hideCalendars:function(){this.container.removeClass("show-calendar"),this.element.trigger("hideCalendar.daterangepicker",this)},hoverRange:function(t){if(!this.container.find("input[name=daterangepicker_start]").is(":focus")&&!this.container.find("input[name=daterangepicker_end]").is(":focus")){var e=t.target.getAttribute("data-range-key");if(e==this.locale.customRangeLabel)this.updateView();else{var i=this.ranges[e];this.container.find("input[name=daterangepicker_start]").val(i[0].format(this.locale.format)),this.container.find("input[name=daterangepicker_end]").val(i[1].format(this.locale.format))}}},clickRange:function(t){var e=t.target.getAttribute("data-range-key");if(this.chosenLabel=e,e==this.locale.customRangeLabel)this.showCalendars();else{var i=this.ranges[e];this.startDate=i[0],this.endDate=i[1],this.timePicker||(this.startDate.startOf("day"),this.endDate.endOf("day")),this.alwaysShowCalendars||this.hideCalendars(),this.clickApply()}},clickPrev:function(t){e(t.target).parents(".calendar").hasClass("left")?(this.leftCalendar.month.subtract(1,"month"),this.linkedCalendars&&this.rightCalendar.month.subtract(1,"month")):this.rightCalendar.month.subtract(1,"month"),this.updateCalendars()},clickNext:function(t){e(t.target).parents(".calendar").hasClass("left")?this.leftCalendar.month.add(1,"month"):(this.rightCalendar.month.add(1,"month"),this.linkedCalendars&&this.leftCalendar.month.add(1,"month")),this.updateCalendars()},hoverDate:function(t){if(e(t.target).hasClass("available")){var i=e(t.target).attr("data-title"),n=i.substr(1,1),o=i.substr(3,1),a=e(t.target).parents(".calendar"),s=a.hasClass("left")?this.leftCalendar.calendar[n][o]:this.rightCalendar.calendar[n][o];this.endDate&&!this.container.find("input[name=daterangepicker_start]").is(":focus")?this.container.find("input[name=daterangepicker_start]").val(s.format(this.locale.format)):this.endDate||this.container.find("input[name=daterangepicker_end]").is(":focus")||this.container.find("input[name=daterangepicker_end]").val(s.format(this.locale.format));var r=this.leftCalendar,l=this.rightCalendar,c=this.startDate;this.endDate||this.container.find(".calendar tbody td").each(function(t,i){if(!e(i).hasClass("week")){var n=e(i).attr("data-title"),o=n.substr(1,1),a=n.substr(3,1),d=e(i).parents(".calendar"),u=d.hasClass("left")?r.calendar[o][a]:l.calendar[o][a];u.isAfter(c)&&u.isBefore(s)||u.isSame(s,"day")?e(i).addClass("in-range"):e(i).removeClass("in-range")}})}},clickDate:function(t){if(e(t.target).hasClass("available")){var i=e(t.target).attr("data-title"),n=i.substr(1,1),o=i.substr(3,1),a=e(t.target).parents(".calendar"),s=a.hasClass("left")?this.leftCalendar.calendar[n][o]:this.rightCalendar.calendar[n][o];if(this.endDate||s.isBefore(this.startDate,"day")){if(this.timePicker){var r=parseInt(this.container.find(".left .hourselect").val(),10);if(!this.timePicker24Hour){var l=this.container.find(".left .ampmselect").val();"PM"===l&&r<12&&(r+=12),"AM"===l&&12===r&&(r=0)}var c=parseInt(this.container.find(".left .minuteselect").val(),10),d=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0;s=s.clone().hour(r).minute(c).second(d)}this.endDate=null,this.setStartDate(s.clone())}else if(!this.endDate&&s.isBefore(this.startDate))this.setEndDate(this.startDate.clone());else{if(this.timePicker){var r=parseInt(this.container.find(".right .hourselect").val(),10);if(!this.timePicker24Hour){var l=this.container.find(".right .ampmselect").val();"PM"===l&&r<12&&(r+=12),"AM"===l&&12===r&&(r=0)}var c=parseInt(this.container.find(".right .minuteselect").val(),10),d=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0;s=s.clone().hour(r).minute(c).second(d)}this.setEndDate(s.clone()),this.autoApply&&(this.calculateChosenLabel(),this.clickApply())}this.singleDatePicker&&(this.setEndDate(this.startDate),this.timePicker||this.clickApply()),this.updateView(),t.stopPropagation()}},calculateChosenLabel:function(){var t=!0,e=0;for(var i in this.ranges){if(this.timePicker){var n=this.timePickerSeconds?"YYYY-MM-DD hh:mm:ss":"YYYY-MM-DD hh:mm";if(this.startDate.format(n)==this.ranges[i][0].format(n)&&this.endDate.format(n)==this.ranges[i][1].format(n)){t=!1,this.chosenLabel=this.container.find(".ranges li:eq("+e+")").addClass("active").html();break}}else if(this.startDate.format("YYYY-MM-DD")==this.ranges[i][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[i][1].format("YYYY-MM-DD")){t=!1,this.chosenLabel=this.container.find(".ranges li:eq("+e+")").addClass("active").html();break}e++}t&&(this.showCustomRangeLabel?this.chosenLabel=this.container.find(".ranges li:last").addClass("active").html():this.chosenLabel=null,this.showCalendars())},clickApply:function(t){this.hide(),this.element.trigger("apply.daterangepicker",this)},clickCancel:function(t){this.startDate=this.oldStartDate,this.endDate=this.oldEndDate,this.hide(),this.element.trigger("cancel.daterangepicker",this)},monthOrYearChanged:function(t){var i=e(t.target).closest(".calendar").hasClass("left"),n=i?"left":"right",o=this.container.find(".calendar."+n),a=parseInt(o.find(".monthselect").val(),10),s=o.find(".yearselect").val();i||(s<this.startDate.year()||s==this.startDate.year()&&a<this.startDate.month())&&(a=this.startDate.month(),s=this.startDate.year()),this.minDate&&(s<this.minDate.year()||s==this.minDate.year()&&a<this.minDate.month())&&(a=this.minDate.month(),s=this.minDate.year()),this.maxDate&&(s>this.maxDate.year()||s==this.maxDate.year()&&a>this.maxDate.month())&&(a=this.maxDate.month(),s=this.maxDate.year()),i?(this.leftCalendar.month.month(a).year(s),this.linkedCalendars&&(this.rightCalendar.month=this.leftCalendar.month.clone().add(1,"month"))):(this.rightCalendar.month.month(a).year(s),this.linkedCalendars&&(this.leftCalendar.month=this.rightCalendar.month.clone().subtract(1,"month"))),this.updateCalendars()},timeChanged:function(t){var i=e(t.target).closest(".calendar"),n=i.hasClass("left"),o=parseInt(i.find(".hourselect").val(),10),a=parseInt(i.find(".minuteselect").val(),10),s=this.timePickerSeconds?parseInt(i.find(".secondselect").val(),10):0;if(!this.timePicker24Hour){var r=i.find(".ampmselect").val();"PM"===r&&o<12&&(o+=12),"AM"===r&&12===o&&(o=0)}if(n){var l=this.startDate.clone();l.hour(o),l.minute(a),l.second(s),this.setStartDate(l),this.singleDatePicker?this.endDate=this.startDate.clone():this.endDate&&this.endDate.format("YYYY-MM-DD")==l.format("YYYY-MM-DD")&&this.endDate.isBefore(l)&&this.setEndDate(l.clone())}else if(this.endDate){var c=this.endDate.clone();c.hour(o),c.minute(a),c.second(s),this.setEndDate(c)}this.updateCalendars(),this.updateFormInputs(),this.renderTimePicker("left"),this.renderTimePicker("right")},formInputsChanged:function(i){var n=e(i.target).closest(".calendar").hasClass("right"),o=t(this.container.find('input[name="daterangepicker_start"]').val(),this.locale.format),a=t(this.container.find('input[name="daterangepicker_end"]').val(),this.locale.format);o.isValid()&&a.isValid()&&(n&&a.isBefore(o)&&(o=a.clone()),this.setStartDate(o),this.setEndDate(a),n?this.container.find('input[name="daterangepicker_start"]').val(this.startDate.format(this.locale.format)):this.container.find('input[name="daterangepicker_end"]').val(this.endDate.format(this.locale.format))),this.updateView()},formInputsFocused:function(t){this.container.find('input[name="daterangepicker_start"], input[name="daterangepicker_end"]').removeClass("active"),e(t.target).addClass("active"),e(t.target).closest(".calendar").hasClass("right")&&(this.endDate=null,this.setStartDate(this.startDate.clone()),this.updateView())},formInputsBlurred:function(e){if(!this.endDate){var i=this.container.find('input[name="daterangepicker_end"]').val(),n=t(i,this.locale.format);n.isValid()&&(this.setEndDate(n),this.updateView())}},formInputsKeydown:function(t){13===t.keyCode&&(t.preventDefault(),this.formInputsChanged(t))},elementChanged:function(){if(this.element.is("input")&&this.element.val().length){var e=this.element.val().split(this.locale.separator),i=null,n=null;2===e.length&&(i=t(e[0],this.locale.format),n=t(e[1],this.locale.format)),(this.singleDatePicker||null===i||null===n)&&(i=t(this.element.val(),this.locale.format),n=i),i.isValid()&&n.isValid()&&(this.setStartDate(i),this.setEndDate(n),this.updateView())}},keydown:function(t){9!==t.keyCode&&13!==t.keyCode||this.hide(),27===t.keyCode&&(t.preventDefault(),t.stopPropagation(),this.hide())},updateElement:function(){this.element.is("input")&&!this.singleDatePicker&&this.autoUpdateInput?(this.element.val(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.element.trigger("change")):this.element.is("input")&&this.autoUpdateInput&&(this.element.val(this.startDate.format(this.locale.format)),this.element.trigger("change"))},remove:function(){this.container.remove(),this.element.off(".daterangepicker"),this.element.removeData()}},e.fn.daterangepicker=function(t,n){var o=e.extend(!0,{},e.fn.daterangepicker.defaultOptions,t);return this.each(function(){var t=e(this);t.data("daterangepicker")&&t.data("daterangepicker").remove(),t.data("daterangepicker",new i(t,o,n))}),this},i});