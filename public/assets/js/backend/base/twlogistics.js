define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'base/twlogistics/index' + location.search,
                    add_url: 'base/twlogistics/add',
                    edit_url: 'base/twlogistics/edit',
                    del_url: 'base/twlogistics/del',
                    multi_url: 'base/twlogistics/multi',
                    import_url: 'base/twlogistics/import',
                    table: 'tw_logistics',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        // {field: 'type', title: __('Type')},
                        {field: 'dict.name', title: __('Type'), operate: 'LIKE'},
                        {field: 'textual', title: __('Textual'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'scale', title: __('Scale')},
                        {field: 'single', title: __('Single')},
                        {field: 'two', title: __('Two')},
                        {field: 'total', title: __('Total')},
                        {field: 'count_value', title: __('Count_value')},
                        {field: 'limit_value', title: __('Limit_value')},
                        {field: 'over_price', title: __('Over_price')},
                        {field: 'base_money', title: __('Base_money')},
                        {field: 'status', title: __('Status')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
