define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'base/warehousetransport/index' + location.search,
                    add_url: 'base/warehousetransport/add',
                    edit_url: 'base/warehousetransport/edit',
                    del_url: 'base/warehousetransport/del',
                    multi_url: 'base/warehousetransport/multi',
                    import_url: 'base/warehousetransport/import',
                    table: 'warehouse_transport',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        // {field: 'wh_id', title: __('Wh_id')},
                        {field: 'warehouse.title', title: __('Warehouse.title'), operate: 'LIKE'},
                        {field: 'dict.name', title: __('Dict.name'), operate: 'LIKE'},
                        // {field: 'type', title: __('Type')},
                        {field: 'status', title: __('Status')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
