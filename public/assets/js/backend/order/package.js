define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order/package/index' + location.search,
                    // add_url: 'order/package/add',
                    edit_url: 'order/package/edit',
                    // del_url: 'order/package/del',
                    multi_url: 'order/package/multi',
                    import_url: 'order/package/import',
                    table: 'package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status1'),"2":__('Status2'),"3":__('Status3'),"4":__('Status4'),"5":__('Status5')} , formatter: Table.api.formatter.status},
                        {field: 'user.username', title: __('User.username')},
                        {field: 'entrust_no', title: __('Entrust_no'), operate: 'LIKE'},
                        {field: 'waybill', title: __('Waybill'), operate: 'LIKE'},
                        {field: 'wbill_name', title: __('Wbill_name'), operate: 'LIKE'},
                        {field: 'goods_name', title: __('Goods_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'goods_url', title: __('Goods_url'), operate: 'LIKE', formatter: Table.api.formatter.url},
                        {field: 'num', title: __('Num')},
                        {field: 'scale', title: __('Scale'), operate:'BETWEEN'},
                        {field: 'volume', title: __('Volume'), operate:'BETWEEN'},
                        {field: 'length', title: __('Length'), operate:'BETWEEN'},
                        {field: 'width', title: __('Width'), operate:'BETWEEN'},
                        {field: 'height', title: __('Height'), operate:'BETWEEN'},
                        {field: 'unit_price', title: __('Unit_price'), operate:'BETWEEN'},
                        {field: 'warehouse.title', title: __('Wh_id')},
                        {field: 'transport', title: __('Transport'), searchList: {"0":__('Transport0'),"15":__('Transport1'),"16":__('Transport2'),"17":__('Transport3')} , formatter: Table.api.formatter.status},
                        {field: 'goodstype.title', title: __('Goodstype')},
                        {field: 'uremarks', title: __('Uremarks'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'oremarks', title: __('Oremarks'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'arrivetime', title: __('Arrivetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'refuse', title: __('Refuse'), searchList: {"0":__('Refuse0'),"1":__('Refuse1')} , formatter: Table.api.formatter.status},
                        {
                            field: 'twlogistics.name', title: __('Twe_id'),operate:'BETWEEN'
                            // formatter:function(value,row,index){
                            //         return row.twlogistics.title + '-' + (row.twlogistics.type==20?'宅配':'超商取货');
                            // }
                        },
                        {field: 'bill_no', title: __('Bill_no'), operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'addser_id', title: __('Addser_id')},
                        {field: 'addservice.order_no', title: __('Addser_no')},
                        {field: 'addservice.tb_money', title: __('Addservice.tb_money')},
                        {field: 'addservice.bal_money', title: __('Addservice.bal_money')},
                        {field: 'addservice.actual_money', title: __('Addservice.actual_money')},
                        {field: 'addservice.order_status', title: __('Addservice.order_status'),
                            formatter:function(value,row,index){
                                if(row.addser_id==0){
                                    return '-';
                                }
                                return row.addservice.order_status==0?'未完成':'已完成';
                            }
                        },
                        {
                            field: 'operate', 
                            title: __('Operate'), 
                            table: table, 
                            events: Table.api.events.operate, 
                            buttons: [
                                {
                                    name: 'detail',
                                    text: '详情',
                                    title: __('详情'),
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    url: 'order/package/details',
									extend: 'data-area=\'["1200px","600px"]\'',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                },
                                {
                                    name: 'remarks',
                                    text: '备注',
                                    title: __('备注'),
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    url: 'order/package/remarks',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                }
                            ],

                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
